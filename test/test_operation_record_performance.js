/**
 * OperationRecord窗口性能测试
 * 测试批量更新和增量更新的性能表现
 */

// 模拟OperationRecord数据生成器
class MockOperationGenerator {
    constructor() {
        this.counter = 0;
    }

    generateOperation(type = 'mouse_click') {
        this.counter++;
        return {
            id: `test_${this.counter}`,
            timestamp: Date.now(),
            type: type,
            action: `操作 ${this.counter}`,
            target: {
                name: `控件 ${this.counter}`,
                type: 'button',
                position: { x: 100 + this.counter, y: 200 + this.counter }
            },
            details: {},
            widget_info: Math.random() > 0.3 ? {
                Name: `控件 ${this.counter}`,
                Rolename: 'button',
                ProcessName: 'test-app'
            } : null // 30%的操作没有控件绑定
        };
    }

    generateBatch(count) {
        const operations = [];
        for (let i = 0; i < count; i++) {
            operations.push(this.generateOperation());
        }
        return operations;
    }
}

// 性能测试类
class PerformanceTest {
    constructor() {
        this.generator = new MockOperationGenerator();
        this.results = [];
    }

    // 测试单次添加性能
    testSingleAddition(count = 100) {
        console.log(`\n=== 测试单次添加性能 (${count}次) ===`);
        
        const operations = this.generator.generateBatch(count);
        const startTime = performance.now();
        
        // 模拟单次添加的DOM操作
        for (const operation of operations) {
            this.simulateAddOperation(operation);
        }
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        console.log(`总耗时: ${duration.toFixed(2)}ms`);
        console.log(`平均每次: ${(duration / count).toFixed(2)}ms`);
        console.log(`操作频率: ${(count / duration * 1000).toFixed(0)} ops/s`);
        
        return { type: 'single', count, duration, avgTime: duration / count };
    }

    // 测试批量添加性能
    testBatchAddition(count = 100) {
        console.log(`\n=== 测试批量添加性能 (${count}次) ===`);
        
        const operations = this.generator.generateBatch(count);
        const startTime = performance.now();
        
        // 模拟批量添加的DOM操作
        this.simulateBatchAddOperations(operations);
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        console.log(`总耗时: ${duration.toFixed(2)}ms`);
        console.log(`平均每次: ${(duration / count).toFixed(2)}ms`);
        console.log(`操作频率: ${(count / duration * 1000).toFixed(0)} ops/s`);
        
        return { type: 'batch', count, duration, avgTime: duration / count };
    }

    // 测试高频添加场景
    testHighFrequencyAddition(opsPerSecond = 10, durationSeconds = 5) {
        console.log(`\n=== 测试高频添加场景 (${opsPerSecond} ops/s, ${durationSeconds}s) ===`);
        
        return new Promise((resolve) => {
            const interval = 1000 / opsPerSecond;
            const totalOps = opsPerSecond * durationSeconds;
            let addedOps = 0;
            const startTime = performance.now();
            
            const timer = setInterval(() => {
                if (addedOps >= totalOps) {
                    clearInterval(timer);
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    console.log(`实际耗时: ${duration.toFixed(2)}ms`);
                    console.log(`预期耗时: ${durationSeconds * 1000}ms`);
                    console.log(`性能开销: ${((duration - durationSeconds * 1000) / (durationSeconds * 1000) * 100).toFixed(1)}%`);
                    
                    resolve({ type: 'highFreq', count: totalOps, duration, overhead: duration - durationSeconds * 1000 });
                    return;
                }
                
                const operation = this.generator.generateOperation();
                this.simulateScheduledAddOperation(operation);
                addedOps++;
            }, interval);
        });
    }

    // 模拟单次添加操作（原有方式）
    simulateAddOperation(operation) {
        // 模拟DOM操作：创建元素、设置属性、添加到容器
        const element = this.createElement(operation);
        this.addToContainer(element);
        this.scrollToBottom();
    }

    // 模拟批量添加操作（优化方式）
    simulateBatchAddOperations(operations) {
        const elements = operations.map(op => this.createElement(op));
        this.addBatchToContainer(elements);
        this.scheduleScrollToBottom();
    }

    // 模拟调度添加操作（性能优化方式）
    simulateScheduledAddOperation(operation) {
        // 模拟批量更新策略
        if (!this.pendingOperations) {
            this.pendingOperations = [];
        }
        
        this.pendingOperations.push(operation);
        
        if (!this.updateScheduled) {
            this.updateScheduled = true;
            setTimeout(() => {
                this.processPendingOperations();
            }, 16); // 16ms延迟
        }
    }

    // 处理待处理的操作
    processPendingOperations() {
        if (this.pendingOperations && this.pendingOperations.length > 0) {
            this.simulateBatchAddOperations(this.pendingOperations);
            this.pendingOperations = [];
        }
        this.updateScheduled = false;
    }

    // 模拟DOM操作
    createElement(operation) {
        return {
            id: operation.id,
            type: operation.type,
            action: operation.action,
            hasWidget: !!operation.widget_info
        };
    }

    addToContainer(element) {
        // 模拟DOM添加操作的耗时
        const start = performance.now();
        while (performance.now() - start < 0.1) {
            // 模拟DOM操作耗时
        }
    }

    addBatchToContainer(elements) {
        // 模拟批量DOM添加操作的耗时
        const start = performance.now();
        while (performance.now() - start < elements.length * 0.05) {
            // 批量操作更高效
        }
    }

    scrollToBottom() {
        // 模拟同步滚动的耗时
        const start = performance.now();
        while (performance.now() - start < 0.5) {
            // 模拟滚动操作耗时
        }
    }

    scheduleScrollToBottom() {
        // 模拟异步滚动（requestAnimationFrame）
        setTimeout(() => {
            const start = performance.now();
            while (performance.now() - start < 0.2) {
                // 异步滚动更高效
            }
        }, 0);
    }

    // 运行完整的性能测试套件
    async runFullTest() {
        console.log('🚀 开始OperationRecord窗口性能测试');
        console.log('=' * 50);

        // 测试不同规模的操作
        const testSizes = [10, 50, 100, 500];
        
        for (const size of testSizes) {
            const singleResult = this.testSingleAddition(size);
            const batchResult = this.testBatchAddition(size);
            
            const improvement = ((singleResult.duration - batchResult.duration) / singleResult.duration * 100);
            console.log(`\n📊 ${size}个操作的性能对比:`);
            console.log(`  单次添加: ${singleResult.duration.toFixed(2)}ms`);
            console.log(`  批量添加: ${batchResult.duration.toFixed(2)}ms`);
            console.log(`  性能提升: ${improvement.toFixed(1)}%`);
            
            this.results.push({ size, singleResult, batchResult, improvement });
        }

        // 测试高频场景
        console.log('\n🔥 高频录制场景测试:');
        const highFreqResult = await this.testHighFrequencyAddition(10, 3);
        this.results.push(highFreqResult);

        this.printSummary();
    }

    // 打印测试总结
    printSummary() {
        console.log('\n' + '='.repeat(50));
        console.log('📋 性能测试总结');
        console.log('='.repeat(50));

        const batchResults = this.results.filter(r => r.improvement !== undefined);
        if (batchResults.length > 0) {
            const avgImprovement = batchResults.reduce((sum, r) => sum + r.improvement, 0) / batchResults.length;
            console.log(`\n✅ 平均性能提升: ${avgImprovement.toFixed(1)}%`);
            
            console.log('\n📈 详细结果:');
            batchResults.forEach(result => {
                console.log(`  ${result.size}个操作: ${result.improvement.toFixed(1)}% 提升`);
            });
        }

        const highFreqResult = this.results.find(r => r.type === 'highFreq');
        if (highFreqResult) {
            console.log(`\n🔥 高频场景性能开销: ${(highFreqResult.overhead).toFixed(1)}ms`);
        }

        console.log('\n🎉 测试完成！');
    }
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PerformanceTest, MockOperationGenerator };
    
    // 如果直接运行此文件
    if (require.main === module) {
        const test = new PerformanceTest();
        test.runFullTest().catch(console.error);
    }
} else {
    // 在浏览器环境中
    window.OperationRecordPerformanceTest = { PerformanceTest, MockOperationGenerator };
}
