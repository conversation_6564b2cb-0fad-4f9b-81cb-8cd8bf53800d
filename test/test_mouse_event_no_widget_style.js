/**
 * 测试没有绑定UNI控件对象的鼠标事件红色样式功能
 */

// 模拟OperationRecord数据
const testOperations = [
    // 正常的鼠标点击事件（有控件绑定）
    {
        id: 'test_1',
        timestamp: Date.now(),
        type: 'mouse_click',
        action: '点击按钮',
        target: {
            name: '确定按钮',
            type: 'button',
            position: { x: 100, y: 200 }
        },
        details: {},
        widget_info: {
            Name: '确定按钮',
            Rolename: 'push button',
            ProcessName: 'test-app',
            Coords: { x: 90, y: 190, width: 80, height: 30 },
            States: ['enabled', 'visible']
        }
    },
    
    // 没有控件绑定的鼠标点击事件（应该显示红色）
    {
        id: 'test_2',
        timestamp: Date.now(),
        type: 'mouse_click',
        action: '点击位置',
        target: {
            name: '未知控件',
            type: '未知类型',
            position: { x: 300, y: 400 }
        },
        details: {},
        widget_info: null // 没有控件信息
    },
    
    // 控件识别错误的鼠标右击事件（应该显示红色）
    {
        id: 'test_3',
        timestamp: Date.now(),
        type: 'mouse_right_click',
        action: '右击位置',
        target: {
            name: '未知控件',
            type: '未知类型',
            position: { x: 500, y: 600 }
        },
        details: {},
        widget_info: {
            error: '控件识别失败',
            capture_status: 'error',
            RecordPosition: [500, 600]
        }
    },
    
    // 控件名称为N/A的鼠标双击事件（应该显示红色）
    {
        id: 'test_4',
        timestamp: Date.now(),
        type: 'mouse_double_click',
        action: '双击位置',
        target: {
            name: 'N/A',
            type: '未知类型',
            position: { x: 700, y: 800 }
        },
        details: {},
        widget_info: {
            Name: 'N/A',
            RecordPosition: [700, 800]
        }
    },
    
    // 正常的鼠标拖动事件（有控件绑定）
    {
        id: 'test_5',
        timestamp: Date.now(),
        type: 'mouse_drag',
        action: '拖动滑块',
        target: {
            name: '音量滑块',
            type: 'slider',
            position: { x: 200, y: 300 }
        },
        details: {
            start_position: { x: 200, y: 300 },
            end_position: { x: 250, y: 300 }
        },
        widget_info: {
            Name: '音量滑块',
            Rolename: 'slider',
            ProcessName: 'audio-app',
            Coords: { x: 180, y: 290, width: 100, height: 20 }
        }
    },
    
    // 键盘事件（不应该显示红色，即使没有widget_info）
    {
        id: 'test_6',
        timestamp: Date.now(),
        type: 'keyboard',
        action: '输入文本',
        target: {
            name: '键盘输入',
            type: 'keyboard'
        },
        details: {
            key: 'Hello World'
        },
        widget_info: null // 键盘事件通常没有控件信息
    }
];

/**
 * 测试isMouseEventWithoutWidget方法的逻辑
 */
function testIsMouseEventWithoutWidget(operation) {
    // 检查是否为鼠标事件类型
    const mouseEventTypes = ['mouse', 'mouse_click', 'mouse_right_click', 'mouse_double_click', 'mouse_drag', 'mouse_hover'];
    if (!mouseEventTypes.includes(operation.type)) {
        return false;
    }

    // 检查是否没有有效的控件信息
    if (!operation.widget_info) {
        return true;
    }

    // 检查widget_info是否包含错误信息
    if (operation.widget_info.error || operation.widget_info.capture_status === 'error') {
        return true;
    }

    // 检查是否为无效的控件信息
    if (operation.widget_info.RecordPosition && 
        (!operation.widget_info.Name || operation.widget_info.Name === '未知控件' || operation.widget_info.Name === 'N/A')) {
        return true;
    }

    return false;
}

/**
 * 运行测试
 */
function runTests() {
    console.log('=== 测试没有绑定UNI控件对象的鼠标事件检测 ===\n');
    
    testOperations.forEach((operation, index) => {
        const shouldShowRed = testIsMouseEventWithoutWidget(operation);
        const status = shouldShowRed ? '🔴 红色样式' : '⚪ 正常样式';
        
        console.log(`测试 ${index + 1}: ${operation.action}`);
        console.log(`  事件类型: ${operation.type}`);
        console.log(`  控件信息: ${operation.widget_info ? '有' : '无'}`);
        if (operation.widget_info && (operation.widget_info.error || operation.widget_info.Name)) {
            console.log(`  控件名称: ${operation.widget_info.Name || '无'}`);
            console.log(`  错误信息: ${operation.widget_info.error || '无'}`);
        }
        console.log(`  预期结果: ${status}`);
        console.log('');
    });
    
    // 统计结果
    const redStyleCount = testOperations.filter(op => testIsMouseEventWithoutWidget(op)).length;
    const normalStyleCount = testOperations.length - redStyleCount;
    
    console.log('=== 测试结果统计 ===');
    console.log(`总事件数: ${testOperations.length}`);
    console.log(`红色样式事件: ${redStyleCount}`);
    console.log(`正常样式事件: ${normalStyleCount}`);
    
    // 验证预期结果
    const expectedRedEvents = [1, 2, 3]; // test_2, test_3, test_4 的索引
    const actualRedEvents = testOperations
        .map((op, index) => ({ operation: op, index }))
        .filter(({ operation }) => testIsMouseEventWithoutWidget(operation))
        .map(({ index }) => index);
    
    console.log('\n=== 验证预期结果 ===');
    console.log(`预期红色事件索引: [${expectedRedEvents.join(', ')}]`);
    console.log(`实际红色事件索引: [${actualRedEvents.join(', ')}]`);
    
    const isCorrect = JSON.stringify(expectedRedEvents) === JSON.stringify(actualRedEvents);
    console.log(`测试结果: ${isCorrect ? '✅ 通过' : '❌ 失败'}`);
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testOperations,
        testIsMouseEventWithoutWidget,
        runTests
    };
    
    // 直接运行测试
    runTests();
} else {
    // 在浏览器环境中，将函数暴露到全局
    window.mouseEventStyleTest = {
        testOperations,
        testIsMouseEventWithoutWidget,
        runTests
    };
}
