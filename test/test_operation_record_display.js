/**
 * 测试OperationRecord界面节点显示功能
 * 验证修复后的界面是否能正常显示操作记录
 */

// 模拟OperationRecord接口
class MockOperationRecordWindow {
    constructor() {
        this.operations = [];
        this.recordWindow = null;
        this.updateScheduled = false;
        this.batchUpdateTimer = null;
        this.BATCH_UPDATE_DELAY = 16;
        this.lastUpdateTime = 0;
        this.MIN_UPDATE_INTERVAL = 50;
        this.currentStepIndex = 0;
        this.yamlSteps = [];
        
        this.setupMockWindow();
    }

    setupMockWindow() {
        // 模拟窗口对象
        this.recordWindow = {
            document: {
                getElementById: (id) => {
                    if (id === 'operationListContainer') {
                        return this.mockContainer;
                    }
                    return null;
                },
                createElement: (tag) => {
                    return {
                        tagName: tag.toUpperCase(),
                        className: '',
                        textContent: '',
                        children: [],
                        appendChild: function(child) {
                            this.children.push(child);
                        },
                        removeChild: function(child) {
                            const index = this.children.indexOf(child);
                            if (index > -1) {
                                this.children.splice(index, 1);
                            }
                        },
                        querySelector: function(selector) {
                            // 简单模拟
                            return null;
                        }
                    };
                }
            },
            requestAnimationFrame: (callback) => {
                setTimeout(callback, 16);
            }
        };

        // 模拟容器
        this.mockContainer = {
            children: [],
            firstChild: null,
            scrollTop: 0,
            scrollHeight: 100,
            appendChild: function(child) {
                this.children.push(child);
                this.firstChild = this.children[0] || null;
            },
            removeChild: function(child) {
                const index = this.children.indexOf(child);
                if (index > -1) {
                    this.children.splice(index, 1);
                    this.firstChild = this.children[0] || null;
                }
            }
        };
    }

    // 模拟addOperation方法（修复后的版本）
    addOperation(operation) {
        // 检查是否为重复事件
        if (this.isDuplicateOperation(operation)) {
            console.log(`跳过重复事件: ${operation.action}`);
            return;
        }

        // 自动设置当前步骤信息
        if (operation.stepIndex === undefined) {
            operation.stepIndex = this.currentStepIndex;
        }

        // 添加到操作数组
        this.operations.push(operation);

        // 使用批量更新策略
        this.scheduleUpdate(operation);
    }

    // 模拟调度更新逻辑
    scheduleUpdate(operation) {
        const now = Date.now();
        
        // 如果是前几个操作或者是重要操作，立即更新
        if (this.operations.length <= 3 || this.isImportantOperation(operation)) {
            this.performUpdate();
            return;
        }

        // 如果距离上次更新时间太短，使用批量更新
        if (now - this.lastUpdateTime < this.MIN_UPDATE_INTERVAL) {
            if (!this.updateScheduled) {
                this.updateScheduled = true;
                this.batchUpdateTimer = setTimeout(() => {
                    this.performUpdate();
                }, this.BATCH_UPDATE_DELAY);
            }
        } else {
            // 立即更新
            this.performUpdate();
        }
    }

    // 模拟执行更新
    performUpdate() {
        if (this.batchUpdateTimer) {
            clearTimeout(this.batchUpdateTimer);
            this.batchUpdateTimer = null;
        }
        
        this.updateScheduled = false;
        this.lastUpdateTime = Date.now();
        
        // 执行DOM更新
        this.updateOperationList();
    }

    // 模拟更新操作列表
    updateOperationList() {
        if (!this.recordWindow || !this.mockContainer) {
            return;
        }

        if (this.operations.length === 0) {
            this.showEmptyState();
            return;
        }

        // 使用完全重建策略
        this.performFullRebuild();
    }

    // 模拟完全重建
    performFullRebuild() {
        // 清空容器
        this.mockContainer.children = [];
        this.mockContainer.firstChild = null;

        // 按步骤分组操作记录
        const operationsByStep = this.groupOperationsByStep();

        // 为每个步骤创建分组显示
        operationsByStep.forEach((stepGroup, stepIndex) => {
            const stepContainer = this.createStepContainer(stepGroup, stepIndex);
            this.mockContainer.appendChild(stepContainer);
        });

        console.log(`✅ DOM更新完成，容器中有 ${this.mockContainer.children.length} 个步骤容器`);
    }

    // 模拟按步骤分组
    groupOperationsByStep() {
        const groups = new Map();

        this.operations.forEach((operation, index) => {
            const stepIndex = operation.stepIndex ?? 0;
            const stepName = operation.stepName || `步骤${stepIndex + 1}`;

            if (!groups.has(stepIndex)) {
                groups.set(stepIndex, {
                    stepName,
                    operations: []
                });
            }

            groups.get(stepIndex).operations.push({ operation, index });
        });

        return groups;
    }

    // 模拟创建步骤容器
    createStepContainer(stepGroup, stepIndex) {
        const container = this.recordWindow.document.createElement('div');
        container.className = 'step-container';

        // 创建步骤头部
        const header = this.recordWindow.document.createElement('div');
        header.className = 'step-header';
        header.textContent = `${stepGroup.stepName} (${stepGroup.operations.length})`;
        container.appendChild(header);

        // 创建操作列表
        const operationsContainer = this.recordWindow.document.createElement('div');
        operationsContainer.className = 'step-operations';

        stepGroup.operations.forEach(({ operation, index }) => {
            const item = this.createOperationItem(operation, index);
            operationsContainer.appendChild(item);
        });

        container.appendChild(operationsContainer);
        return container;
    }

    // 模拟创建操作项
    createOperationItem(operation, index) {
        const item = this.recordWindow.document.createElement('div');
        item.className = 'operation-item';

        // 检查是否为没有绑定UNI控件对象的鼠标事件
        if (this.isMouseEventWithoutWidget(operation)) {
            item.className += ' mouse-event-no-widget';
        }

        item.textContent = `${operation.type}: ${operation.action}`;
        return item;
    }

    // 模拟检查鼠标事件是否没有控件绑定
    isMouseEventWithoutWidget(operation) {
        const mouseEventTypes = ['mouse', 'mouse_click', 'mouse_right_click', 'mouse_double_click', 'mouse_drag', 'mouse_hover'];
        if (!mouseEventTypes.includes(operation.type)) {
            return false;
        }

        if (!operation.widget_info) {
            return true;
        }

        if (operation.widget_info.error || operation.widget_info.capture_status === 'error') {
            return true;
        }

        if (operation.widget_info.RecordPosition && 
            (!operation.widget_info.Name || operation.widget_info.Name === '未知控件' || operation.widget_info.Name === 'N/A')) {
            return true;
        }

        return false;
    }

    // 模拟其他方法
    isDuplicateOperation(operation) {
        return false; // 简化实现
    }

    isImportantOperation(operation) {
        if (!operation) return false;
        return operation.action === '录制开始' || 
               operation.action === '录制完成' || 
               operation.action === '录制统计';
    }

    showEmptyState() {
        this.mockContainer.children = [];
        this.mockContainer.firstChild = null;
        console.log('📝 显示空状态');
    }

    // 获取当前状态
    getStatus() {
        return {
            operationsCount: this.operations.length,
            containerChildrenCount: this.mockContainer.children.length,
            updateScheduled: this.updateScheduled
        };
    }
}

// 测试函数
function testOperationRecordDisplay() {
    console.log('🧪 开始测试OperationRecord界面节点显示功能');
    console.log('=' * 50);

    const window = new MockOperationRecordWindow();

    // 测试1：添加单个操作
    console.log('\n📝 测试1：添加单个操作');
    const operation1 = {
        id: 'test_1',
        timestamp: Date.now(),
        type: 'mouse_click',
        action: '点击按钮',
        target: { name: '确定按钮' },
        details: {},
        widget_info: { Name: '确定按钮' }
    };

    window.addOperation(operation1);
    
    // 等待更新完成
    setTimeout(() => {
        const status1 = window.getStatus();
        console.log(`  操作数量: ${status1.operationsCount}`);
        console.log(`  容器子元素: ${status1.containerChildrenCount}`);
        console.log(`  结果: ${status1.containerChildrenCount > 0 ? '✅ 成功显示' : '❌ 未显示'}`);

        // 测试2：添加多个操作
        console.log('\n📝 测试2：添加多个操作');
        const operations = [
            {
                id: 'test_2',
                type: 'mouse_click',
                action: '点击输入框',
                target: { name: '用户名输入框' },
                widget_info: null // 没有控件绑定
            },
            {
                id: 'test_3',
                type: 'keyboard',
                action: '输入文本',
                target: { name: '键盘输入' },
                widget_info: null
            },
            {
                id: 'test_4',
                type: 'mouse_right_click',
                action: '右击菜单',
                target: { name: '未知控件' },
                widget_info: { error: '控件识别失败' }
            }
        ];

        operations.forEach(op => window.addOperation(op));

        setTimeout(() => {
            const status2 = window.getStatus();
            console.log(`  操作数量: ${status2.operationsCount}`);
            console.log(`  容器子元素: ${status2.containerChildrenCount}`);
            console.log(`  结果: ${status2.containerChildrenCount > 0 ? '✅ 成功显示' : '❌ 未显示'}`);

            // 测试3：检查红色样式
            console.log('\n📝 测试3：检查红色样式功能');
            let redStyleCount = 0;
            window.mockContainer.children.forEach(stepContainer => {
                stepContainer.children.forEach(operationsContainer => {
                    if (operationsContainer.className === 'step-operations') {
                        operationsContainer.children.forEach(item => {
                            if (item.className.includes('mouse-event-no-widget')) {
                                redStyleCount++;
                            }
                        });
                    }
                });
            });

            console.log(`  红色样式事件数量: ${redStyleCount}`);
            console.log(`  预期红色样式事件: 2 (test_2和test_4)`);
            console.log(`  结果: ${redStyleCount === 2 ? '✅ 红色样式正常' : '❌ 红色样式异常'}`);

            // 总结
            console.log('\n' + '='.repeat(50));
            console.log('📋 测试总结');
            console.log('='.repeat(50));
            console.log(`✅ 操作记录添加: ${status2.operationsCount === 4 ? '正常' : '异常'}`);
            console.log(`✅ 界面节点显示: ${status2.containerChildrenCount > 0 ? '正常' : '异常'}`);
            console.log(`✅ 红色样式功能: ${redStyleCount === 2 ? '正常' : '异常'}`);
            console.log(`✅ 性能优化: ${!status2.updateScheduled ? '正常' : '异常'}`);

            const allTestsPassed = status2.operationsCount === 4 && 
                                   status2.containerChildrenCount > 0 && 
                                   redStyleCount === 2 && 
                                   !status2.updateScheduled;

            console.log(`\n🎉 总体结果: ${allTestsPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`);
        }, 100);
    }, 100);
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MockOperationRecordWindow, testOperationRecordDisplay };
    
    // 如果直接运行此文件
    if (require.main === module) {
        testOperationRecordDisplay();
    }
} else {
    // 在浏览器环境中
    window.OperationRecordDisplayTest = { MockOperationRecordWindow, testOperationRecordDisplay };
}
