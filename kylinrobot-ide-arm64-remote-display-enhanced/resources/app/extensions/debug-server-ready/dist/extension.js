(()=>{"use strict";var e={23:e=>{e.exports=require("util")},256:function(e,t,s){var r,i=this&&this.__createBinding||(Object.create?function(e,t,s,r){void 0===r&&(r=s);var i=Object.getOwnPropertyDescriptor(t,s);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,r,i)}:function(e,t,s,r){void 0===r&&(r=s),e[r]=t[s]}),n=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(t[t.length]=s);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s=r(e),o=0;o<s.length;o++)"default"!==s[o]&&i(t,e,s[o]);return n(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.activate=function(e){e.subscriptions.push(a.debug.onDidStartDebugSession(e=>{e.configuration.serverReadyAction&&p.start(e)&&p.startListeningTerminalData()})),e.subscriptions.push(a.debug.onDidTerminateDebugSession(e=>{p.stop(e)}));const t=new Set;e.subscriptions.push(a.debug.registerDebugConfigurationProvider("*",{resolveDebugConfigurationWithSubstitutedVariables:(s,r)=>(r.type&&r.serverReadyAction&&(t.has(r.type)||(t.add(r.type),function(e,t){e.subscriptions.push(a.debug.registerDebugAdapterTrackerFactory(t,{createDebugAdapterTracker(e){const t=p.start(e);if(t){let s;return{onDidSendMessage:e=>{if("event"===e.type&&"output"===e.event&&e.body)switch(e.body.category){case"console":case"stderr":case"stdout":e.body.output&&t.detectPattern(e.body.output)}"request"===e.type&&"runInTerminal"===e.command&&e.arguments&&"integrated"===e.arguments.kind&&(s=e.seq)},onWillReceiveMessage:t=>{s&&"response"===t.type&&"runInTerminal"===t.command&&t.body&&s===t.request_seq&&(s=void 0,p.rememberShellPid(e,t.body.shellProcessId))}}}}}))}(e,r.type))),r)}))};const a=o(s(398)),d=o(s(23)),c=s(982),u=new RegExp("(?:"+[/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/.source,/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/.source,/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/.source].join("|")+")","g");class l{constructor(){this._fired=!1}get hasFired(){return this._fired}fire(){this._fired=!0}}class p extends a.Disposable{static start(e){if(e.configuration.serverReadyAction){let t=p.detectors.get(e);return t||(t=new p(e),p.detectors.set(e,t)),t}}static stop(e){const t=p.detectors.get(e);t&&(p.detectors.delete(e),t.sessionStopped(),t.dispose())}static rememberShellPid(e,t){const s=p.detectors.get(e);s&&(s.shellPid=t)}static async startListeningTerminalData(){this.terminalDataListener||(this.terminalDataListener=a.window.onDidWriteTerminalData(async e=>{const t=await e.terminal.processId,s=function(e){return e&&(e=e.replace(u,"")),e}(e.data);for(const[,e]of this.detectors)if(e.shellPid===t)return void e.detectPattern(s);for(const[,e]of this.detectors)if(e.detectPattern(s))return}))}constructor(e){super(()=>this.internalDispose()),this.session=e,this.stoppedEmitter=new a.EventEmitter,this.onDidSessionStop=this.stoppedEmitter.event,this.disposables=new Set([]),e.parentSession?this.trigger=p.start(e.parentSession)?.trigger??new l:this.trigger=new l,this.regexp=new RegExp(e.configuration.serverReadyAction.pattern||"listening on.* (https?://\\S+|[0-9]+)","i")}internalDispose(){this.disposables.forEach(e=>e.dispose()),this.disposables.clear()}sessionStopped(){this.stoppedEmitter.fire()}detectPattern(e){if(!this.trigger.hasFired){const t=this.regexp.exec(e);if(t&&t.length>=1)return this.openExternalWithString(this.session,t.length>1?t[1]:""),this.trigger.fire(),!0}return!1}openExternalWithString(e,t){const s=e.configuration.serverReadyAction;let r;if(""===t){const e=s.uriFormat||"";if(e.indexOf("%s")>=0){const t=a.l10n.t("Format uri ('{0}') uses a substitution placeholder but pattern did not capture anything.",e);return void a.window.showErrorMessage(t,{modal:!0}).then(e=>{})}r=e}else{const e=s.uriFormat||(/^[0-9]+$/.test(t)?"http://localhost:%s":"%s");if(2!==e.split("%s").length){const t=a.l10n.t("Format uri ('{0}') must contain exactly one substitution placeholder.",e);return void a.window.showErrorMessage(t,{modal:!0}).then(e=>{})}r=d.format(e,t)}this.openExternalWithUri(e,r)}async openExternalWithUri(e,t){const s=e.configuration.serverReadyAction;switch(s.action||"openExternally"){case"openExternally":await a.env.openExternal(a.Uri.parse(t));break;case"debugWithChrome":await this.debugWithBrowser("pwa-chrome",e,t);break;case"debugWithEdge":await this.debugWithBrowser("pwa-msedge",e,t);break;case"startDebugging":s.config?await this.startDebugSession(e,s.config.name,s.config):await this.startDebugSession(e,s.name||"unspecified")}}async debugWithBrowser(e,t,s){if(!t.configuration.serverReadyAction.killOnServerStop)return void await this.startBrowserDebugSession(e,t,s);const r=(0,c.randomUUID)(),i=new a.CancellationTokenSource,n=this.catchStartedDebugSession(e=>e.configuration._debugServerReadySessionId===r,i.token);if(!await this.startBrowserDebugSession(e,t,s,r))return i.cancel(),void i.dispose();const o=await n;if(i.dispose(),!o)return;const d=this.onDidSessionStop(async()=>{d.dispose(),this.disposables.delete(d),await a.debug.stopDebugging(o)});this.disposables.add(d)}startBrowserDebugSession(e,t,s,r){return a.debug.startDebugging(t.workspaceFolder,{type:e,name:"Browser Debug",request:"launch",url:s,webRoot:t.configuration.serverReadyAction.webRoot||"${workspaceFolder}",_debugServerReadySessionId:r})}async startDebugSession(e,t,s){if(!e.configuration.serverReadyAction.killOnServerStop)return void await a.debug.startDebugging(e.workspaceFolder,s??t);const r=new a.CancellationTokenSource,i=this.catchStartedDebugSession(e=>e.name===t,r.token);if(!await a.debug.startDebugging(e.workspaceFolder,s??t))return r.cancel(),void r.dispose();const n=await i;if(r.dispose(),!n)return;const o=this.onDidSessionStop(async()=>{o.dispose(),this.disposables.delete(o),await a.debug.stopDebugging(n)});this.disposables.add(o)}catchStartedDebugSession(e,t){return new Promise(s=>{const r=e=>{n.dispose(),i.dispose(),this.disposables.delete(n),this.disposables.delete(i),s(e)},i=t.onCancellationRequested(r),n=a.debug.onDidStartDebugSession(t=>{e(t)&&r(t)});this.disposables.add(n),this.disposables.add(i)})}}p.detectors=new Map},398:e=>{e.exports=require("vscode")},982:e=>{e.exports=require("crypto")}},t={},s=function s(r){var i=t[r];if(void 0!==i)return i.exports;var n=t[r]={exports:{}};return e[r].call(n.exports,n,n.exports,s),n.exports}(256),r=exports;for(var i in s)r[i]=s[i];s.__esModule&&Object.defineProperty(r,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/4cefd8e163dc8255316b7bc1c43103ac9eaeba8e/extensions/debug-server-ready/dist/extension.js.map