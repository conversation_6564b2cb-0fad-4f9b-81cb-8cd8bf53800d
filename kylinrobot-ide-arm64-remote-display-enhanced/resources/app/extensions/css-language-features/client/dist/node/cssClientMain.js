(()=>{var e={107:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=o(n(1398));class a extends s.InlayHint{data;constructor(e,t,n){super(e,t,n)}}t.default=a},144:(e,t,n)=>{const i=n(5380);e.exports=(e,t,n)=>i(e,t,n)>0},190:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentOnTypeFormattingFeature=t.DocumentRangeFormattingFeature=t.DocumentFormattingFeature=void 0;const s=n(1398),a=n(3434),c=o(n(8820)),u=n(9810);var l;!function(e){e.fromConfiguration=function(e){const t=s.workspace.getConfiguration("files",e);return{trimTrailingWhitespace:t.get("trimTrailingWhitespace"),trimFinalNewlines:t.get("trimFinalNewlines"),insertFinalNewline:t.get("insertFinalNewline")}}}(l||(l={}));class d extends u.TextDocumentLanguageFeature{constructor(e){super(e,a.DocumentFormattingRequest.type)}fillClientCapabilities(e){(0,u.ensure)((0,u.ensure)(e,"textDocument"),"formatting").dynamicRegistration=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.documentFormattingProvider);n&&this.register({id:c.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideDocumentFormattingEdits:(e,t,n)=>{const i=this._client,r=(e,t,n)=>{const r={textDocument:i.code2ProtocolConverter.asTextDocumentIdentifier(e),options:i.code2ProtocolConverter.asFormattingOptions(t,l.fromConfiguration(e))};return i.sendRequest(a.DocumentFormattingRequest.type,r,n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asTextEdits(e,n),e=>i.handleFailedRequest(a.DocumentFormattingRequest.type,n,e,null))},o=i.middleware;return o.provideDocumentFormattingEdits?o.provideDocumentFormattingEdits(e,t,n,r):r(e,t,n)}};return[s.languages.registerDocumentFormattingEditProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),n),n]}}t.DocumentFormattingFeature=d;class h extends u.TextDocumentLanguageFeature{constructor(e){super(e,a.DocumentRangeFormattingRequest.type)}fillClientCapabilities(e){const t=(0,u.ensure)((0,u.ensure)(e,"textDocument"),"rangeFormatting");t.dynamicRegistration=!0,t.rangesSupport=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.documentRangeFormattingProvider);n&&this.register({id:c.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideDocumentRangeFormattingEdits:(e,t,n,i)=>{const r=this._client,o=(e,t,n,i)=>{const o={textDocument:r.code2ProtocolConverter.asTextDocumentIdentifier(e),range:r.code2ProtocolConverter.asRange(t),options:r.code2ProtocolConverter.asFormattingOptions(n,l.fromConfiguration(e))};return r.sendRequest(a.DocumentRangeFormattingRequest.type,o,i).then(e=>i.isCancellationRequested?null:r.protocol2CodeConverter.asTextEdits(e,i),e=>r.handleFailedRequest(a.DocumentRangeFormattingRequest.type,i,e,null))},s=r.middleware;return s.provideDocumentRangeFormattingEdits?s.provideDocumentRangeFormattingEdits(e,t,n,i,o):o(e,t,n,i)}};return e.rangesSupport&&(n.provideDocumentRangesFormattingEdits=(e,t,n,i)=>{const r=this._client,o=(e,t,n,i)=>{const o={textDocument:r.code2ProtocolConverter.asTextDocumentIdentifier(e),ranges:r.code2ProtocolConverter.asRanges(t),options:r.code2ProtocolConverter.asFormattingOptions(n,l.fromConfiguration(e))};return r.sendRequest(a.DocumentRangesFormattingRequest.type,o,i).then(e=>i.isCancellationRequested?null:r.protocol2CodeConverter.asTextEdits(e,i),e=>r.handleFailedRequest(a.DocumentRangesFormattingRequest.type,i,e,null))},s=r.middleware;return s.provideDocumentRangesFormattingEdits?s.provideDocumentRangesFormattingEdits(e,t,n,i,o):o(e,t,n,i)}),[s.languages.registerDocumentRangeFormattingEditProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),n),n]}}t.DocumentRangeFormattingFeature=h;class p extends u.TextDocumentLanguageFeature{constructor(e){super(e,a.DocumentOnTypeFormattingRequest.type)}fillClientCapabilities(e){(0,u.ensure)((0,u.ensure)(e,"textDocument"),"onTypeFormatting").dynamicRegistration=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.documentOnTypeFormattingProvider);n&&this.register({id:c.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideOnTypeFormattingEdits:(e,t,n,i,r)=>{const o=this._client,s=(e,t,n,i,r)=>{const s={textDocument:o.code2ProtocolConverter.asTextDocumentIdentifier(e),position:o.code2ProtocolConverter.asPosition(t),ch:n,options:o.code2ProtocolConverter.asFormattingOptions(i,l.fromConfiguration(e))};return o.sendRequest(a.DocumentOnTypeFormattingRequest.type,s,r).then(e=>r.isCancellationRequested?null:o.protocol2CodeConverter.asTextEdits(e,r),e=>o.handleFailedRequest(a.DocumentOnTypeFormattingRequest.type,r,e,null))},c=o.middleware;return c.provideOnTypeFormattingEdits?c.provideOnTypeFormattingEdits(e,t,n,i,r,s):s(e,t,n,i,r)}},i=e.moreTriggerCharacter||[];return[s.languages.registerOnTypeFormattingEditProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),n,e.firstTriggerCharacter,...i),n]}}t.DocumentOnTypeFormattingFeature=p},276:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TypeDefinitionFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);class s extends o.TextDocumentLanguageFeature{constructor(e){super(e,r.TypeDefinitionRequest.type)}fillClientCapabilities(e){(0,o.ensure)((0,o.ensure)(e,"textDocument"),"typeDefinition").dynamicRegistration=!0;const t=(0,o.ensure)((0,o.ensure)(e,"textDocument"),"typeDefinition");t.dynamicRegistration=!0,t.linkSupport=!0}initialize(e,t){const[n,i]=this.getRegistration(t,e.typeDefinitionProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=e.documentSelector,n={provideTypeDefinition:(e,t,n)=>{const i=this._client,o=(e,t,n)=>i.sendRequest(r.TypeDefinitionRequest.type,i.code2ProtocolConverter.asTextDocumentPositionParams(e,t),n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asDefinitionResult(e,n),e=>i.handleFailedRequest(r.TypeDefinitionRequest.type,n,e,null)),s=i.middleware;return s.provideTypeDefinition?s.provideTypeDefinition(e,t,n,o):o(e,t,n)}};return[this.registerProvider(t,n),n]}registerProvider(e,t){return i.languages.registerTypeDefinitionProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}}t.TypeDefinitionFeature=s},378:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ProgressFeature=void 0;const i=n(3434),r=n(5679);t.ProgressFeature=class{_client;activeParts;constructor(e){this._client=e,this.activeParts=new Set}getState(){return{kind:"window",id:i.WorkDoneProgressCreateRequest.method,registrations:this.activeParts.size>0}}fillClientCapabilities(e){var t,n;(t=e,n="window",void 0===t[n]&&(t[n]=Object.create(null)),t[n]).workDoneProgress=!0}initialize(){const e=this._client,t=e=>{this.activeParts.delete(e)};e.onRequest(i.WorkDoneProgressCreateRequest.type,e=>{this.activeParts.add(new r.ProgressPart(this._client,e.token,t))})}clear(){for(const e of this.activeParts)e.done();this.activeParts.clear()}}},514:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TypeHierarchyFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);class s{client;middleware;constructor(e){this.client=e,this.middleware=e.middleware}prepareTypeHierarchy(e,t,n){const i=this.client,o=this.middleware,s=(e,t,n)=>{const o=i.code2ProtocolConverter.asTextDocumentPositionParams(e,t);return i.sendRequest(r.TypeHierarchyPrepareRequest.type,o,n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asTypeHierarchyItems(e,n),e=>i.handleFailedRequest(r.TypeHierarchyPrepareRequest.type,n,e,null))};return o.prepareTypeHierarchy?o.prepareTypeHierarchy(e,t,n,s):s(e,t,n)}provideTypeHierarchySupertypes(e,t){const n=this.client,i=this.middleware,o=(e,t)=>{const i={item:n.code2ProtocolConverter.asTypeHierarchyItem(e)};return n.sendRequest(r.TypeHierarchySupertypesRequest.type,i,t).then(e=>t.isCancellationRequested?null:n.protocol2CodeConverter.asTypeHierarchyItems(e,t),e=>n.handleFailedRequest(r.TypeHierarchySupertypesRequest.type,t,e,null))};return i.provideTypeHierarchySupertypes?i.provideTypeHierarchySupertypes(e,t,o):o(e,t)}provideTypeHierarchySubtypes(e,t){const n=this.client,i=this.middleware,o=(e,t)=>{const i={item:n.code2ProtocolConverter.asTypeHierarchyItem(e)};return n.sendRequest(r.TypeHierarchySubtypesRequest.type,i,t).then(e=>t.isCancellationRequested?null:n.protocol2CodeConverter.asTypeHierarchyItems(e,t),e=>n.handleFailedRequest(r.TypeHierarchySubtypesRequest.type,t,e,null))};return i.provideTypeHierarchySubtypes?i.provideTypeHierarchySubtypes(e,t,o):o(e,t)}}class a extends o.TextDocumentLanguageFeature{constructor(e){super(e,r.TypeHierarchyPrepareRequest.type)}fillClientCapabilities(e){(0,o.ensure)((0,o.ensure)(e,"textDocument"),"typeHierarchy").dynamicRegistration=!0}initialize(e,t){const[n,i]=this.getRegistration(t,e.typeHierarchyProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=this._client,n=new s(t);return[i.languages.registerTypeHierarchyProvider(t.protocol2CodeConverter.asDocumentSelector(e.documentSelector),n),n]}}t.TypeHierarchyFeature=a},599:(e,t,n)=>{const i=n(5380);e.exports=(e,t,n)=>i(e,t,n)<0},613:function(e,t,n){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(i=function(e){return i=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},i(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=i(e),s=0;s<n.length;s++)"default"!==n[s]&&r(t,e,n[s]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.getNodeFSRequestService=function(){function e(e){if(!e.startsWith("file://"))throw new Error("fileRequestService can only handle file URLs")}return{getContent:(t,n)=>(e(t),new Promise((e,i)=>{const r=c.Uri.parse(t);a.readFile(r.fsPath,n,(t,n)=>{if(t)return i(t);e(n.toString())})})),stat:t=>(e(t),new Promise((e,n)=>{const i=c.Uri.parse(t);a.stat(i.fsPath,(t,i)=>{if(t)return"ENOENT"===t.code?e({type:u.FileType.Unknown,ctime:-1,mtime:-1,size:-1}):n(t);let r=u.FileType.Unknown;i.isFile()?r=u.FileType.File:i.isDirectory()?r=u.FileType.Directory:i.isSymbolicLink()&&(r=u.FileType.SymbolicLink),e({type:r,ctime:i.ctime.getTime(),mtime:i.mtime.getTime(),size:i.size})})})),readDirectory:t=>(e(t),new Promise((e,n)=>{const i=c.Uri.parse(t).fsPath;a.readdir(i,{withFileTypes:!0},(t,i)=>{if(t)return n(t);e(i.map(e=>e.isSymbolicLink()?[e.name,u.FileType.SymbolicLink]:e.isDirectory()?[e.name,u.FileType.Directory]:e.isFile()?[e.name,u.FileType.File]:[e.name,u.FileType.Unknown]))})}))}};const a=s(n(9896)),c=n(1398),u=n(6149)},623:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.RenameFeature=void 0;const s=n(1398),a=n(3434),c=o(n(8820)),u=o(n(1027)),l=n(9810);class d extends l.TextDocumentLanguageFeature{constructor(e){super(e,a.RenameRequest.type)}fillClientCapabilities(e){const t=(0,l.ensure)((0,l.ensure)(e,"textDocument"),"rename");t.dynamicRegistration=!0,t.prepareSupport=!0,t.prepareSupportDefaultBehavior=a.PrepareSupportDefaultBehavior.Identifier,t.honorsChangeAnnotations=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.renameProvider);n&&(u.boolean(e.renameProvider)&&(n.prepareProvider=!1),this.register({id:c.generateUuid(),registerOptions:n}))}registerLanguageProvider(e){const t=e.documentSelector,n={provideRenameEdits:(e,t,n,i)=>{const r=this._client,o=(e,t,n,i)=>{const o={textDocument:r.code2ProtocolConverter.asTextDocumentIdentifier(e),position:r.code2ProtocolConverter.asPosition(t),newName:n};return r.sendRequest(a.RenameRequest.type,o,i).then(e=>i.isCancellationRequested?null:r.protocol2CodeConverter.asWorkspaceEdit(e,i),e=>r.handleFailedRequest(a.RenameRequest.type,i,e,null,!1))},s=r.middleware;return s.provideRenameEdits?s.provideRenameEdits(e,t,n,i,o):o(e,t,n,i)},prepareRename:e.prepareProvider?(e,t,n)=>{const i=this._client,r=(e,t,n)=>{const r={textDocument:i.code2ProtocolConverter.asTextDocumentIdentifier(e),position:i.code2ProtocolConverter.asPosition(t)};return i.sendRequest(a.PrepareRenameRequest.type,r,n).then(e=>n.isCancellationRequested?null:a.Range.is(e)?i.protocol2CodeConverter.asRange(e):this.isDefaultBehavior(e)?!0===e.defaultBehavior?null:Promise.reject(new Error("The element can't be renamed.")):e&&a.Range.is(e.range)?{range:i.protocol2CodeConverter.asRange(e.range),placeholder:e.placeholder}:Promise.reject(new Error("The element can't be renamed.")),e=>{throw"string"==typeof e.message?new Error(e.message):new Error("The element can't be renamed.")})},o=i.middleware;return o.prepareRename?o.prepareRename(e,t,n,r):r(e,t,n)}:void 0};return[this.registerProvider(t,n),n]}registerProvider(e,t){return s.languages.registerRenameProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}isDefaultBehavior(e){const t=e;return t&&u.boolean(t.defaultBehavior)}}t.RenameFeature=d},708:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentLinkFeature=void 0;const s=n(1398),a=n(3434),c=n(9810),u=o(n(8820));class l extends c.TextDocumentLanguageFeature{constructor(e){super(e,a.DocumentLinkRequest.type)}fillClientCapabilities(e){const t=(0,c.ensure)((0,c.ensure)(e,"textDocument"),"documentLink");t.dynamicRegistration=!0,t.tooltipSupport=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.documentLinkProvider);n&&this.register({id:u.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideDocumentLinks:(e,t)=>{const n=this._client,i=(e,t)=>n.sendRequest(a.DocumentLinkRequest.type,n.code2ProtocolConverter.asDocumentLinkParams(e),t).then(e=>t.isCancellationRequested?null:n.protocol2CodeConverter.asDocumentLinks(e,t),e=>n.handleFailedRequest(a.DocumentLinkRequest.type,t,e,null)),r=n.middleware;return r.provideDocumentLinks?r.provideDocumentLinks(e,t,i):i(e,t)},resolveDocumentLink:e.resolveProvider?(e,t)=>{const n=this._client,i=(e,t)=>n.sendRequest(a.DocumentLinkResolveRequest.type,n.code2ProtocolConverter.asDocumentLink(e),t).then(i=>t.isCancellationRequested?e:n.protocol2CodeConverter.asDocumentLink(i),i=>n.handleFailedRequest(a.DocumentLinkResolveRequest.type,t,i,e)),r=n.middleware;return r.resolveDocumentLink?r.resolveDocumentLink(e,t,i):i(e,t)}:void 0};return[s.languages.registerDocumentLinkProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),n),n]}}t.DocumentLinkFeature=l},710:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.ProposedFeatures=t.LanguageClient=t.BaseLanguageClient=t.ShutdownMode=t.MessageTransports=t.SuspendMode=t.State=t.CloseAction=t.ErrorAction=t.RevealOutputChannelOn=void 0;const s=n(1398),a=n(3434),c=o(n(4346)),u=o(n(3881)),l=o(n(1027)),d=n(5671),h=o(n(8820)),p=n(5679),f=n(9810),g=n(7034),m=n(4292),v=n(4267),y=n(9504),b=n(3507),_=n(4231),C=n(3876),D=n(4092),S=n(986),w=n(7984),R=n(3652),T=n(4384),P=n(276),O=n(7523),k=n(7327),x=n(2114),E=n(3760),M=n(190),F=n(623),q=n(708),I=n(2837),N=n(2855),j=n(7921),L=n(7086),A=n(8382),$=n(1423),H=n(2661),W=n(514),U=n(7511),K=n(6811),z=n(6272),B=n(4201),V=n(2758),G=n(5122),X=n(6576),J=n(378);var Q,Y,Z,ee,te,ne,ie,re,oe,se;!function(e){e[e.Debug=0]="Debug",e[e.Info=1]="Info",e[e.Warn=2]="Warn",e[e.Error=3]="Error",e[e.Never=4]="Never"}(Q||(t.RevealOutputChannelOn=Q={})),function(e){e[e.Continue=1]="Continue",e[e.Shutdown=2]="Shutdown"}(Y||(t.ErrorAction=Y={})),function(e){e[e.DoNotRestart=1]="DoNotRestart",e[e.Restart=2]="Restart"}(Z||(t.CloseAction=Z={})),function(e){e[e.Stopped=1]="Stopped",e[e.Starting=3]="Starting",e[e.StartFailed=4]="StartFailed",e[e.Running=2]="Running"}(ee||(t.State=ee={})),function(e){e.off="off",e.on="on"}(te||(t.SuspendMode=te={})),function(e){e.sanitizeIsTrusted=function(e){return null!=e&&!!("boolean"==typeof e||"object"==typeof e&&null!==e&&l.stringArray(e.enabledCommands))&&e}}(ne||(ne={}));class ae{client;maxRestartCount;restarts;constructor(e,t){this.client=e,this.maxRestartCount=t,this.restarts=[]}error(e,t,n){return n&&n<=3?{action:Y.Continue}:{action:Y.Shutdown}}closed(){return this.restarts.push(Date.now()),this.restarts.length<=this.maxRestartCount?{action:Z.Restart}:this.restarts[this.restarts.length-1]-this.restarts[0]<=18e4?{action:Z.DoNotRestart,message:`The ${this.client.name} server crashed ${this.maxRestartCount+1} times in the last 3 minutes. The server will not be restarted. See the output for more information.`}:(this.restarts.shift(),{action:Z.Restart})}}!function(e){e.Initial="initial",e.Starting="starting",e.StartFailed="startFailed",e.Running="running",e.Stopping="stopping",e.Stopped="stopped"}(ie||(ie={})),function(e){e.is=function(e){return e&&a.MessageReader.is(e.reader)&&a.MessageWriter.is(e.writer)}}(re||(t.MessageTransports=re={})),function(e){e.Restart="restart",e.Stop="stop"}(oe||(t.ShutdownMode=oe={}));class ce{open;_onOpen;_onClose;disposable;constructor(){this.open=new Set,this._onOpen=new s.EventEmitter,this._onClose=new s.EventEmitter,ce.fillTabResources(this.open);void 0!==s.window.tabGroups.onDidChangeTabs?this.disposable=s.window.tabGroups.onDidChangeTabs(e=>{if(0===e.closed.length&&0===e.opened.length)return;const t=this.open,n=new Set;ce.fillTabResources(n);const i=new Set,r=new Set(n);for(const e of t.values())n.has(e)?r.delete(e):i.add(e);if(this.open=n,i.size>0){const e=new Set;for(const t of i)e.add(s.Uri.parse(t));this._onClose.fire(e)}if(r.size>0){const e=new Set;for(const t of r)e.add(s.Uri.parse(t));this._onOpen.fire(e)}}):this.disposable={dispose:()=>{}}}get onClose(){return this._onClose.event}get onOpen(){return this._onOpen.event}dispose(){this.disposable.dispose()}isActive(e){return e instanceof s.Uri?s.window.activeTextEditor?.document.uri===e:s.window.activeTextEditor?.document===e}isVisible(e){const t=e instanceof s.Uri?e:e.uri;return t.scheme===m.NotebookDocumentSyncFeature.CellScheme?s.workspace.notebookDocuments.some(e=>!!this.open.has(e.uri.toString())&&void 0!==e.getCells().find(e=>e.document.uri.toString()===t.toString())):this.open.has(t.toString())}getTabResources(){const e=new Set;return ce.fillTabResources(new Set,e),e}static fillTabResources(e,t){const n=e??new Set;for(const e of s.window.tabGroups.all)for(const i of e.tabs){const e=i.input;let r;e instanceof s.TabInputText?r=e.uri:e instanceof s.TabInputTextDiff?r=e.modified:(e instanceof s.TabInputCustom||e instanceof s.TabInputNotebook)&&(r=e.uri),void 0===r||n.has(r.toString())||(n.add(r.toString()),void 0!==t&&t.add(r))}}}class ue{_id;_name;_clientOptions;_state;_onStart;_onStop;_connection;_idleInterval;_ignoredRegistrations;_listeners;_disposed;_notificationHandlers;_notificationDisposables;_pendingNotificationHandlers;_requestHandlers;_requestDisposables;_pendingRequestHandlers;_progressHandlers;_pendingProgressHandlers;_progressDisposables;_initializeResult;_outputChannel;_disposeOutputChannel;_traceOutputChannel;_capabilities;_diagnostics;_syncedDocuments;_didChangeTextDocumentFeature;_inFlightOpenNotifications;_pendingChangeSemaphore;_pendingChangeDelayer;_didOpenTextDocumentFeature;_fileEvents;_fileEventDelayer;_telemetryEmitter;_stateChangeEmitter;_trace;_traceFormat=a.TraceFormat.Text;_tracer;_c2p;_p2c;_tabsModel;constructor(e,t,n){this._id=e,this._name=t;const i={isTrusted:!1,supportHtml:!1,supportThemeIcons:!1};void 0!==(n=n||{}).markdown&&(i.isTrusted=ne.sanitizeIsTrusted(n.markdown.isTrusted),i.supportHtml=!0===n.markdown.supportHtml,i.supportThemeIcons=!0===n.markdown.supportThemeIcons),this._clientOptions={documentSelector:n.documentSelector??[],synchronize:n.synchronize??{},diagnosticCollectionName:n.diagnosticCollectionName,outputChannelName:n.outputChannelName??this._name,revealOutputChannelOn:n.revealOutputChannelOn??Q.Error,stdioEncoding:n.stdioEncoding??"utf8",initializationOptions:n.initializationOptions,initializationFailedHandler:n.initializationFailedHandler,progressOnInitialization:!!n.progressOnInitialization,errorHandler:n.errorHandler??this.createDefaultErrorHandler(n.connectionOptions?.maxRestartCount),middleware:n.middleware??{},uriConverters:n.uriConverters,workspaceFolder:n.workspaceFolder,connectionOptions:n.connectionOptions,markdown:i,diagnosticPullOptions:n.diagnosticPullOptions??{onChange:!0,onSave:!1},notebookDocumentOptions:n.notebookDocumentOptions??{},textSynchronization:this.createTextSynchronizationOptions(n.textSynchronization)},this._clientOptions.synchronize=this._clientOptions.synchronize||{},this._state=ie.Initial,this._ignoredRegistrations=new Set,this._listeners=[],this._notificationHandlers=new Map,this._pendingNotificationHandlers=new Map,this._notificationDisposables=new Map,this._requestHandlers=new Map,this._pendingRequestHandlers=new Map,this._requestDisposables=new Map,this._progressHandlers=new Map,this._pendingProgressHandlers=new Map,this._progressDisposables=new Map,this._connection=void 0,this._initializeResult=void 0,n.outputChannel?(this._outputChannel=n.outputChannel,this._disposeOutputChannel=!1):(this._outputChannel=void 0,this._disposeOutputChannel=!0),this._traceOutputChannel=n.traceOutputChannel,this._diagnostics=void 0,this._inFlightOpenNotifications=new Set,this._pendingChangeSemaphore=new d.Semaphore(1),this._pendingChangeDelayer=new d.Delayer(250),this._fileEvents=[],this._fileEventDelayer=new d.Delayer(250),this._onStop=void 0,this._telemetryEmitter=new a.Emitter,this._stateChangeEmitter=new a.Emitter,this._trace=a.Trace.Off,this._tracer={log:(e,t)=>{l.string(e)?this.logTrace(e,t):this.logObjectTrace(e)}},this._c2p=c.createConverter(n.uriConverters?n.uriConverters.code2Protocol:void 0),this._p2c=u.createConverter(n.uriConverters?n.uriConverters.protocol2Code:void 0,this._clientOptions.markdown.isTrusted,this._clientOptions.markdown.supportHtml,this._clientOptions.markdown.supportThemeIcons),this._syncedDocuments=new Map,this.registerBuiltinFeatures()}createTextSynchronizationOptions(e){return e&&"boolean"==typeof e.delayOpenNotifications?{delayOpenNotifications:e.delayOpenNotifications}:{delayOpenNotifications:!1}}get name(){return this._name}get middleware(){return this._clientOptions.middleware??Object.create(null)}get clientOptions(){return this._clientOptions}get protocol2CodeConverter(){return this._p2c}get code2ProtocolConverter(){return this._c2p}get tabsModel(){return void 0===this._tabsModel&&(this._tabsModel=new ce),this._tabsModel}get onTelemetry(){return this._telemetryEmitter.event}get onDidChangeState(){return this._stateChangeEmitter.event}get outputChannel(){return this._outputChannel||(this._outputChannel=s.window.createOutputChannel(this._clientOptions.outputChannelName?this._clientOptions.outputChannelName:this._name)),this._outputChannel}get traceOutputChannel(){return this._traceOutputChannel?this._traceOutputChannel:this.outputChannel}get diagnostics(){return this._diagnostics}get state(){return this.getPublicState()}get $state(){return this._state}set $state(e){const t=this.getPublicState();this._state=e;const n=this.getPublicState();n!==t&&this._stateChangeEmitter.fire({oldState:t,newState:n})}getPublicState(){switch(this.$state){case ie.Starting:return ee.Starting;case ie.Running:return ee.Running;case ie.StartFailed:return ee.StartFailed;default:return ee.Stopped}}get initializeResult(){return this._initializeResult}async sendRequest(e,...t){if(this.$state===ie.StartFailed||this.$state===ie.Stopping||this.$state===ie.Stopped)return Promise.reject(new a.ResponseError(a.ErrorCodes.ConnectionInactive,"Client is not running"));const n=await this.$start();let i,r;if(await this._didOpenTextDocumentFeature.sendPendingOpenNotifications(),this._didChangeTextDocumentFeature.syncKind===a.TextDocumentSyncKind.Full&&await this.sendPendingFullTextDocumentChanges(n),1===t.length?a.CancellationToken.is(t[0])?r=t[0]:i=t[0]:2===t.length&&(i=t[0],r=t[1]),void 0!==r&&r.isCancellationRequested)return Promise.reject(new a.ResponseError(a.LSPErrorCodes.RequestCancelled,"Request got cancelled"));const o=this._clientOptions.middleware?.sendRequest;return void 0!==o?o(e,i,r,(e,t,i)=>{const r=[];return void 0!==t&&r.push(t),void 0!==i&&r.push(i),n.sendRequest(e,...r)}):n.sendRequest(e,...t)}onRequest(e,t){const n="string"==typeof e?e:e.method;this._requestHandlers.set(n,t);const i=this.activeConnection();let r;return void 0!==i?(this._requestDisposables.set(n,i.onRequest(e,t)),r={dispose:()=>{const e=this._requestDisposables.get(n);void 0!==e&&(e.dispose(),this._requestDisposables.delete(n))}}):(this._pendingRequestHandlers.set(n,t),r={dispose:()=>{this._pendingRequestHandlers.delete(n);const e=this._requestDisposables.get(n);void 0!==e&&(e.dispose(),this._requestDisposables.delete(n))}}),{dispose:()=>{this._requestHandlers.delete(n),r.dispose()}}}async sendNotification(e,t){if(this.$state===ie.StartFailed||this.$state===ie.Stopping||this.$state===ie.Stopped)return Promise.reject(new a.ResponseError(a.ErrorCodes.ConnectionInactive,"Client is not running"));const n=this._didChangeTextDocumentFeature.syncKind===a.TextDocumentSyncKind.Full;let i,r;n&&"string"!=typeof e&&e.method===a.DidOpenTextDocumentNotification.method&&(i=t?.textDocument.uri,this._inFlightOpenNotifications.add(i)),"string"!=typeof e&&e.method===a.DidCloseTextDocumentNotification.method&&(r=t.textDocument.uri);const o=await this.$start();await this._didOpenTextDocumentFeature.sendPendingOpenNotifications(r),n&&await this.sendPendingFullTextDocumentChanges(o),void 0!==i&&this._inFlightOpenNotifications.delete(i);const s=this._clientOptions.middleware?.sendNotification;return s?s(e,o.sendNotification.bind(o),t):o.sendNotification(e,t)}onNotification(e,t){const n="string"==typeof e?e:e.method;this._notificationHandlers.set(n,t);const i=this.activeConnection();let r;return void 0!==i?(this._notificationDisposables.set(n,i.onNotification(e,t)),r={dispose:()=>{const e=this._notificationDisposables.get(n);void 0!==e&&(e.dispose(),this._notificationDisposables.delete(n))}}):(this._pendingNotificationHandlers.set(n,t),r={dispose:()=>{this._pendingNotificationHandlers.delete(n);const e=this._notificationDisposables.get(n);void 0!==e&&(e.dispose(),this._notificationDisposables.delete(n))}}),{dispose:()=>{this._notificationHandlers.delete(n),r.dispose()}}}async sendProgress(e,t,n){if(this.$state===ie.StartFailed||this.$state===ie.Stopping||this.$state===ie.Stopped)return Promise.reject(new a.ResponseError(a.ErrorCodes.ConnectionInactive,"Client is not running"));try{return(await this.$start()).sendProgress(e,t,n)}catch(e){throw this.error(`Sending progress for token ${t} failed.`,e),e}}onProgress(e,t,n){this._progressHandlers.set(t,{type:e,handler:n});const i=this.activeConnection();let r;const o=this._clientOptions.middleware?.handleWorkDoneProgress,s=a.WorkDoneProgress.is(e)&&void 0!==o?e=>{o(t,e,()=>n(e))}:n;return void 0!==i?(this._progressDisposables.set(t,i.onProgress(e,t,s)),r={dispose:()=>{const e=this._progressDisposables.get(t);void 0!==e&&(e.dispose(),this._progressDisposables.delete(t))}}):(this._pendingProgressHandlers.set(t,{type:e,handler:n}),r={dispose:()=>{this._pendingProgressHandlers.delete(t);const e=this._progressDisposables.get(t);void 0!==e&&(e.dispose(),this._progressDisposables.delete(t))}}),{dispose:()=>{this._progressHandlers.delete(t),r.dispose()}}}createDefaultErrorHandler(e){if(void 0!==e&&e<0)throw new Error(`Invalid maxRestartCount: ${e}`);return new ae(this,e??4)}async setTrace(e){this._trace=e;const t=this.activeConnection();void 0!==t&&await t.trace(this._trace,this._tracer,{sendNotification:!1,traceFormat:this._traceFormat})}data2String(e){if(e instanceof a.ResponseError){const t=e;return`  Message: ${t.message}\n  Code: ${t.code} ${t.data?"\n"+t.data.toString():""}`}return e instanceof Error?l.string(e.stack)?e.stack:e.message:l.string(e)?e:e.toString()}debug(e,t,n=!0){this.logOutputMessage(a.MessageType.Debug,Q.Debug,"Debug",e,t,n)}info(e,t,n=!0){this.logOutputMessage(a.MessageType.Info,Q.Info,"Info",e,t,n)}warn(e,t,n=!0){this.logOutputMessage(a.MessageType.Warning,Q.Warn,"Warn",e,t,n)}error(e,t,n=!0){this.logOutputMessage(a.MessageType.Error,Q.Error,"Error",e,t,n)}logOutputMessage(e,t,n,i,r,o){this.outputChannel.appendLine(`[${n.padEnd(5)} - ${(new Date).toLocaleTimeString()}] ${i}`),null!=r&&this.outputChannel.appendLine(this.data2String(r)),("force"===o||o&&this._clientOptions.revealOutputChannelOn<=t)&&this.showNotificationMessage(e,i)}showNotificationMessage(e,t){t=t??"A request has failed. See the output for more information.",(e===a.MessageType.Error?s.window.showErrorMessage:e===a.MessageType.Warning?s.window.showWarningMessage:s.window.showInformationMessage)(t,"Go to output").then(e=>{void 0!==e&&this.outputChannel.show(!0)})}logTrace(e,t){this.traceOutputChannel.appendLine(`[Trace - ${(new Date).toLocaleTimeString()}] ${e}`),t&&this.traceOutputChannel.appendLine(this.data2String(t))}logObjectTrace(e){e.isLSPMessage&&e.type?this.traceOutputChannel.append(`[LSP   - ${(new Date).toLocaleTimeString()}] `):this.traceOutputChannel.append(`[Trace - ${(new Date).toLocaleTimeString()}] `),e&&this.traceOutputChannel.appendLine(`${JSON.stringify(e)}`)}needsStart(){return this.$state===ie.Initial||this.$state===ie.Stopping||this.$state===ie.Stopped}needsStop(){return this.$state===ie.Starting||this.$state===ie.Running}activeConnection(){return this.$state===ie.Running&&void 0!==this._connection?this._connection:void 0}isRunning(){return this.$state===ie.Running}async start(){if("disposing"===this._disposed||"disposed"===this._disposed)throw new Error("Client got disposed and can't be restarted.");if(this.$state===ie.Stopping)throw new Error("Client is currently stopping. Can only restart a full stopped client");if(void 0!==this._onStart)return this._onStart;const[e,t,n]=this.createOnStartPromise();this._onStart=e,void 0===this._diagnostics&&(this._diagnostics=s.languages.createDiagnosticCollection(this._clientOptions.diagnosticCollectionName??this._id));for(const[e,t]of this._notificationHandlers)this._pendingNotificationHandlers.has(e)||this._pendingNotificationHandlers.set(e,t);for(const[e,t]of this._requestHandlers)this._pendingRequestHandlers.has(e)||this._pendingRequestHandlers.set(e,t);for(const[e,t]of this._progressHandlers)this._pendingProgressHandlers.has(e)||this._pendingProgressHandlers.set(e,t);this.$state=ie.Starting;try{const e=await this.createConnection();e.onNotification(a.LogMessageNotification.type,e=>{switch(e.type){case a.MessageType.Error:this.error(e.message,void 0,!1);break;case a.MessageType.Warning:this.warn(e.message,void 0,!1);break;case a.MessageType.Info:this.info(e.message,void 0,!1);break;case a.MessageType.Debug:this.debug(e.message,void 0,!1);break;default:this.outputChannel.appendLine(e.message)}}),e.onNotification(a.ShowMessageNotification.type,e=>{switch(e.type){case a.MessageType.Error:s.window.showErrorMessage(e.message);break;case a.MessageType.Warning:s.window.showWarningMessage(e.message);break;case a.MessageType.Info:default:s.window.showInformationMessage(e.message)}}),e.onRequest(a.ShowMessageRequest.type,e=>{let t;switch(e.type){case a.MessageType.Error:t=s.window.showErrorMessage;break;case a.MessageType.Warning:t=s.window.showWarningMessage;break;case a.MessageType.Info:default:t=s.window.showInformationMessage}const n=e.actions||[];return t(e.message,...n)}),e.onNotification(a.TelemetryEventNotification.type,e=>{this._telemetryEmitter.fire(e)}),e.onRequest(a.ShowDocumentRequest.type,async(e,t)=>{const n=async e=>{const t=this.protocol2CodeConverter.asUri(e.uri);try{if(!0===e.external)return{success:await s.env.openExternal(t)};{const n={};return void 0!==e.selection&&(n.selection=this.protocol2CodeConverter.asRange(e.selection)),void 0===e.takeFocus||!1===e.takeFocus?n.preserveFocus=!0:!0===e.takeFocus&&(n.preserveFocus=!1),await s.window.showTextDocument(t,n),{success:!0}}}catch(e){return{success:!1}}},i=this._clientOptions.middleware.window?.showDocument;return void 0!==i?i(e,t,n):n(e)}),e.listen(),await this.initialize(e),t()}catch(e){this.$state=ie.StartFailed,this.error(`${this._name} client: couldn't create connection to server.`,e,"force"),n(e)}return this._onStart}createOnStartPromise(){let e,t;return[new Promise((n,i)=>{e=n,t=i}),e,t]}async initialize(e){this.refreshTrace(e,!1);const t=this._clientOptions.initializationOptions,[n,i]=void 0!==this._clientOptions.workspaceFolder?[this._clientOptions.workspaceFolder.uri.fsPath,[{uri:this._c2p.asUri(this._clientOptions.workspaceFolder.uri),name:this._clientOptions.workspaceFolder.name}]]:[this._clientGetRootPath(),null],r={processId:null,clientInfo:{name:s.env.appName,version:s.version},locale:this.getLocale(),rootPath:n||null,rootUri:n?this._c2p.asUri(s.Uri.file(n)):null,capabilities:this.computeClientCapabilities(),initializationOptions:l.func(t)?t():t,trace:a.Trace.toString(this._trace),workspaceFolders:i};if(this.fillInitializeParams(r),!this._clientOptions.progressOnInitialization)return this.doInitialize(e,r);{const t=h.generateUuid(),n=new p.ProgressPart(e,t);r.workDoneToken=t;try{const t=await this.doInitialize(e,r);return n.done(),t}catch(e){throw n.cancel(),e}}}async doInitialize(e,t){try{const n=await e.initialize(t);if(void 0!==n.capabilities.positionEncoding&&n.capabilities.positionEncoding!==a.PositionEncodingKind.UTF16)throw new Error(`Unsupported position encoding (${n.capabilities.positionEncoding}) received from server ${this.name}`);let i;this._initializeResult=n,this.$state=ie.Running,l.number(n.capabilities.textDocumentSync)?i=n.capabilities.textDocumentSync===a.TextDocumentSyncKind.None?{openClose:!1,change:a.TextDocumentSyncKind.None,save:void 0}:{openClose:!0,change:n.capabilities.textDocumentSync,save:{includeText:!1}}:void 0!==n.capabilities.textDocumentSync&&null!==n.capabilities.textDocumentSync&&(i=n.capabilities.textDocumentSync),this._capabilities=Object.assign({},n.capabilities,{resolvedTextDocumentSync:i}),e.onNotification(a.PublishDiagnosticsNotification.type,e=>this.handleDiagnostics(e)),e.onRequest(a.RegistrationRequest.type,e=>this.handleRegistrationRequest(e)),e.onRequest("client/registerFeature",e=>this.handleRegistrationRequest(e)),e.onRequest(a.UnregistrationRequest.type,e=>this.handleUnregistrationRequest(e)),e.onRequest("client/unregisterFeature",e=>this.handleUnregistrationRequest(e)),e.onRequest(a.ApplyWorkspaceEditRequest.type,e=>this.handleApplyWorkspaceEdit(e));for(const[t,n]of this._pendingNotificationHandlers)this._notificationDisposables.set(t,e.onNotification(t,n));this._pendingNotificationHandlers.clear();for(const[t,n]of this._pendingRequestHandlers)this._requestDisposables.set(t,e.onRequest(t,n));this._pendingRequestHandlers.clear();for(const[t,n]of this._pendingProgressHandlers)this._progressDisposables.set(t,e.onProgress(n.type,t,n.handler));return this._pendingProgressHandlers.clear(),await e.sendNotification(a.InitializedNotification.type,{}),this.hookFileEvents(e),this.hookConfigurationChanged(e),this.initializeFeatures(e),n}catch(t){throw this._clientOptions.initializationFailedHandler?this._clientOptions.initializationFailedHandler(t)?this.initialize(e):this.stop():t instanceof a.ResponseError&&t.data&&t.data.retry?s.window.showErrorMessage(t.message,{title:"Retry",id:"retry"}).then(t=>{t&&"retry"===t.id?this.initialize(e):this.stop()}):(t&&t.message&&s.window.showErrorMessage(t.message),this.error("Server initialization failed.",t),this.stop()),t}}_clientGetRootPath(){const e=s.workspace.workspaceFolders;if(!e||0===e.length)return;const t=e[0];return"file"===t.uri.scheme?t.uri.fsPath:void 0}stop(e=2e3){return this.shutdown(oe.Stop,e)}dispose(e=2e3){try{return this._disposed="disposing",this.stop(e)}finally{this._disposed="disposed"}}async shutdown(e,t=2e3){if(this.$state===ie.Stopped||this.$state===ie.Initial)return;if(this.$state===ie.Stopping){if(void 0!==this._onStop)return this._onStop;throw new Error("Client is stopping but no stop promise available.")}const n=this.activeConnection();if(void 0===n||this.$state!==ie.Running)throw new Error(`Client is not running and can't be stopped. It's current state is: ${this.$state}`);this._initializeResult=void 0,this.$state=ie.Stopping,this.cleanUp(e);const i=new Promise(e=>{(0,a.RAL)().timer.setTimeout(e,t)}),r=(async e=>(await e.shutdown(),await e.exit(),e))(n);return this._onStop=Promise.race([i,r]).then(e=>{if(void 0===e)throw this.error("Stopping server timed out",void 0,!1),new Error("Stopping the server timed out");e.end(),e.dispose()},e=>{throw this.error("Stopping server failed",e,!1),e}).finally(()=>{this.$state=ie.Stopped,e===oe.Stop&&this.cleanUpChannel(),this._onStart=void 0,this._onStop=void 0,this._connection=void 0,this._ignoredRegistrations.clear()})}cleanUp(e){this._fileEvents=[],this._fileEventDelayer.cancel();const t=this._listeners.splice(0,this._listeners.length);for(const e of t)e.dispose();this._syncedDocuments&&this._syncedDocuments.clear();for(const e of Array.from(this._features.entries()).map(e=>e[1]).reverse())e.clear();e!==oe.Stop&&e!==oe.Restart||void 0===this._diagnostics||(this._diagnostics.dispose(),this._diagnostics=void 0),void 0!==this._idleInterval&&(this._idleInterval.dispose(),this._idleInterval=void 0)}cleanUpChannel(){void 0!==this._outputChannel&&this._disposeOutputChannel&&(this._outputChannel.dispose(),this._outputChannel=void 0)}notifyFileEvent(e){const t=this;async function n(e){return t._fileEvents.push(e),t._fileEventDelayer.trigger(async()=>{const e=t._fileEvents;t._fileEvents=[];try{await t.sendNotification(a.DidChangeWatchedFilesNotification.type,{changes:e})}catch(n){throw t._fileEvents.push(...e),n}})}const i=this.clientOptions.middleware?.workspace;(i?.didChangeWatchedFile?i.didChangeWatchedFile(e,n):n(e)).catch(e=>{t.error("Notifying file events failed.",e)})}async sendPendingFullTextDocumentChanges(e){return this._pendingChangeSemaphore.lock(async()=>{try{const t=this._didChangeTextDocumentFeature.getPendingDocumentChanges(this._inFlightOpenNotifications);if(0===t.length)return;for(const n of t){const t=this.code2ProtocolConverter.asChangeTextDocumentParams(n);await e.sendNotification(a.DidChangeTextDocumentNotification.type,t),this._didChangeTextDocumentFeature.notificationSent(n,a.DidChangeTextDocumentNotification.type,t)}}catch(e){throw this.error("Sending pending changes failed",e,!1),e}})}triggerPendingChangeDelivery(){this._pendingChangeDelayer.trigger(async()=>{const e=this.activeConnection();void 0!==e?await this.sendPendingFullTextDocumentChanges(e):this.triggerPendingChangeDelivery()}).catch(e=>this.error("Delivering pending changes failed",e,!1))}_diagnosticQueue=new Map;_diagnosticQueueState={state:"idle"};handleDiagnostics(e){if(!this._diagnostics)return;const t=e.uri;"busy"===this._diagnosticQueueState.state&&this._diagnosticQueueState.document===t&&this._diagnosticQueueState.tokenSource.cancel(),this._diagnosticQueue.set(e.uri,e.diagnostics),this.triggerDiagnosticQueue()}triggerDiagnosticQueue(){(0,a.RAL)().timer.setImmediate(()=>{this.workDiagnosticQueue()})}workDiagnosticQueue(){if("busy"===this._diagnosticQueueState.state)return;const e=this._diagnosticQueue.entries().next();if(!0===e.done)return;const[t,n]=e.value;this._diagnosticQueue.delete(t);const i=new s.CancellationTokenSource;this._diagnosticQueueState={state:"busy",document:t,tokenSource:i},this._p2c.asDiagnostics(n,i.token).then(e=>{if(!i.token.isCancellationRequested){const n=this._p2c.asUri(t),i=this.clientOptions.middleware;i.handleDiagnostics?i.handleDiagnostics(n,e,(e,t)=>this.setDiagnostics(e,t)):this.setDiagnostics(n,e)}}).catch(e=>{this.error("Processing diagnostic queue failed.",e)}).finally(()=>{this._diagnosticQueueState={state:"idle"},this.triggerDiagnosticQueue()})}setDiagnostics(e,t){this._diagnostics&&this._diagnostics.set(e,t)}getLocale(){return s.env.language}async $start(){if(this.$state===ie.StartFailed)throw new Error("Previous start failed. Can't restart server.");await this.start();const e=this.activeConnection();if(void 0===e)throw new Error("Starting server failed");return e}async createConnection(){const e=await this.createMessageTransports(this._clientOptions.stdioEncoding||"utf8");return this._connection=function(e,t,n,i,r){const o=new le,s=(0,a.createProtocolConnection)(e,t,o,r);return s.onError(e=>{n(e[0],e[1],e[2])}),s.onClose(i),{listen:()=>s.listen(),sendRequest:s.sendRequest,onRequest:s.onRequest,hasPendingResponse:s.hasPendingResponse,sendNotification:s.sendNotification,onNotification:s.onNotification,onProgress:s.onProgress,sendProgress:s.sendProgress,trace:(e,t,n)=>{const i={sendNotification:!1,traceFormat:a.TraceFormat.Text};return void 0===n?s.trace(e,t,i):(l.boolean(n),s.trace(e,t,n))},initialize:e=>s.sendRequest(a.InitializeRequest.type,e),shutdown:()=>s.sendRequest(a.ShutdownRequest.type,void 0),exit:()=>s.sendNotification(a.ExitNotification.type),end:()=>s.end(),dispose:()=>s.dispose()}}(e.reader,e.writer,(e,t,n)=>{this.handleConnectionError(e,t,n).catch(e=>this.error("Handling connection error failed",e))},()=>{this.handleConnectionClosed().catch(e=>this.error("Handling connection close failed",e))},this._clientOptions.connectionOptions),this._connection}async handleConnectionClosed(){if(this.$state===ie.Stopped)return;try{void 0!==this._connection&&this._connection.dispose()}catch(e){}let e={action:Z.DoNotRestart};if(this.$state!==ie.Stopping)try{e=await this._clientOptions.errorHandler.closed()}catch(e){}this._connection=void 0,e.action===Z.DoNotRestart?(this.error(e.message??"Connection to server got closed. Server will not be restarted.",void 0,!0!==e.handled&&"force"),this.cleanUp(oe.Stop),this.$state===ie.Starting?this.$state=ie.StartFailed:this.$state=ie.Stopped,this._onStop=Promise.resolve(),this._onStart=void 0):e.action===Z.Restart&&(this.info(e.message??"Connection to server got closed. Server will restart.",!e.handled),this.cleanUp(oe.Restart),this.$state=ie.Initial,this._onStop=Promise.resolve(),this._onStart=void 0,this.start().catch(e=>this.error("Restarting server failed",e,"force")))}async handleConnectionError(e,t,n){const i=await this._clientOptions.errorHandler.error(e,t,n);i.action===Y.Shutdown?(this.error(i.message??`Client ${this._name}: connection to server is erroring.\n${e.message}\nShutting down server.`,void 0,!0!==i.handled&&"force"),this.stop().catch(e=>{this.error("Stopping server failed",e,!1)})):this.error(i.message??`Client ${this._name}: connection to server is erroring.\n${e.message}`,void 0,!0!==i.handled&&"force")}hookConfigurationChanged(e){this._listeners.push(s.workspace.onDidChangeConfiguration(()=>{this.refreshTrace(e,!0)}))}refreshTrace(e,t=!1){const n=s.workspace.getConfiguration(this._id);let i=a.Trace.Off,r=a.TraceFormat.Text;if(n){const e=n.get("trace.server","off");"string"==typeof e?i=a.Trace.fromString(e):(i=a.Trace.fromString(n.get("trace.server.verbosity","off")),r=a.TraceFormat.fromString(n.get("trace.server.format","text")))}this._trace=i,this._traceFormat=r,e.trace(this._trace,this._tracer,{sendNotification:t,traceFormat:this._traceFormat}).catch(e=>{this.error("Updating trace failed with error",e,!1)})}hookFileEvents(e){const t=this._clientOptions.synchronize.fileEvents;if(!t)return;let n;n=l.array(t)?t:[t],n&&this._dynamicFeatures.get(a.DidChangeWatchedFilesNotification.type.method).registerRaw(h.generateUuid(),n)}_features=[];_dynamicFeatures=new Map;registerFeatures(e){for(const t of e)this.registerFeature(t)}registerFeature(e){if(this._features.push(e),f.DynamicFeature.is(e)){const t=e.registrationType;this._dynamicFeatures.set(t.method,e)}}getFeature(e){return this._dynamicFeatures.get(e)}hasDedicatedTextSynchronizationFeature(e){const t=this.getFeature(a.NotebookDocumentSyncRegistrationType.method);return void 0!==t&&t instanceof m.NotebookDocumentSyncFeature&&t.handles(e)}registerBuiltinFeatures(){const e=new Map;this.registerFeature(new v.ConfigurationFeature(this)),this._didOpenTextDocumentFeature=new y.DidOpenTextDocumentFeature(this,this._syncedDocuments),this.registerFeature(this._didOpenTextDocumentFeature),this._didChangeTextDocumentFeature=new y.DidChangeTextDocumentFeature(this,e),this._didChangeTextDocumentFeature.onPendingChangeAdded(()=>{this.triggerPendingChangeDelivery()}),this.registerFeature(this._didChangeTextDocumentFeature),this.registerFeature(new y.WillSaveFeature(this)),this.registerFeature(new y.WillSaveWaitUntilFeature(this)),this.registerFeature(new y.DidSaveTextDocumentFeature(this)),this.registerFeature(new y.DidCloseTextDocumentFeature(this,this._syncedDocuments,e)),this.registerFeature(new X.FileSystemWatcherFeature(this,e=>this.notifyFileEvent(e))),this.registerFeature(new b.CompletionItemFeature(this)),this.registerFeature(new _.HoverFeature(this)),this.registerFeature(new D.SignatureHelpFeature(this)),this.registerFeature(new C.DefinitionFeature(this)),this.registerFeature(new T.ReferencesFeature(this)),this.registerFeature(new S.DocumentHighlightFeature(this)),this.registerFeature(new w.DocumentSymbolFeature(this)),this.registerFeature(new R.WorkspaceSymbolFeature(this)),this.registerFeature(new x.CodeActionFeature(this)),this.registerFeature(new E.CodeLensFeature(this)),this.registerFeature(new M.DocumentFormattingFeature(this)),this.registerFeature(new M.DocumentRangeFormattingFeature(this)),this.registerFeature(new M.DocumentOnTypeFormattingFeature(this)),this.registerFeature(new F.RenameFeature(this)),this.registerFeature(new q.DocumentLinkFeature(this)),this.registerFeature(new I.ExecuteCommandFeature(this)),this.registerFeature(new v.SyncConfigurationFeature(this)),this.registerFeature(new P.TypeDefinitionFeature(this)),this.registerFeature(new O.ImplementationFeature(this)),this.registerFeature(new k.ColorProviderFeature(this)),void 0===this.clientOptions.workspaceFolder&&this.registerFeature(new z.WorkspaceFoldersFeature(this)),this.registerFeature(new N.FoldingRangeFeature(this)),this.registerFeature(new j.DeclarationFeature(this)),this.registerFeature(new L.SelectionRangeFeature(this)),this.registerFeature(new J.ProgressFeature(this)),this.registerFeature(new A.CallHierarchyFeature(this)),this.registerFeature(new $.SemanticTokensFeature(this)),this.registerFeature(new H.LinkedEditingFeature(this)),this.registerFeature(new B.DidCreateFilesFeature(this)),this.registerFeature(new B.DidRenameFilesFeature(this)),this.registerFeature(new B.DidDeleteFilesFeature(this)),this.registerFeature(new B.WillCreateFilesFeature(this)),this.registerFeature(new B.WillRenameFilesFeature(this)),this.registerFeature(new B.WillDeleteFilesFeature(this)),this.registerFeature(new W.TypeHierarchyFeature(this)),this.registerFeature(new U.InlineValueFeature(this)),this.registerFeature(new K.InlayHintsFeature(this)),this.registerFeature(new g.DiagnosticFeature(this)),this.registerFeature(new m.NotebookDocumentSyncFeature(this))}registerProposedFeatures(){this.registerFeatures(se.createAll(this))}fillInitializeParams(e){for(const t of this._features)l.func(t.fillInitializeParams)&&t.fillInitializeParams(e)}computeClientCapabilities(){const e={};(0,f.ensure)(e,"workspace").applyEdit=!0;const t=(0,f.ensure)((0,f.ensure)(e,"workspace"),"workspaceEdit");t.documentChanges=!0,t.resourceOperations=[a.ResourceOperationKind.Create,a.ResourceOperationKind.Rename,a.ResourceOperationKind.Delete],t.failureHandling=a.FailureHandlingKind.TextOnlyTransactional,t.normalizesLineEndings=!0,t.changeAnnotationSupport={groupsOnLabel:!0},t.metadataSupport=!0,t.snippetEditSupport=!0;const n=(0,f.ensure)((0,f.ensure)(e,"textDocument"),"publishDiagnostics");n.relatedInformation=!0,n.versionSupport=!1,n.tagSupport={valueSet:[a.DiagnosticTag.Unnecessary,a.DiagnosticTag.Deprecated]},n.codeDescriptionSupport=!0,n.dataSupport=!0;const i=(0,f.ensure)(e,"window");(0,f.ensure)(i,"showMessage").messageActionItem={additionalPropertiesSupport:!0},(0,f.ensure)(i,"showDocument").support=!0;const r=(0,f.ensure)(e,"general");r.staleRequestSupport={cancel:!0,retryOnContentModified:Array.from(ue.RequestsToCancelOnContentModified)},r.regularExpressions={engine:"ECMAScript",version:"ES2020"},r.markdown={parser:"marked",version:"1.1.0"},r.positionEncodings=["utf-16"],this._clientOptions.markdown.supportHtml&&(r.markdown.allowedTags=["ul","li","p","code","blockquote","ol","h1","h2","h3","h4","h5","h6","hr","em","pre","table","thead","tbody","tr","th","td","div","del","a","strong","br","img","span"]);for(const t of this._features)t.fillClientCapabilities(e);return e}initializeFeatures(e){const t=this._clientOptions.documentSelector;for(const e of this._features)l.func(e.preInitialize)&&e.preInitialize(this._capabilities,t);for(const e of this._features)e.initialize(this._capabilities,t)}async handleRegistrationRequest(e){const t=this.clientOptions.middleware?.handleRegisterCapability;return t?t(e,e=>this.doRegisterCapability(e)):this.doRegisterCapability(e)}async doRegisterCapability(e){if(this.isRunning())for(const t of e.registrations){const e=this._dynamicFeatures.get(t.method);if(void 0===e)return Promise.reject(new Error(`No feature implementation for ${t.method} found. Registration failed.`));const n=t.registerOptions??{};n.documentSelector=n.documentSelector??this._clientOptions.documentSelector;const i={id:t.id,registerOptions:n};try{e.register(i)}catch(e){return Promise.reject(e)}}else for(const t of e.registrations)this._ignoredRegistrations.add(t.id)}async handleUnregistrationRequest(e){const t=this.clientOptions.middleware?.handleUnregisterCapability;return t?t(e,e=>this.doUnregisterCapability(e)):this.doUnregisterCapability(e)}async doUnregisterCapability(e){for(const t of e.unregisterations){if(this._ignoredRegistrations.has(t.id))continue;const e=this._dynamicFeatures.get(t.method);if(!e)return Promise.reject(new Error(`No feature implementation for ${t.method} found. Unregistration failed.`));e.unregister(t.id)}}async handleApplyWorkspaceEdit(e){const t=this.clientOptions.middleware?.workspace?.handleApplyEdit;if(t){const n=await t(e,e=>this.doHandleApplyWorkspaceEdit(e));return n instanceof a.ResponseError?Promise.reject(n):n}return this.doHandleApplyWorkspaceEdit(e)}workspaceEditLock=new d.Semaphore(1);async doHandleApplyWorkspaceEdit(e){const t=e.edit,n=await this.workspaceEditLock.lock(()=>this._p2c.asWorkspaceEdit(t)),i=new Map;s.workspace.textDocuments.forEach(e=>i.set(e.uri.toString(),e));let r=!1;if(t.documentChanges)for(const e of t.documentChanges)if(a.TextDocumentEdit.is(e)&&e.textDocument.version&&e.textDocument.version>=0){const t=this._p2c.asUri(e.textDocument.uri).toString(),n=i.get(t);if(n&&n.version!==e.textDocument.version){r=!0;break}}return r?Promise.resolve({applied:!1}):l.asPromise(s.workspace.applyEdit(n,{isRefactoring:e.metadata?.isRefactoring}).then(e=>({applied:e})))}static RequestsToCancelOnContentModified=new Set([a.SemanticTokensRequest.method,a.SemanticTokensRangeRequest.method,a.SemanticTokensDeltaRequest.method]);static CancellableResolveCalls=new Set([a.CompletionResolveRequest.method,a.CodeLensResolveRequest.method,a.CodeActionResolveRequest.method,a.InlayHintResolveRequest.method,a.DocumentLinkResolveRequest.method,a.WorkspaceSymbolResolveRequest.method]);handleFailedRequest(e,t,n,i,r=!0,o=!1){if(n instanceof a.ResponseError){if(n.code===a.ErrorCodes.PendingResponseRejected||n.code===a.ErrorCodes.ConnectionInactive)return i;if(n.code===a.LSPErrorCodes.RequestCancelled||n.code===a.LSPErrorCodes.ServerCancelled){if(void 0!==t&&t.isCancellationRequested&&!o)return i;throw void 0!==n.data?new f.LSPCancellationError(n.data):new s.CancellationError}if(n.code===a.LSPErrorCodes.ContentModified){if(ue.RequestsToCancelOnContentModified.has(e.method)||ue.CancellableResolveCalls.has(e.method))throw new s.CancellationError;return i}}throw this.error(`Request ${e.method} failed.`,n,r),n}}t.BaseLanguageClient=ue,t.LanguageClient=class extends ue{serverOptions;constructor(e,t,n,i){super(e,t,i),this.serverOptions=n}async createMessageTransports(e){return this.serverOptions()}};class le{error(e){(0,a.RAL)().console.error(e)}warn(e){(0,a.RAL)().console.warn(e)}info(e){(0,a.RAL)().console.info(e)}log(e){(0,a.RAL)().console.log(e)}}!function(e){e.createAll=function(e){return[new V.InlineCompletionItemFeature(e),new G.TextDocumentContentFeature(e)]}}(se||(t.ProposedFeatures=se={}))},744:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlineValueRefreshRequest=t.InlineValueRequest=void 0;const i=n(7096);var r,o;!function(e){e.method="textDocument/inlineValue",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.InlineValueRequest=r={})),function(e){e.method="workspace/inlineValue/refresh",e.messageDirection=i.MessageDirection.serverToClient,e.type=new i.ProtocolRequestType0(e.method)}(o||(t.InlineValueRefreshRequest=o={}))},857:e=>{"use strict";e.exports=require("os")},986:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentHighlightFeature=void 0;const s=n(1398),a=n(3434),c=n(9810),u=o(n(8820));class l extends c.TextDocumentLanguageFeature{constructor(e){super(e,a.DocumentHighlightRequest.type)}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"textDocument"),"documentHighlight").dynamicRegistration=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.documentHighlightProvider);n&&this.register({id:u.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideDocumentHighlights:(e,t,n)=>{const i=this._client,r=(e,t,n)=>i.sendRequest(a.DocumentHighlightRequest.type,i.code2ProtocolConverter.asTextDocumentPositionParams(e,t),n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asDocumentHighlights(e,n),e=>i.handleFailedRequest(a.DocumentHighlightRequest.type,n,e,null)),o=i.middleware;return o.provideDocumentHighlights?o.provideDocumentHighlights(e,t,n,r):r(e,t,n)}};return[s.languages.registerDocumentHighlightProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),n),n]}}t.DocumentHighlightFeature=l},1027:(e,t)=>{"use strict";function n(e){return"string"==typeof e||e instanceof String}function i(e){return"function"==typeof e}function r(e){return Array.isArray(e)}function o(e){return e&&i(e.then)}Object.defineProperty(t,"__esModule",{value:!0}),t.asPromise=t.thenable=t.typedArray=t.stringArray=t.array=t.func=t.error=t.number=t.string=t.boolean=void 0,t.boolean=function(e){return!0===e||!1===e},t.string=n,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=i,t.array=r,t.stringArray=function(e){return r(e)&&e.every(e=>n(e))},t.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)},t.thenable=o,t.asPromise=function(e){return e instanceof Promise?e:o(e)?new Promise((t,n)=>{e.then(e=>t(e),e=>n(e))}):Promise.resolve(e)}},1121:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TypeDefinitionRequest=void 0;const i=n(7096);var r;!function(e){e.method="textDocument/typeDefinition",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.TypeDefinitionRequest=r={}))},1141:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.terminate=void 0;const s=o(n(5317)),a=n(6928),c="win32"===process.platform,u="darwin"===process.platform,l="linux"===process.platform;t.terminate=function(e,t){if(c)try{const n={stdio:["pipe","pipe","ignore"]};return t&&(n.cwd=t),s.execFileSync("taskkill",["/T","/F","/PID",e.pid.toString()],n),!0}catch(e){return!1}else{if(!l&&!u)return e.kill("SIGKILL"),!0;try{const t=(0,a.join)(__dirname,"terminateProcess.sh");return!s.spawnSync(t,[e.pid.toString()]).error}catch(e){return!1}}}},1332:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=o(n(1398));class a extends s.SymbolInformation{data;hasRange;constructor(e,t,n,i,r){const o=!(i instanceof s.Uri);super(e,t,n,o?i:new s.Location(i,new s.Range(0,0,0,0))),this.hasRange=o,void 0!==r&&(this.data=r)}}t.default=a},1398:e=>{"use strict";e.exports=require("vscode")},1423:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticTokensFeature=void 0;const s=o(n(1398)),a=n(3434),c=n(9810),u=o(n(1027));class l extends c.TextDocumentLanguageFeature{constructor(e){super(e,a.SemanticTokensRegistrationType.type)}fillClientCapabilities(e){const t=(0,c.ensure)((0,c.ensure)(e,"textDocument"),"semanticTokens");t.dynamicRegistration=!0,t.tokenTypes=[a.SemanticTokenTypes.namespace,a.SemanticTokenTypes.type,a.SemanticTokenTypes.class,a.SemanticTokenTypes.enum,a.SemanticTokenTypes.interface,a.SemanticTokenTypes.struct,a.SemanticTokenTypes.typeParameter,a.SemanticTokenTypes.parameter,a.SemanticTokenTypes.variable,a.SemanticTokenTypes.property,a.SemanticTokenTypes.enumMember,a.SemanticTokenTypes.event,a.SemanticTokenTypes.function,a.SemanticTokenTypes.method,a.SemanticTokenTypes.macro,a.SemanticTokenTypes.keyword,a.SemanticTokenTypes.modifier,a.SemanticTokenTypes.comment,a.SemanticTokenTypes.string,a.SemanticTokenTypes.number,a.SemanticTokenTypes.regexp,a.SemanticTokenTypes.operator,a.SemanticTokenTypes.decorator,a.SemanticTokenTypes.label],t.tokenModifiers=[a.SemanticTokenModifiers.declaration,a.SemanticTokenModifiers.definition,a.SemanticTokenModifiers.readonly,a.SemanticTokenModifiers.static,a.SemanticTokenModifiers.deprecated,a.SemanticTokenModifiers.abstract,a.SemanticTokenModifiers.async,a.SemanticTokenModifiers.modification,a.SemanticTokenModifiers.documentation,a.SemanticTokenModifiers.defaultLibrary],t.formats=[a.TokenFormat.Relative],t.requests={range:!0,full:{delta:!0}},t.multilineTokenSupport=!1,t.overlappingTokenSupport=!1,t.serverCancelSupport=!0,t.augmentsSyntaxTokens=!0,(0,c.ensure)((0,c.ensure)(e,"workspace"),"semanticTokens").refreshSupport=!0}initialize(e,t){this._client.onRequest(a.SemanticTokensRefreshRequest.type,async()=>{for(const e of this.getAllProviders())e.onDidChangeSemanticTokensEmitter.fire()});const[n,i]=this.getRegistration(t,e.semanticTokensProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=e.documentSelector,n=u.boolean(e.full)?e.full:void 0!==e.full,i=void 0!==e.full&&"boolean"!=typeof e.full&&!0===e.full.delta,r=new s.EventEmitter,o=n?{onDidChangeSemanticTokens:r.event,provideDocumentSemanticTokens:(e,t)=>{const n=this._client,i=n.middleware,r=(e,t)=>{const i={textDocument:n.code2ProtocolConverter.asTextDocumentIdentifier(e)};return n.sendRequest(a.SemanticTokensRequest.type,i,t).then(e=>t.isCancellationRequested?null:n.protocol2CodeConverter.asSemanticTokens(e,t),e=>n.handleFailedRequest(a.SemanticTokensRequest.type,t,e,null))};return i.provideDocumentSemanticTokens?i.provideDocumentSemanticTokens(e,t,r):r(e,t)},provideDocumentSemanticTokensEdits:i?(e,t,n)=>{const i=this._client,r=i.middleware,o=(e,t,n)=>{const r={textDocument:i.code2ProtocolConverter.asTextDocumentIdentifier(e),previousResultId:t};return i.sendRequest(a.SemanticTokensDeltaRequest.type,r,n).then(async e=>n.isCancellationRequested?null:a.SemanticTokens.is(e)?await i.protocol2CodeConverter.asSemanticTokens(e,n):await i.protocol2CodeConverter.asSemanticTokensEdits(e,n),e=>i.handleFailedRequest(a.SemanticTokensDeltaRequest.type,n,e,null))};return r.provideDocumentSemanticTokensEdits?r.provideDocumentSemanticTokensEdits(e,t,n,o):o(e,t,n)}:void 0}:void 0,c=!0===e.range?{provideDocumentRangeSemanticTokens:(e,t,n)=>{const i=this._client,r=i.middleware,o=(e,t,n)=>{const r={textDocument:i.code2ProtocolConverter.asTextDocumentIdentifier(e),range:i.code2ProtocolConverter.asRange(t)};return i.sendRequest(a.SemanticTokensRangeRequest.type,r,n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asSemanticTokens(e,n),e=>i.handleFailedRequest(a.SemanticTokensRangeRequest.type,n,e,null))};return r.provideDocumentRangeSemanticTokens?r.provideDocumentRangeSemanticTokens(e,t,n,o):o(e,t,n)}}:void 0,l=[],d=this._client,h=d.protocol2CodeConverter.asSemanticTokensLegend(e.legend),p=d.protocol2CodeConverter.asDocumentSelector(t);return void 0!==o&&l.push(s.languages.registerDocumentSemanticTokensProvider(p,o,h)),void 0!==c&&l.push(s.languages.registerDocumentRangeSemanticTokensProvider(p,c,h)),[new s.Disposable(()=>l.forEach(e=>e.dispose())),{range:c,full:o,onDidChangeSemanticTokensEmitter:r}]}}t.SemanticTokensFeature=l},1722:(e,t,n)=>{const i=n(8395);e.exports=(e,t,n)=>{try{t=new i(t,n)}catch(e){return!1}return t.test(e)}},1936:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=o(n(1398));class a extends s.CodeLens{data;constructor(e){super(e)}}t.default=a},1977:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ReadableStreamMessageReader=t.AbstractMessageReader=t.MessageReader=void 0;const a=s(n(9042)),c=o(n(6357)),u=n(6712),l=n(7975);var d,h;!function(e){e.is=function(e){const t=e;return t&&c.func(t.listen)&&c.func(t.dispose)&&c.func(t.onError)&&c.func(t.onClose)&&c.func(t.onPartialMessage)}}(d||(t.MessageReader=d={}));class p{errorEmitter;closeEmitter;partialMessageEmitter;constructor(){this.errorEmitter=new u.Emitter,this.closeEmitter=new u.Emitter,this.partialMessageEmitter=new u.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e){this.errorEmitter.fire(this.asError(e))}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}get onPartialMessage(){return this.partialMessageEmitter.event}firePartialMessage(e){this.partialMessageEmitter.fire(e)}asError(e){return e instanceof Error?e:new Error(`Reader received error. Reason: ${c.string(e.message)?e.message:"unknown"}`)}}t.AbstractMessageReader=p,function(e){e.fromOptions=function(e){let t,n;const i=new Map;let r;const o=new Map;if(void 0===e||"string"==typeof e)t=e??"utf-8";else{if(t=e.charset??"utf-8",void 0!==e.contentDecoder&&(n=e.contentDecoder,i.set(n.name,n)),void 0!==e.contentDecoders)for(const t of e.contentDecoders)i.set(t.name,t);if(void 0!==e.contentTypeDecoder&&(r=e.contentTypeDecoder,o.set(r.name,r)),void 0!==e.contentTypeDecoders)for(const t of e.contentTypeDecoders)o.set(t.name,t)}return void 0===r&&(r=(0,a.default)().applicationJson.decoder,o.set(r.name,r)),{charset:t,contentDecoder:n,contentDecoders:i,contentTypeDecoder:r,contentTypeDecoders:o}}}(h||(h={})),t.ReadableStreamMessageReader=class extends p{readable;options;callback;nextMessageLength;messageToken;buffer;partialMessageTimer;_partialMessageTimeout;readSemaphore;constructor(e,t){super(),this.readable=e,this.options=h.fromOptions(t),this.buffer=(0,a.default)().messageBuffer.create(this.options.charset),this._partialMessageTimeout=1e4,this.nextMessageLength=-1,this.messageToken=0,this.readSemaphore=new l.Semaphore(1)}set partialMessageTimeout(e){this._partialMessageTimeout=e}get partialMessageTimeout(){return this._partialMessageTimeout}listen(e){this.nextMessageLength=-1,this.messageToken=0,this.partialMessageTimer=void 0,this.callback=e;const t=this.readable.onData(e=>{this.onData(e)});return this.readable.onError(e=>this.fireError(e)),this.readable.onClose(()=>this.fireClose()),t}onData(e){try{for(this.buffer.append(e);;){if(-1===this.nextMessageLength){const e=this.buffer.tryReadHeaders(!0);if(!e)return;const t=e.get("content-length");if(!t)return void this.fireError(new Error(`Header must provide a Content-Length property.\n${JSON.stringify(Object.fromEntries(e))}`));const n=parseInt(t);if(isNaN(n))return void this.fireError(new Error(`Content-Length value must be a number. Got ${t}`));this.nextMessageLength=n}const e=this.buffer.tryReadBody(this.nextMessageLength);if(void 0===e)return void this.setPartialMessageTimer();this.clearPartialMessageTimer(),this.nextMessageLength=-1,this.readSemaphore.lock(async()=>{const t=void 0!==this.options.contentDecoder?await this.options.contentDecoder.decode(e):e,n=await this.options.contentTypeDecoder.decode(t,this.options);this.callback(n)}).catch(e=>{this.fireError(e)})}}catch(e){this.fireError(e)}}clearPartialMessageTimer(){this.partialMessageTimer&&(this.partialMessageTimer.dispose(),this.partialMessageTimer=void 0)}setPartialMessageTimer(){this.clearPartialMessageTimer(),this._partialMessageTimeout<=0||(this.partialMessageTimer=(0,a.default)().timer.setTimeout((e,t)=>{this.partialMessageTimer=void 0,e===this.messageToken&&(this.firePartialMessage({messageToken:e,waitingTime:t}),this.setPartialMessageTimer())},this._partialMessageTimeout,this.messageToken,this._partialMessageTimeout))}}},2077:(e,t,n)=>{const i=n(5380);e.exports=(e,t,n)=>i(e,t,n)>=0},2087:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t},s=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.createMessageConnection=t.createServerSocketTransport=t.createClientSocketTransport=t.createServerPipeTransport=t.createClientPipeTransport=t.generateRandomPipeName=t.StreamMessageWriter=t.StreamMessageReader=t.SocketMessageWriter=t.SocketMessageReader=t.PortMessageWriter=t.PortMessageReader=t.IPCMessageWriter=t.IPCMessageReader=void 0;const c=a(n(3527));c.default.install();const u=o(n(6928)),l=o(n(857)),d=o(n(9896)),h=n(6982),p=n(9278),f=n(9765);s(n(9765),t);class g extends f.AbstractMessageReader{process;constructor(e){super(),this.process=e;const t=this.process;t.on("error",e=>this.fireError(e)),t.on("close",()=>this.fireClose())}listen(e){return this.process.on("message",e),f.Disposable.create(()=>this.process.off("message",e))}}t.IPCMessageReader=g;class m extends f.AbstractMessageWriter{process;errorCount;constructor(e){super(),this.process=e,this.errorCount=0;const t=this.process;t.on("error",e=>this.fireError(e)),t.on("close",()=>this.fireClose)}write(e){try{return"function"==typeof this.process.send&&this.process.send(e,void 0,void 0,t=>{t?(this.errorCount++,this.handleError(t,e)):this.errorCount=0}),Promise.resolve()}catch(t){return this.handleError(t,e),Promise.reject(t)}}handleError(e,t){this.errorCount++,this.fireError(e,t,this.errorCount)}end(){}}t.IPCMessageWriter=m;class v extends f.AbstractMessageReader{onData;constructor(e){super(),this.onData=new f.Emitter,e.on("close",()=>this.fireClose),e.on("error",e=>this.fireError(e)),e.on("message",e=>{this.onData.fire(e)})}listen(e){return this.onData.event(e)}}t.PortMessageReader=v;class y extends f.AbstractMessageWriter{port;errorCount;constructor(e){super(),this.port=e,this.errorCount=0,e.on("close",()=>this.fireClose()),e.on("error",e=>this.fireError(e))}write(e){try{return this.port.postMessage(e),Promise.resolve()}catch(t){return this.handleError(t,e),Promise.reject(t)}}handleError(e,t){this.errorCount++,this.fireError(e,t,this.errorCount)}end(){}}t.PortMessageWriter=y;class b extends f.ReadableStreamMessageReader{constructor(e,t="utf-8"){super((0,c.default)().stream.asReadableStream(e),t)}}t.SocketMessageReader=b;class _ extends f.WriteableStreamMessageWriter{socket;constructor(e,t){super((0,c.default)().stream.asWritableStream(e),t),this.socket=e}dispose(){super.dispose(),this.socket.destroy()}}t.SocketMessageWriter=_;class C extends f.ReadableStreamMessageReader{constructor(e,t){super((0,c.default)().stream.asReadableStream(e),t)}}t.StreamMessageReader=C;class D extends f.WriteableStreamMessageWriter{constructor(e,t){super((0,c.default)().stream.asWritableStream(e),t)}}t.StreamMessageWriter=D;const S=process.env.XDG_RUNTIME_DIR,w=new Map([["linux",107],["darwin",103]]);t.generateRandomPipeName=function(){if("win32"===process.platform)return`\\\\.\\pipe\\lsp-${(0,h.randomBytes)(16).toString("hex")}-sock`;let e=32;const t=d.realpathSync(S??l.tmpdir()),n=w.get(process.platform);if(void 0!==n&&(e=Math.min(n-t.length-9,e)),e<16)throw new Error(`Unable to generate a random pipe name with ${e} characters.`);const i=(0,h.randomBytes)(Math.floor(e/2)).toString("hex");return u.join(t,`lsp-${i}.sock`)},t.createClientPipeTransport=function(e,t="utf-8"){let n;const i=new Promise((e,t)=>{n=e});return new Promise((r,o)=>{const s=(0,p.createServer)(e=>{s.close(),n([new b(e,t),new _(e,t)])});s.on("error",o),s.listen(e,()=>{s.removeListener("error",o),r({onConnected:()=>i})})})},t.createServerPipeTransport=function(e,t="utf-8"){const n=(0,p.createConnection)(e);return[new b(n,t),new _(n,t)]},t.createClientSocketTransport=function(e,t="utf-8"){let n;const i=new Promise((e,t)=>{n=e});return new Promise((r,o)=>{const s=(0,p.createServer)(e=>{s.close(),n([new b(e,t),new _(e,t)])});s.on("error",o),s.listen(e,"127.0.0.1",()=>{s.removeListener("error",o),r({onConnected:()=>i})})})},t.createServerSocketTransport=function(e,t="utf-8"){const n=(0,p.createConnection)(e,"127.0.0.1");return[new b(n,t),new _(n,t)]},t.createMessageConnection=function(e,t,n,i){n||(n=f.NullLogger);const r=function(e){const t=e;return void 0!==t.read&&void 0!==t.addListener}(e)?new C(e):e,o=function(e){const t=e;return void 0!==t.write&&void 0!==t.addListener}(t)?new D(t):t;return f.ConnectionStrategy.is(i)&&(i={connectionStrategy:i}),(0,f.createMessageConnection)(r,o,n,i)}},2114:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.CodeActionFeature=void 0;const s=n(1398),a=n(3434),c=o(n(8820)),u=n(9810);class l extends u.TextDocumentLanguageFeature{constructor(e){super(e,a.CodeActionRequest.type)}fillClientCapabilities(e){const t=(0,u.ensure)((0,u.ensure)(e,"textDocument"),"codeAction");t.dynamicRegistration=!0,t.isPreferredSupport=!0,t.disabledSupport=!0,t.dataSupport=!0,t.resolveSupport={properties:["edit","command"]},t.codeActionLiteralSupport={codeActionKind:{valueSet:[a.CodeActionKind.Empty,a.CodeActionKind.QuickFix,a.CodeActionKind.Refactor,a.CodeActionKind.RefactorExtract,a.CodeActionKind.RefactorInline,a.CodeActionKind.RefactorMove,a.CodeActionKind.RefactorRewrite,a.CodeActionKind.Source,a.CodeActionKind.SourceOrganizeImports,a.CodeActionKind.Notebook]}},t.honorsChangeAnnotations=!0,t.documentationSupport=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.codeActionProvider);n&&this.register({id:c.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideCodeActions:(e,t,n,i)=>{const r=this._client,o=async(e,t,n,i)=>{const o={textDocument:r.code2ProtocolConverter.asTextDocumentIdentifier(e),range:r.code2ProtocolConverter.asRange(t),context:r.code2ProtocolConverter.asCodeActionContextSync(n)};return r.sendRequest(a.CodeActionRequest.type,o,i).then(e=>i.isCancellationRequested||null==e?null:r.protocol2CodeConverter.asCodeActionResult(e,i),e=>r.handleFailedRequest(a.CodeActionRequest.type,i,e,null))},s=r.middleware;return s.provideCodeActions?s.provideCodeActions(e,t,n,i,o):o(e,t,n,i)},resolveCodeAction:e.resolveProvider?(e,t)=>{const n=this._client,i=this._client.middleware,r=async(e,t)=>n.sendRequest(a.CodeActionResolveRequest.type,n.code2ProtocolConverter.asCodeActionSync(e),t).then(i=>t.isCancellationRequested?e:n.protocol2CodeConverter.asCodeAction(i,t),i=>n.handleFailedRequest(a.CodeActionResolveRequest.type,t,i,e));return i.resolveCodeAction?i.resolveCodeAction(e,t,r):r(e,t)}:void 0};return[s.languages.registerCodeActionsProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),n,this.getMetadata(e)),n]}getMetadata(e){if(void 0!==e.codeActionKinds||void 0!==e.documentation)return{providedCodeActionKinds:this._client.protocol2CodeConverter.asCodeActionKinds(e.codeActionKinds),documentation:this._client.protocol2CodeConverter.asCodeActionDocumentations(e.documentation)}}}t.CodeActionFeature=l},2164:(e,t,n)=>{var i=n(8509);e.exports=function(e){return e?("{}"===e.substr(0,2)&&(e="\\{\\}"+e.substr(2)),m(function(e){return e.split("\\\\").join(r).split("\\{").join(o).split("\\}").join(s).split("\\,").join(a).split("\\.").join(c)}(e),!0).map(l)):[]};var r="\0SLASH"+Math.random()+"\0",o="\0OPEN"+Math.random()+"\0",s="\0CLOSE"+Math.random()+"\0",a="\0COMMA"+Math.random()+"\0",c="\0PERIOD"+Math.random()+"\0";function u(e){return parseInt(e,10)==e?parseInt(e,10):e.charCodeAt(0)}function l(e){return e.split(r).join("\\").split(o).join("{").split(s).join("}").split(a).join(",").split(c).join(".")}function d(e){if(!e)return[""];var t=[],n=i("{","}",e);if(!n)return e.split(",");var r=n.pre,o=n.body,s=n.post,a=r.split(",");a[a.length-1]+="{"+o+"}";var c=d(s);return s.length&&(a[a.length-1]+=c.shift(),a.push.apply(a,c)),t.push.apply(t,a),t}function h(e){return"{"+e+"}"}function p(e){return/^-?0\d/.test(e)}function f(e,t){return e<=t}function g(e,t){return e>=t}function m(e,t){var n=[],r=i("{","}",e);if(!r)return[e];var o=r.pre,a=r.post.length?m(r.post,!1):[""];if(/\$$/.test(r.pre))for(var c=0;c<a.length;c++){var l=o+"{"+r.body+"}"+a[c];n.push(l)}else{var v,y,b=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(r.body),_=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(r.body),C=b||_,D=r.body.indexOf(",")>=0;if(!C&&!D)return r.post.match(/,.*\}/)?m(e=r.pre+"{"+r.body+s+r.post):[e];if(C)v=r.body.split(/\.\./);else if(1===(v=d(r.body)).length&&1===(v=m(v[0],!1).map(h)).length)return a.map(function(e){return r.pre+v[0]+e});if(C){var S=u(v[0]),w=u(v[1]),R=Math.max(v[0].length,v[1].length),T=3==v.length?Math.abs(u(v[2])):1,P=f;w<S&&(T*=-1,P=g);var O=v.some(p);y=[];for(var k=S;P(k,w);k+=T){var x;if(_)"\\"===(x=String.fromCharCode(k))&&(x="");else if(x=String(k),O){var E=R-x.length;if(E>0){var M=new Array(E+1).join("0");x=k<0?"-"+M+x.slice(1):M+x}}y.push(x)}}else{y=[];for(var F=0;F<v.length;F++)y.push.apply(y,m(v[F],!1))}for(F=0;F<y.length;F++)for(c=0;c<a.length;c++)l=o+y[F]+a[c],(!t||C||l)&&n.push(l)}return n}},2434:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ImplementationRequest=void 0;const i=n(7096);var r;!function(e){e.method="textDocument/implementation",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.ImplementationRequest=r={}))},2549:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.WriteableStreamMessageWriter=t.AbstractMessageWriter=t.MessageWriter=void 0;const a=s(n(9042)),c=o(n(6357)),u=n(7975),l=n(6712);var d,h;!function(e){e.is=function(e){const t=e;return t&&c.func(t.dispose)&&c.func(t.onClose)&&c.func(t.onError)&&c.func(t.write)}}(d||(t.MessageWriter=d={}));class p{errorEmitter;closeEmitter;constructor(){this.errorEmitter=new l.Emitter,this.closeEmitter=new l.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e,t,n){this.errorEmitter.fire([this.asError(e),t,n])}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}asError(e){return e instanceof Error?e:new Error(`Writer received error. Reason: ${c.string(e.message)?e.message:"unknown"}`)}}t.AbstractMessageWriter=p,function(e){e.fromOptions=function(e){return void 0===e||"string"==typeof e?{charset:e??"utf-8",contentTypeEncoder:(0,a.default)().applicationJson.encoder}:{charset:e.charset??"utf-8",contentEncoder:e.contentEncoder,contentTypeEncoder:e.contentTypeEncoder??(0,a.default)().applicationJson.encoder}}}(h||(h={})),t.WriteableStreamMessageWriter=class extends p{writable;options;errorCount;writeSemaphore;constructor(e,t){super(),this.writable=e,this.options=h.fromOptions(t),this.errorCount=0,this.writeSemaphore=new u.Semaphore(1),this.writable.onError(e=>this.fireError(e)),this.writable.onClose(()=>this.fireClose())}async write(e){return this.writeSemaphore.lock(async()=>this.options.contentTypeEncoder.encode(e,this.options).then(e=>void 0!==this.options.contentEncoder?this.options.contentEncoder.encode(e):e).then(t=>{const n=[];return n.push("Content-Length: ",t.byteLength.toString(),"\r\n"),n.push("\r\n"),this.doWrite(e,n,t)},e=>{throw this.fireError(e),e}))}async doWrite(e,t,n){try{return await this.writable.write(t.join(""),"ascii"),this.writable.write(n)}catch(t){return this.handleError(t,e),Promise.reject(t)}}handleError(e,t){this.errorCount++,this.fireError(e,t,this.errorCount)}end(){this.writable.end()}}},2661:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.LinkedEditingFeature=void 0;const s=o(n(1398)),a=o(n(3434)),c=n(9810);class u extends c.TextDocumentLanguageFeature{constructor(e){super(e,a.LinkedEditingRangeRequest.type)}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"textDocument"),"linkedEditingRange").dynamicRegistration=!0}initialize(e,t){const[n,i]=this.getRegistration(t,e.linkedEditingRangeProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=e.documentSelector,n={provideLinkedEditingRanges:(e,t,n)=>{const i=this._client,r=(e,t,n)=>i.sendRequest(a.LinkedEditingRangeRequest.type,i.code2ProtocolConverter.asTextDocumentPositionParams(e,t),n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asLinkedEditingRanges(e,n),e=>i.handleFailedRequest(a.LinkedEditingRangeRequest.type,n,e,null)),o=i.middleware;return o.provideLinkedEditingRange?o.provideLinkedEditingRange(e,t,n,r):r(e,t,n)}};return[this.registerProvider(t,n),n]}registerProvider(e,t){return s.languages.registerLinkedEditingRangeProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}}t.LinkedEditingFeature=u},2685:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.DiagnosticPullMode=t.vsdiag=void 0,r(n(3434),t),r(n(9810),t);var o=n(7034);Object.defineProperty(t,"vsdiag",{enumerable:!0,get:function(){return o.vsdiag}}),Object.defineProperty(t,"DiagnosticPullMode",{enumerable:!0,get:function(){return o.DiagnosticPullMode}}),r(n(710),t)},2758:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.InlineCompletionItemFeature=void 0;const s=n(1398),a=n(3434),c=n(9810),u=o(n(8820));class l extends c.TextDocumentLanguageFeature{constructor(e){super(e,a.InlineCompletionRequest.type)}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"textDocument"),"inlineCompletion").dynamicRegistration=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.inlineCompletionProvider);n&&this.register({id:u.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideInlineCompletionItems:(e,t,n,i)=>{const r=this._client,o=this._client.middleware,s=(e,t,n,i)=>r.sendRequest(a.InlineCompletionRequest.type,r.code2ProtocolConverter.asInlineCompletionParams(e,t,n),i).then(e=>i.isCancellationRequested?null:r.protocol2CodeConverter.asInlineCompletionResult(e,i),e=>r.handleFailedRequest(a.InlineCompletionRequest.type,i,e,null));return o.provideInlineCompletionItems?o.provideInlineCompletionItems(e,t,n,i,s):s(e,t,n,i)}};return[s.languages.registerInlineCompletionItemProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),n),n]}}t.InlineCompletionItemFeature=l},2795:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MonikerRequest=t.MonikerKind=t.UniquenessLevel=void 0;const i=n(7096);var r,o,s;!function(e){e.document="document",e.project="project",e.group="group",e.scheme="scheme",e.global="global"}(r||(t.UniquenessLevel=r={})),function(e){e.$import="import",e.$export="export",e.local="local"}(o||(t.MonikerKind=o={})),function(e){e.method="textDocument/moniker",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(s||(t.MonikerRequest=s={}))},2837:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.ExecuteCommandFeature=void 0;const s=n(1398),a=n(3434),c=o(n(8820)),u=n(9810);t.ExecuteCommandFeature=class{_client;_commands;constructor(e){this._client=e,this._commands=new Map}getState(){return{kind:"workspace",id:this.registrationType.method,registrations:this._commands.size>0}}get registrationType(){return a.ExecuteCommandRequest.type}fillClientCapabilities(e){(0,u.ensure)((0,u.ensure)(e,"workspace"),"executeCommand").dynamicRegistration=!0}initialize(e){e.executeCommandProvider&&this.register({id:c.generateUuid(),registerOptions:Object.assign({},e.executeCommandProvider)})}register(e){const t=this._client,n=t.middleware,i=(e,n)=>{const i={command:e,arguments:n};return t.sendRequest(a.ExecuteCommandRequest.type,i).then(void 0,e=>t.handleFailedRequest(a.ExecuteCommandRequest.type,void 0,e,void 0))};if(e.registerOptions.commands){const t=[];for(const r of e.registerOptions.commands)t.push(s.commands.registerCommand(r,(...e)=>n.executeCommand?n.executeCommand(r,e,i):i(r,e)));this._commands.set(e.id,t)}}unregister(e){const t=this._commands.get(e);t&&(this._commands.delete(e),t.forEach(e=>e.dispose()))}clear(){this._commands.forEach(e=>{e.forEach(e=>e.dispose())}),this._commands.clear()}}},2855:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FoldingRangeFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);class s extends o.TextDocumentLanguageFeature{constructor(e){super(e,r.FoldingRangeRequest.type)}fillClientCapabilities(e){const t=(0,o.ensure)((0,o.ensure)(e,"textDocument"),"foldingRange");t.dynamicRegistration=!0,t.rangeLimit=5e3,t.lineFoldingOnly=!0,t.foldingRangeKind={valueSet:[r.FoldingRangeKind.Comment,r.FoldingRangeKind.Imports,r.FoldingRangeKind.Region]},t.foldingRange={collapsedText:!1},(0,o.ensure)((0,o.ensure)(e,"workspace"),"foldingRange").refreshSupport=!0}initialize(e,t){this._client.onRequest(r.FoldingRangeRefreshRequest.type,async()=>{for(const e of this.getAllProviders())e.onDidChangeFoldingRange.fire()});const[n,i]=this.getRegistration(t,e.foldingRangeProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=e.documentSelector,n=new i.EventEmitter,o={onDidChangeFoldingRanges:n.event,provideFoldingRanges:(e,t,n)=>{const i=this._client,o=(e,t,n)=>{const o={textDocument:i.code2ProtocolConverter.asTextDocumentIdentifier(e)};return i.sendRequest(r.FoldingRangeRequest.type,o,n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asFoldingRanges(e,n),e=>i.handleFailedRequest(r.FoldingRangeRequest.type,n,e,null))},s=i.middleware;return s.provideFoldingRanges?s.provideFoldingRanges(e,t,n,o):o(e,0,n)}};return[i.languages.registerFoldingRangeProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),o),{provider:o,onDidChangeFoldingRange:n}]}}t.FoldingRangeFeature=s},2946:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=o(n(1398));class a extends s.CodeAction{data;constructor(e,t){super(e),this.data=t}}t.default=a},3167:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Disposable=void 0,function(e){e.create=function(e){return{dispose:e}}}(n||(t.Disposable=n={}))},3219:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DidChangeWorkspaceFoldersNotification=t.WorkspaceFoldersRequest=void 0;const i=n(7096);var r,o;!function(e){e.method="workspace/workspaceFolders",e.messageDirection=i.MessageDirection.serverToClient,e.type=new i.ProtocolRequestType0(e.method)}(r||(t.WorkspaceFoldersRequest=r={})),function(e){e.method="workspace/didChangeWorkspaceFolders",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolNotificationType(e.method)}(o||(t.DidChangeWorkspaceFoldersNotification=o={}))},3286:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.createProtocolConnection=void 0;const o=n(2087);r(n(2087),t),r(n(3434),t),t.createProtocolConnection=function(e,t,n,i){return(0,o.createMessageConnection)(e,t,n,i)}},3434:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.LSPErrorCodes=t.createProtocolConnection=void 0,r(n(9765),t),r(n(4437),t),r(n(7096),t),r(n(6948),t);var o,s=n(7784);Object.defineProperty(t,"createProtocolConnection",{enumerable:!0,get:function(){return s.createProtocolConnection}}),function(e){e.lspReservedErrorRangeStart=-32899,e.RequestFailed=-32803,e.ServerCancelled=-32802,e.ContentModified=-32801,e.RequestCancelled=-32800,e.lspReservedErrorRangeEnd=-32800}(o||(t.LSPErrorCodes=o={}))},3507:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.CompletionItemFeature=void 0;const s=n(1398),a=n(3434),c=n(9810),u=o(n(8820)),l=[a.CompletionItemKind.Text,a.CompletionItemKind.Method,a.CompletionItemKind.Function,a.CompletionItemKind.Constructor,a.CompletionItemKind.Field,a.CompletionItemKind.Variable,a.CompletionItemKind.Class,a.CompletionItemKind.Interface,a.CompletionItemKind.Module,a.CompletionItemKind.Property,a.CompletionItemKind.Unit,a.CompletionItemKind.Value,a.CompletionItemKind.Enum,a.CompletionItemKind.Keyword,a.CompletionItemKind.Snippet,a.CompletionItemKind.Color,a.CompletionItemKind.File,a.CompletionItemKind.Reference,a.CompletionItemKind.Folder,a.CompletionItemKind.EnumMember,a.CompletionItemKind.Constant,a.CompletionItemKind.Struct,a.CompletionItemKind.Event,a.CompletionItemKind.Operator,a.CompletionItemKind.TypeParameter];class d extends c.TextDocumentLanguageFeature{labelDetailsSupport;constructor(e){super(e,a.CompletionRequest.type),this.labelDetailsSupport=new Map}fillClientCapabilities(e){const t=(0,c.ensure)((0,c.ensure)(e,"textDocument"),"completion");t.dynamicRegistration=!0,t.contextSupport=!0,t.completionItem={snippetSupport:!0,commitCharactersSupport:!0,documentationFormat:[a.MarkupKind.Markdown,a.MarkupKind.PlainText],deprecatedSupport:!0,preselectSupport:!0,tagSupport:{valueSet:[a.CompletionItemTag.Deprecated]},insertReplaceSupport:!0,resolveSupport:{properties:["documentation","detail","additionalTextEdits"]},insertTextModeSupport:{valueSet:[a.InsertTextMode.asIs,a.InsertTextMode.adjustIndentation]},labelDetailsSupport:!0},t.insertTextMode=a.InsertTextMode.adjustIndentation,t.completionItemKind={valueSet:l},t.completionList={itemDefaults:["commitCharacters","editRange","insertTextFormat","insertTextMode","data"]}}initialize(e,t){const n=this.getRegistrationOptions(t,e.completionProvider);n&&this.register({id:u.generateUuid(),registerOptions:n})}registerLanguageProvider(e,t){this.labelDetailsSupport.set(t,!!e.completionItem?.labelDetailsSupport);const n=e.triggerCharacters??[],i=e.allCommitCharacters,r=e.documentSelector,o={provideCompletionItems:(e,t,n,r)=>{const o=this._client,s=this._client.middleware,c=(e,t,n,r)=>o.sendRequest(a.CompletionRequest.type,o.code2ProtocolConverter.asCompletionParams(e,t,n),r).then(e=>r.isCancellationRequested?null:o.protocol2CodeConverter.asCompletionResult(e,i,r),e=>o.handleFailedRequest(a.CompletionRequest.type,r,e,null));return s.provideCompletionItem?s.provideCompletionItem(e,t,r,n,c):c(e,t,r,n)},resolveCompletionItem:e.resolveProvider?(e,n)=>{const i=this._client,r=this._client.middleware,o=(e,n)=>i.sendRequest(a.CompletionResolveRequest.type,i.code2ProtocolConverter.asCompletionItem(e,!!this.labelDetailsSupport.get(t)),n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asCompletionItem(e),t=>i.handleFailedRequest(a.CompletionResolveRequest.type,n,t,e));return r.resolveCompletionItem?r.resolveCompletionItem(e,n,o):o(e,n)}:void 0};return[s.languages.registerCompletionItemProvider(this._client.protocol2CodeConverter.asDocumentSelector(r),o,...n),o]}}t.CompletionItemFeature=d},3527:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const i=n(9023),r=n(9765);class o extends r.AbstractMessageBuffer{static emptyBuffer=Buffer.allocUnsafe(0);constructor(e="utf-8"){super(e)}emptyBuffer(){return o.emptyBuffer}fromString(e,t){return Buffer.from(e,t)}toString(e,t){return e instanceof Buffer?e.toString(t):new i.TextDecoder(t).decode(e)}asNative(e,t){return void 0===t?e instanceof Buffer?e:Buffer.from(e):e instanceof Buffer?e.slice(0,t):Buffer.from(e,0,t)}allocNative(e){return Buffer.allocUnsafe(e)}}class s{stream;constructor(e){this.stream=e}onClose(e){return this.stream.on("close",e),r.Disposable.create(()=>this.stream.off("close",e))}onError(e){return this.stream.on("error",e),r.Disposable.create(()=>this.stream.off("error",e))}onEnd(e){return this.stream.on("end",e),r.Disposable.create(()=>this.stream.off("end",e))}onData(e){return this.stream.on("data",e),r.Disposable.create(()=>this.stream.off("data",e))}}class a{stream;constructor(e){this.stream=e}onClose(e){return this.stream.on("close",e),r.Disposable.create(()=>this.stream.off("close",e))}onError(e){return this.stream.on("error",e),r.Disposable.create(()=>this.stream.off("error",e))}onEnd(e){return this.stream.on("end",e),r.Disposable.create(()=>this.stream.off("end",e))}write(e,t){return new Promise((n,i)=>{const r=e=>{null==e?n():i(e)};"string"==typeof e?this.stream.write(e,t,r):this.stream.write(e,r)})}end(){this.stream.end()}}const c=Object.freeze({messageBuffer:Object.freeze({create:e=>new o(e)}),applicationJson:Object.freeze({encoder:Object.freeze({name:"application/json",encode:(e,t)=>{try{return Promise.resolve(Buffer.from(JSON.stringify(e,void 0,0),t.charset))}catch(e){return Promise.reject(e)}}}),decoder:Object.freeze({name:"application/json",decode:(e,t)=>{try{return e instanceof Buffer?Promise.resolve(JSON.parse(e.toString(t.charset))):Promise.resolve(JSON.parse(new i.TextDecoder(t.charset).decode(e)))}catch(e){return Promise.reject(e)}}})}),stream:Object.freeze({asReadableStream:e=>new s(e),asWritableStream:e=>new a(e)}),console,timer:Object.freeze({setTimeout(e,t,...n){const i=setTimeout(e,t,...n);return{dispose:()=>clearTimeout(i)}},setImmediate(e,...t){const n=setImmediate(e,...t);return{dispose:()=>clearImmediate(n)}},setInterval(e,t,...n){const i=setInterval(e,t,...n);return{dispose:()=>clearInterval(i)}}})});function u(){return c}!function(e){e.install=function(){r.RAL.install(c)}}(u||(u={})),t.default=u},3562:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticTokensRefreshRequest=t.SemanticTokensRangeRequest=t.SemanticTokensDeltaRequest=t.SemanticTokensRequest=t.SemanticTokensRegistrationType=t.TokenFormat=void 0;const i=n(7096);var r,o,s,a,c,u;!function(e){e.Relative="relative"}(r||(t.TokenFormat=r={})),function(e){e.method="textDocument/semanticTokens",e.type=new i.RegistrationType(e.method)}(o||(t.SemanticTokensRegistrationType=o={})),function(e){e.method="textDocument/semanticTokens/full",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method),e.registrationMethod=o.method}(s||(t.SemanticTokensRequest=s={})),function(e){e.method="textDocument/semanticTokens/full/delta",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method),e.registrationMethod=o.method}(a||(t.SemanticTokensDeltaRequest=a={})),function(e){e.method="textDocument/semanticTokens/range",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method),e.registrationMethod=o.method}(c||(t.SemanticTokensRangeRequest=c={})),function(e){e.method="workspace/semanticTokens/refresh",e.messageDirection=i.MessageDirection.serverToClient,e.type=new i.ProtocolRequestType0(e.method)}(u||(t.SemanticTokensRefreshRequest=u={}))},3563:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.matchGlobPattern=void 0;const s=o(n(9624)),a=n(1398);t.matchGlobPattern=function(e,t){let n;if("string"==typeof e)n=e.replace(/\\/g,"/");else try{const t=a.Uri.parse("string"==typeof e.baseUri?e.baseUri:e.baseUri.uri);n=t.with({path:t.path+"/"+e.pattern}).fsPath.replace(/\\/g,"/")}catch(e){return!1}const i=new s.Minimatch(n,{noext:!0});return!!i.makeRe()&&i.match(t.fsPath)}},3633:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.DidCloseNotebookDocumentNotification=t.DidSaveNotebookDocumentNotification=t.DidChangeNotebookDocumentNotification=t.NotebookCellArrayChange=t.DidOpenNotebookDocumentNotification=t.NotebookDocumentSyncRegistrationType=t.NotebookDocument=t.NotebookCell=t.ExecutionSummary=t.NotebookCellKind=void 0;const s=n(4437),a=o(n(7786)),c=n(7096);var u,l,d,h,p,f,g,m,v,y;!function(e){e.Markup=1,e.Code=2,e.is=function(e){return 1===e||2===e}}(u||(t.NotebookCellKind=u={})),function(e){e.create=function(e,t){const n={executionOrder:e};return!0!==t&&!1!==t||(n.success=t),n},e.is=function(e){const t=e;return a.objectLiteral(t)&&s.uinteger.is(t.executionOrder)&&(void 0===t.success||a.boolean(t.success))},e.equals=function(e,t){return e===t||null!=e&&null!=t&&e.executionOrder===t.executionOrder&&e.success===t.success}}(l||(t.ExecutionSummary=l={})),function(e){function t(e,n){if(e===n)return!0;if(null==e||null==n)return!1;if(typeof e!=typeof n)return!1;if("object"!=typeof e)return!1;const i=Array.isArray(e),r=Array.isArray(n);if(i!==r)return!1;if(i&&r){if(e.length!==n.length)return!1;for(let i=0;i<e.length;i++)if(!t(e[i],n[i]))return!1}if(a.objectLiteral(e)&&a.objectLiteral(n)){const i=Object.keys(e),r=Object.keys(n);if(i.length!==r.length)return!1;if(i.sort(),r.sort(),!t(i,r))return!1;for(let r=0;r<i.length;r++){const o=i[r];if(!t(e[o],n[o]))return!1}}return!0}e.create=function(e,t){return{kind:e,document:t}},e.is=function(e){const t=e;return a.objectLiteral(t)&&u.is(t.kind)&&s.DocumentUri.is(t.document)&&(void 0===t.metadata||a.objectLiteral(t.metadata))},e.diff=function(e,n){const i=new Set;return e.document!==n.document&&i.add("document"),e.kind!==n.kind&&i.add("kind"),e.executionSummary!==n.executionSummary&&i.add("executionSummary"),void 0===e.metadata&&void 0===n.metadata||t(e.metadata,n.metadata)||i.add("metadata"),void 0===e.executionSummary&&void 0===n.executionSummary||l.equals(e.executionSummary,n.executionSummary)||i.add("executionSummary"),i}}(d||(t.NotebookCell=d={})),function(e){e.create=function(e,t,n,i){return{uri:e,notebookType:t,version:n,cells:i}},e.is=function(e){const t=e;return a.objectLiteral(t)&&a.string(t.uri)&&s.integer.is(t.version)&&a.typedArray(t.cells,d.is)}}(h||(t.NotebookDocument=h={})),function(e){e.method="notebookDocument/sync",e.messageDirection=c.MessageDirection.clientToServer,e.type=new c.RegistrationType(e.method)}(p||(t.NotebookDocumentSyncRegistrationType=p={})),function(e){e.method="notebookDocument/didOpen",e.messageDirection=c.MessageDirection.clientToServer,e.type=new c.ProtocolNotificationType(e.method),e.registrationMethod=p.method}(f||(t.DidOpenNotebookDocumentNotification=f={})),function(e){e.is=function(e){const t=e;return a.objectLiteral(t)&&s.uinteger.is(t.start)&&s.uinteger.is(t.deleteCount)&&(void 0===t.cells||a.typedArray(t.cells,d.is))},e.create=function(e,t,n){const i={start:e,deleteCount:t};return void 0!==n&&(i.cells=n),i}}(g||(t.NotebookCellArrayChange=g={})),function(e){e.method="notebookDocument/didChange",e.messageDirection=c.MessageDirection.clientToServer,e.type=new c.ProtocolNotificationType(e.method),e.registrationMethod=p.method}(m||(t.DidChangeNotebookDocumentNotification=m={})),function(e){e.method="notebookDocument/didSave",e.messageDirection=c.MessageDirection.clientToServer,e.type=new c.ProtocolNotificationType(e.method),e.registrationMethod=p.method}(v||(t.DidSaveNotebookDocumentNotification=v={})),function(e){e.method="notebookDocument/didClose",e.messageDirection=c.MessageDirection.clientToServer,e.type=new c.ProtocolNotificationType(e.method),e.registrationMethod=p.method}(y||(t.DidCloseNotebookDocumentNotification=y={}))},3652:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.WorkspaceSymbolFeature=void 0;const s=n(1398),a=n(3434),c=n(9810),u=n(7984),l=o(n(8820));class d extends c.WorkspaceFeature{constructor(e){super(e,a.WorkspaceSymbolRequest.type)}fillClientCapabilities(e){const t=(0,c.ensure)((0,c.ensure)(e,"workspace"),"symbol");t.dynamicRegistration=!0,t.symbolKind={valueSet:u.SupportedSymbolKinds},t.tagSupport={valueSet:u.SupportedSymbolTags},t.resolveSupport={properties:["location.range"]}}initialize(e){e.workspaceSymbolProvider&&this.register({id:l.generateUuid(),registerOptions:!0===e.workspaceSymbolProvider?{workDoneProgress:!1}:e.workspaceSymbolProvider})}registerLanguageProvider(e){const t={provideWorkspaceSymbols:(e,t)=>{const n=this._client,i=(e,t)=>n.sendRequest(a.WorkspaceSymbolRequest.type,{query:e},t).then(e=>t.isCancellationRequested?null:n.protocol2CodeConverter.asSymbolInformations(e,t),e=>n.handleFailedRequest(a.WorkspaceSymbolRequest.type,t,e,null)),r=n.middleware;return r.provideWorkspaceSymbols?r.provideWorkspaceSymbols(e,t,i):i(e,t)},resolveWorkspaceSymbol:!0===e.resolveProvider?(e,t)=>{const n=this._client,i=(e,t)=>n.sendRequest(a.WorkspaceSymbolResolveRequest.type,n.code2ProtocolConverter.asWorkspaceSymbol(e),t).then(e=>t.isCancellationRequested?null:n.protocol2CodeConverter.asSymbolInformation(e),e=>n.handleFailedRequest(a.WorkspaceSymbolResolveRequest.type,t,e,null)),r=n.middleware;return r.resolveWorkspaceSymbol?r.resolveWorkspaceSymbol(e,t,i):i(e,t)}:void 0};return[s.languages.registerWorkspaceSymbolProvider(t),t]}}t.WorkspaceSymbolFeature=d},3760:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.CodeLensFeature=void 0;const s=n(1398),a=n(3434),c=o(n(8820)),u=n(9810);class l extends u.TextDocumentLanguageFeature{constructor(e){super(e,a.CodeLensRequest.type)}fillClientCapabilities(e){const t=(0,u.ensure)((0,u.ensure)(e,"textDocument"),"codeLens");t.dynamicRegistration=!0,t.resolveSupport={properties:["command"]},(0,u.ensure)((0,u.ensure)(e,"workspace"),"codeLens").refreshSupport=!0}initialize(e,t){this._client.onRequest(a.CodeLensRefreshRequest.type,async()=>{for(const e of this.getAllProviders())e.onDidChangeCodeLensEmitter.fire()});const n=this.getRegistrationOptions(t,e.codeLensProvider);n&&this.register({id:c.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n=new s.EventEmitter,i={onDidChangeCodeLenses:n.event,provideCodeLenses:(e,t)=>{const n=this._client,i=(e,t)=>n.sendRequest(a.CodeLensRequest.type,n.code2ProtocolConverter.asCodeLensParams(e),t).then(e=>t.isCancellationRequested?null:n.protocol2CodeConverter.asCodeLenses(e,t),e=>n.handleFailedRequest(a.CodeLensRequest.type,t,e,null)),r=n.middleware;return r.provideCodeLenses?r.provideCodeLenses(e,t,i):i(e,t)},resolveCodeLens:e.resolveProvider?(e,t)=>{const n=this._client,i=(e,t)=>n.sendRequest(a.CodeLensResolveRequest.type,n.code2ProtocolConverter.asCodeLens(e),t).then(i=>t.isCancellationRequested?e:n.protocol2CodeConverter.asCodeLens(i),i=>n.handleFailedRequest(a.CodeLensResolveRequest.type,t,i,e)),r=n.middleware;return r.resolveCodeLens?r.resolveCodeLens(e,t,i):i(e,t)}:void 0};return[s.languages.registerCodeLensProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),i),{provider:i,onDidChangeCodeLensEmitter:n}]}}t.CodeLensFeature=l},3811:(e,t,n)=>{const i=n(6909),r=n(7659),o=n(144),s=n(2077),a=n(599),c=n(7092);e.exports=(e,t,n,u)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof n&&(n=n.version),e===n;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof n&&(n=n.version),e!==n;case"":case"=":case"==":return i(e,n,u);case"!=":return r(e,n,u);case">":return o(e,n,u);case">=":return s(e,n,u);case"<":return a(e,n,u);case"<=":return c(e,n,u);default:throw new TypeError(`Invalid operator: ${t}`)}}},3876:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.DefinitionFeature=void 0;const s=n(1398),a=n(3434),c=n(9810),u=o(n(8820));class l extends c.TextDocumentLanguageFeature{constructor(e){super(e,a.DefinitionRequest.type)}fillClientCapabilities(e){const t=(0,c.ensure)((0,c.ensure)(e,"textDocument"),"definition");t.dynamicRegistration=!0,t.linkSupport=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.definitionProvider);n&&this.register({id:u.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideDefinition:(e,t,n)=>{const i=this._client,r=(e,t,n)=>i.sendRequest(a.DefinitionRequest.type,i.code2ProtocolConverter.asTextDocumentPositionParams(e,t),n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asDefinitionResult(e,n),e=>i.handleFailedRequest(a.DefinitionRequest.type,n,e,null)),o=i.middleware;return o.provideDefinition?o.provideDefinition(e,t,n,r):r(e,t,n)}};return[this.registerProvider(t,n),n]}registerProvider(e,t){return s.languages.registerDefinitionProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}}t.DefinitionFeature=l},3881:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.createConverter=void 0;const a=o(n(1398)),c=o(n(3434)),u=o(n(1027)),l=o(n(5671)),d=s(n(4752)),h=s(n(1936)),p=s(n(7252)),f=s(n(2946)),g=n(5146),m=s(n(4430)),v=s(n(5965)),y=s(n(1332)),b=s(n(107)),_=n(3434);var C;!function(e){e.is=function(e){const t=e;return t&&u.string(t.language)&&u.string(t.value)}}(C||(C={})),t.createConverter=function(e,t,n,i){const r=e||(e=>a.Uri.parse(e));function o(e){return r(e)}function s(e){const t=new g.ProtocolDiagnostic(w(e.range),e.message,T(e.severity),e.data);if(void 0!==e.code)if("string"==typeof e.code||"number"==typeof e.code)c.CodeDescription.is(e.codeDescription)?t.code={value:e.code,target:o(e.codeDescription.href)}:t.code=e.code;else if(g.DiagnosticCode.is(e.code)){t.hasDiagnosticCode=!0;const n=e.code;t.code={value:n.value,target:o(n.target)}}return e.source&&(t.source=e.source),e.relatedInformation&&(t.relatedInformation=function(e){const t=new Array(e.length);for(let n=0;n<e.length;n++){const i=e[n];t[n]=new a.DiagnosticRelatedInformation(L(i.location),i.message)}return t}(e.relatedInformation)),Array.isArray(e.tags)&&(t.tags=function(e){if(!e)return;const t=[];for(const n of e){const e=D(n);void 0!==e&&t.push(e)}return t.length>0?t:void 0}(e.tags)),t}function D(e){switch(e){case c.DiagnosticTag.Unnecessary:return a.DiagnosticTag.Unnecessary;case c.DiagnosticTag.Deprecated:return a.DiagnosticTag.Deprecated;default:return}}function S(e){return e?new a.Position(e.line,e.character):void 0}function w(e){return e?new a.Range(e.start.line,e.start.character,e.end.line,e.end.character):void 0}async function R(e,t){return l.map(e,e=>new a.Range(e.start.line,e.start.character,e.end.line,e.end.character),t)}function T(e){if(null==e)return a.DiagnosticSeverity.Error;switch(e){case c.DiagnosticSeverity.Error:return a.DiagnosticSeverity.Error;case c.DiagnosticSeverity.Warning:return a.DiagnosticSeverity.Warning;case c.DiagnosticSeverity.Information:return a.DiagnosticSeverity.Information;case c.DiagnosticSeverity.Hint:return a.DiagnosticSeverity.Hint}return a.DiagnosticSeverity.Error}function P(e){if(u.string(e))return e;switch(e.kind){case c.MarkupKind.Markdown:return O(e.value);case c.MarkupKind.PlainText:return e.value;default:return`Unsupported Markup content received. Kind is: ${e.kind}`}}function O(e){let r;if(void 0===e||"string"==typeof e)r=new a.MarkdownString(e);else switch(e.kind){case c.MarkupKind.Markdown:r=new a.MarkdownString(e.value);break;case c.MarkupKind.PlainText:r=new a.MarkdownString,r.appendText(e.value);break;default:r=new a.MarkdownString,r.appendText(`Unsupported Markup content received. Kind is: ${e.kind}`)}return r.isTrusted=t,r.supportHtml=n,r.supportThemeIcons=i,r}function k(e){if(e===c.CompletionItemTag.Deprecated)return a.CompletionItemTag.Deprecated}function x(e,t,n,i,r,o){const s=function(e){if(null==e)return[];const t=[];for(const n of e){const e=k(n);void 0!==e&&t.push(e)}return t}(e.tags),l=function(e){return c.CompletionItemLabelDetails.is(e.labelDetails)?{label:e.label,detail:e.labelDetails.detail,description:e.labelDetails.description}:e.label}(e),h=new d.default(l);e.detail&&(h.detail=e.detail),e.documentation&&(h.documentation=P(e.documentation),h.documentationFormat=u.string(e.documentation)?"$string":e.documentation.kind),e.filterText&&(h.filterText=e.filterText);const p=function(e,t,n){const i=e.insertTextFormat??n;if(void 0!==e.textEdit||void 0!==t){const[n,o]=void 0!==e.textEdit?(r=e.textEdit,c.InsertReplaceEdit.is(r)?[{inserting:w(r.insert),replacing:w(r.replace)},r.newText]:[w(r.range),r.newText]):[t,e.textEditText??e.label];return i===c.InsertTextFormat.Snippet?{text:new a.SnippetString(o),range:n,fromEdit:!0}:{text:o,range:n,fromEdit:!0}}return e.insertText?i===c.InsertTextFormat.Snippet?{text:new a.SnippetString(e.insertText),fromEdit:!1}:{text:e.insertText,fromEdit:!1}:void 0;var r}(e,n,r);if(p&&(h.insertText=p.text,h.range=p.range,h.fromEdit=p.fromEdit),u.number(e.kind)){const[t,n]=(f=e.kind,c.CompletionItemKind.Text<=f&&f<=c.CompletionItemKind.TypeParameter?[f-1,void 0]:[a.CompletionItemKind.Text,f]);h.kind=t,n&&(h.originalItemKind=n)}var f;e.sortText&&(h.sortText=e.sortText),e.additionalTextEdits&&(h.additionalTextEdits=F(e.additionalTextEdits));const g=void 0!==e.commitCharacters?u.stringArray(e.commitCharacters)?e.commitCharacters:void 0:t;g&&(h.commitCharacters=g.slice()),e.command&&(h.command=X(e.command)),!0!==e.deprecated&&!1!==e.deprecated||(h.deprecated=e.deprecated,!0===e.deprecated&&s.push(a.CompletionItemTag.Deprecated)),!0!==e.preselect&&!1!==e.preselect||(h.preselect=e.preselect);const m=e.data??o;void 0!==m&&(h.data=m),s.length>0&&(h.tags=s);const v=e.insertTextMode??i;return void 0!==v&&(h.insertTextMode=v,v===c.InsertTextMode.asIs&&(h.keepWhitespace=!0)),h}function E(e){if(e)return new a.TextEdit(w(e.range),e.newText)}async function M(e,t){if(e)return l.map(e,E,t)}function F(e){if(!e)return;const t=new Array(e.length);for(let n=0;n<e.length;n++)t[n]=E(e[n]);return t}async function q(e,t){return l.mapAsync(e,I,t)}async function I(e,t){const n=new a.SignatureInformation(e.label);return void 0!==e.documentation&&(n.documentation=P(e.documentation)),void 0!==e.parameters&&(n.parameters=await N(e.parameters,t)),void 0!==e.activeParameter&&(n.activeParameter=e.activeParameter??-1),n}function N(e,t){return l.map(e,j,t)}function j(e){const t=new a.ParameterInformation(e.label);return e.documentation&&(t.documentation=P(e.documentation)),t}function L(e){return e?new a.Location(r(e.uri),w(e.range)):void 0}function A(e){if(!e)return;const t={targetUri:r(e.targetUri),targetRange:w(e.targetRange),originSelectionRange:w(e.originSelectionRange),targetSelectionRange:w(e.targetSelectionRange)};if(!t.targetSelectionRange)throw new Error("targetSelectionRange must not be undefined or null");return t}async function $(e,t){if(e){if(u.array(e)){if(0===e.length)return[];if(c.LocationLink.is(e[0])){const n=e;return l.map(n,A,t)}{const n=e;return l.map(n,L,t)}}return c.LocationLink.is(e)?[A(e)]:L(e)}}function H(e){const t=new a.DocumentHighlight(w(e.range));return u.number(e.kind)&&(t.kind=W(e.kind)),t}function W(e){switch(e){case c.DocumentHighlightKind.Text:return a.DocumentHighlightKind.Text;case c.DocumentHighlightKind.Read:return a.DocumentHighlightKind.Read;case c.DocumentHighlightKind.Write:return a.DocumentHighlightKind.Write}return a.DocumentHighlightKind.Text}function U(e){return e<=c.SymbolKind.TypeParameter?e-1:a.SymbolKind.Property}function K(e){if(e===c.SymbolTag.Deprecated)return a.SymbolTag.Deprecated}function z(e){if(null==e)return;const t=[];for(const n of e){const e=K(n);void 0!==e&&t.push(e)}return 0===t.length?void 0:t}function B(e){const t=e.data,n=e.location,i=void 0===n.range||void 0!==t?new y.default(e.name,U(e.kind),e.containerName??"",void 0===n.range?r(n.uri):new a.Location(r(e.location.uri),w(n.range)),t):new a.SymbolInformation(e.name,U(e.kind),e.containerName??"",new a.Location(r(e.location.uri),w(n.range)));return G(i,e),i}function V(e){const t=new a.DocumentSymbol(e.name,e.detail||"",U(e.kind),w(e.range),w(e.selectionRange));if(G(t,e),void 0!==e.children&&e.children.length>0){const n=[];for(const t of e.children)n.push(V(t));t.children=n}return t}function G(e,t){e.tags=z(t.tags),t.deprecated&&(e.tags?e.tags.includes(a.SymbolTag.Deprecated)||(e.tags=e.tags.concat(a.SymbolTag.Deprecated)):e.tags=[a.SymbolTag.Deprecated])}function X(e){const t={title:e.title,command:e.command};return e.tooltip&&(t.tooltip=e.tooltip),e.arguments&&(t.arguments=e.arguments),t}const J=new Map;function Q(e){if(null==e)return;let t=J.get(e);if(t)return t;const n=e.split(".");t=a.CodeActionKind.Empty;for(const e of n)t=t.append(e);return t}async function Y(e,t){if(null==e)return;const n=new f.default(e.title,e.data);return void 0!==e.kind&&(n.kind=Q(e.kind)),void 0!==e.diagnostics&&(n.diagnostics=function(e){const t=new Array(e.length);for(let n=0;n<e.length;n++)t[n]=s(e[n]);return t}(e.diagnostics)),void 0!==e.edit&&(n.edit=await ee(e.edit,t)),void 0!==e.command&&(n.command=X(e.command)),void 0!==e.isPreferred&&(n.isPreferred=e.isPreferred),void 0!==e.disabled&&(n.disabled={reason:e.disabled.reason}),n}function Z(e){if(!e)return;const t=new h.default(w(e.range));return e.command&&(t.command=X(e.command)),void 0!==e.data&&null!==e.data&&(t.data=e.data),t}async function ee(e,t){if(!e)return;const n=new Map;if(void 0!==e.changeAnnotations){const i=e.changeAnnotations;await l.forEach(Object.keys(i),e=>{const t=function(e){if(void 0!==e)return{label:e.label,needsConfirmation:!!e.needsConfirmation,description:e.description}}(i[e]);n.set(e,t)},t)}const i=e=>void 0===e?void 0:n.get(e),o=new a.WorkspaceEdit;if(e.documentChanges){const n=e.documentChanges;await l.forEach(n,e=>{if(c.CreateFile.is(e))o.createFile(r(e.uri),e.options,i(e.annotationId));else if(c.RenameFile.is(e))o.renameFile(r(e.oldUri),r(e.newUri),e.options,i(e.annotationId));else if(c.DeleteFile.is(e))o.deleteFile(r(e.uri),e.options,i(e.annotationId));else{if(!c.TextDocumentEdit.is(e))throw new Error(`Unknown workspace edit change received:\n${JSON.stringify(e,void 0,4)}`);{const t=r(e.textDocument.uri),n=[];for(const t of e.edits)c.AnnotatedTextEdit.is(t)?n.push([new a.TextEdit(w(t.range),t.newText),i(t.annotationId)]):c.SnippetTextEdit.is(t)?n.push([new a.SnippetTextEdit(w(t.range),new a.SnippetString(t.snippet.value)),i(t.annotationId)]):n.push([new a.TextEdit(w(t.range),t.newText),void 0]);o.set(t,n)}}},t)}else if(e.changes){const n=e.changes;await l.forEach(Object.keys(n),e=>{o.set(r(e),F(n[e]))},t)}return o}function te(e){const t=w(e.range),n=e.target?o(e.target):void 0,i=new p.default(t,n);return void 0!==e.tooltip&&(i.tooltip=e.tooltip),void 0!==e.data&&null!==e.data&&(i.data=e.data),i}function ne(e){return new a.Color(e.red,e.green,e.blue,e.alpha)}function ie(e){return new a.ColorInformation(w(e.range),ne(e.color))}function re(e){const t=new a.ColorPresentation(e.label);return t.additionalTextEdits=F(e.additionalTextEdits),e.textEdit&&(t.textEdit=E(e.textEdit)),t}function oe(e){if(e)switch(e){case c.FoldingRangeKind.Comment:return a.FoldingRangeKind.Comment;case c.FoldingRangeKind.Imports:return a.FoldingRangeKind.Imports;case c.FoldingRangeKind.Region:return a.FoldingRangeKind.Region}}function se(e){return new a.FoldingRange(e.startLine,e.endLine,oe(e.kind))}function ae(e){return new a.SelectionRange(w(e.range),e.parent?ae(e.parent):void 0)}function ce(e){return c.InlineValueText.is(e)?new a.InlineValueText(w(e.range),e.text):c.InlineValueVariableLookup.is(e)?new a.InlineValueVariableLookup(w(e.range),e.variableName,e.caseSensitiveLookup):new a.InlineValueEvaluatableExpression(w(e.range),e.expression)}async function ue(e,t){const n="string"==typeof e.label?e.label:await l.map(e.label,le,t),i=new b.default(S(e.position),n);return void 0!==e.kind&&(i.kind=e.kind),void 0!==e.textEdits&&(i.textEdits=await M(e.textEdits,t)),void 0!==e.tooltip&&(i.tooltip=de(e.tooltip)),void 0!==e.paddingLeft&&(i.paddingLeft=e.paddingLeft),void 0!==e.paddingRight&&(i.paddingRight=e.paddingRight),void 0!==e.data&&(i.data=e.data),i}function le(e){const t=new a.InlayHintLabelPart(e.value);return void 0!==e.location&&(t.location=L(e.location)),void 0!==e.tooltip&&(t.tooltip=de(e.tooltip)),void 0!==e.command&&(t.command=X(e.command)),t}function de(e){return"string"==typeof e?e:O(e)}function he(e){if(null===e)return;const t=new m.default(U(e.kind),e.name,e.detail||"",o(e.uri),w(e.range),w(e.selectionRange),e.data);return void 0!==e.tags&&(t.tags=z(e.tags)),t}async function pe(e,t){return new a.CallHierarchyIncomingCall(he(e.from),await R(e.fromRanges,t))}async function fe(e,t){return new a.CallHierarchyOutgoingCall(he(e.to),await R(e.fromRanges,t))}function ge(e){return new a.SemanticTokensEdit(e.start,e.deleteCount,void 0!==e.data?new Uint32Array(e.data):void 0)}function me(e){if(null===e)return;const t=new v.default(U(e.kind),e.name,e.detail||"",o(e.uri),w(e.range),w(e.selectionRange),e.data);return void 0!==e.tags&&(t.tags=z(e.tags)),t}function ve(e){if(u.string(e))return e;if(c.RelativePattern.is(e)){if(c.URI.is(e.baseUri))return new a.RelativePattern(o(e.baseUri),e.pattern);if(c.WorkspaceFolder.is(e.baseUri)){const t=a.workspace.getWorkspaceFolder(o(e.baseUri.uri));return void 0!==t?new a.RelativePattern(t,e.pattern):void 0}}}function ye(e){let t,n;t="string"==typeof e.insertText?e.insertText:new a.SnippetString(e.insertText.value),e.command&&(n=X(e.command));const i=new a.InlineCompletionItem(t,w(e.range),n);return e.filterText&&(i.filterText=e.filterText),i}return J.set(c.CodeActionKind.Empty,a.CodeActionKind.Empty),J.set(c.CodeActionKind.QuickFix,a.CodeActionKind.QuickFix),J.set(c.CodeActionKind.Refactor,a.CodeActionKind.Refactor),J.set(c.CodeActionKind.RefactorExtract,a.CodeActionKind.RefactorExtract),J.set(c.CodeActionKind.RefactorInline,a.CodeActionKind.RefactorInline),J.set(c.CodeActionKind.RefactorRewrite,a.CodeActionKind.RefactorRewrite),J.set(c.CodeActionKind.Source,a.CodeActionKind.Source),J.set(c.CodeActionKind.SourceOrganizeImports,a.CodeActionKind.SourceOrganizeImports),{asUri:o,asDocumentSelector:function(e){const t=[];for(const n of e)if("string"==typeof n)t.push(n);else if(_.NotebookCellTextDocumentFilter.is(n))if("string"==typeof n.notebook)t.push({notebookType:n.notebook,language:n.language});else{const e=n.notebook.notebookType??"*";t.push({notebookType:e,scheme:n.notebook.scheme,pattern:ve(n.notebook.pattern),language:n.language})}else _.TextDocumentFilter.is(n)&&t.push({language:n.language,scheme:n.scheme,pattern:ve(n.pattern)});return t},asDiagnostics:async function(e,t){return l.map(e,s,t)},asDiagnostic:s,asRange:w,asRanges:R,asPosition:S,asDiagnosticSeverity:T,asDiagnosticTag:D,asHover:function(e){if(e)return new a.Hover(function(e){if(u.string(e))return O(e);if(C.is(e))return O().appendCodeblock(e.value,e.language);if(Array.isArray(e)){const t=[];for(const n of e){const e=O();C.is(n)?e.appendCodeblock(n.value,n.language):e.appendMarkdown(n),t.push(e)}return t}return O(e)}(e.contents),w(e.range))},asCompletionResult:async function(e,t,n){if(!e)return;if(Array.isArray(e))return l.map(e,e=>x(e,t),n);const i=e,{defaultRange:r,commitCharacters:o}=function(e,t){const n=e.itemDefaults?.editRange,i=e.itemDefaults?.commitCharacters??t;return c.Range.is(n)?{defaultRange:w(n),commitCharacters:i}:void 0!==n?{defaultRange:{inserting:w(n.insert),replacing:w(n.replace)},commitCharacters:i}:{defaultRange:void 0,commitCharacters:i}}(i,t),s=await l.map(i.items,e=>x(e,o,r,i.itemDefaults?.insertTextMode,i.itemDefaults?.insertTextFormat,i.itemDefaults?.data),n);return new a.CompletionList(s,i.isIncomplete)},asCompletionItem:x,asTextEdit:E,asTextEdits:M,asSignatureHelp:async function(e,t){if(!e)return;const n=new a.SignatureHelp;return u.number(e.activeSignature)?n.activeSignature=e.activeSignature:n.activeSignature=0,u.number(e.activeParameter)?n.activeParameter=e.activeParameter:null===e.activeParameter?n.activeParameter=-1:n.activeParameter=0,e.signatures&&(n.signatures=await q(e.signatures,t)),n},asSignatureInformations:q,asSignatureInformation:I,asParameterInformations:N,asParameterInformation:j,asDeclarationResult:async function(e,t){if(e)return $(e,t)},asDefinitionResult:async function(e,t){if(e)return $(e,t)},asLocation:L,asReferences:async function(e,t){if(e)return l.map(e,L,t)},asDocumentHighlights:async function(e,t){if(e)return l.map(e,H,t)},asDocumentHighlight:H,asDocumentHighlightKind:W,asSymbolKind:U,asSymbolTag:K,asSymbolTags:z,asSymbolInformations:async function(e,t){if(e)return l.map(e,B,t)},asSymbolInformation:B,asDocumentSymbols:async function(e,t){if(null!=e)return l.map(e,V,t)},asDocumentSymbol:V,asCommand:X,asCommands:async function(e,t){if(e)return l.map(e,X,t)},asCodeAction:Y,asCodeActionKind:Q,asCodeActionKinds:function(e){if(null!=e)return e.map(e=>Q(e))},asCodeActionDocumentations:function(e){if(null!=e)return e.map(e=>({kind:Q(e.kind),command:X(e.command)}))},asCodeActionResult:function(e,t){return l.mapAsync(e,async e=>c.Command.is(e)?X(e):Y(e,t),t)},asCodeLens:Z,asCodeLenses:async function(e,t){if(e)return l.map(e,Z,t)},asWorkspaceEdit:ee,asDocumentLink:te,asDocumentLinks:async function(e,t){if(e)return l.map(e,te,t)},asFoldingRangeKind:oe,asFoldingRange:se,asFoldingRanges:async function(e,t){if(e)return l.map(e,se,t)},asColor:ne,asColorInformation:ie,asColorInformations:async function(e,t){if(e)return l.map(e,ie,t)},asColorPresentation:re,asColorPresentations:async function(e,t){if(e)return l.map(e,re,t)},asSelectionRange:ae,asSelectionRanges:async function(e,t){return Array.isArray(e)?l.map(e,ae,t):[]},asInlineValue:ce,asInlineValues:async function(e,t){return Array.isArray(e)?l.map(e,ce,t):[]},asInlayHint:ue,asInlayHints:async function(e,t){if(Array.isArray(e))return l.mapAsync(e,ue,t)},asSemanticTokensLegend:function(e){return e},asSemanticTokens:async function(e,t){if(null!=e)return new a.SemanticTokens(new Uint32Array(e.data),e.resultId)},asSemanticTokensEdit:ge,asSemanticTokensEdits:async function(e,t){if(null!=e)return new a.SemanticTokensEdits(e.edits.map(ge),e.resultId)},asCallHierarchyItem:he,asCallHierarchyItems:async function(e,t){if(null!==e)return l.map(e,he,t)},asCallHierarchyIncomingCall:pe,asCallHierarchyIncomingCalls:async function(e,t){if(null!==e)return l.mapAsync(e,pe,t)},asCallHierarchyOutgoingCall:fe,asCallHierarchyOutgoingCalls:async function(e,t){if(null!==e)return l.mapAsync(e,fe,t)},asLinkedEditingRanges:async function(e,t){if(null!=e)return new a.LinkedEditingRanges(await R(e.ranges,t),function(e){if(null!=e)return new RegExp(e)}(e.wordPattern))},asTypeHierarchyItem:me,asTypeHierarchyItems:async function(e,t){if(null!==e)return l.map(e,me,t)},asGlobPattern:ve,asInlineCompletionResult:async function(e,t){if(!e)return;if(Array.isArray(e))return l.map(e,e=>ye(e),t);const n=e,i=await l.map(n.items,e=>ye(e),t);return new a.InlineCompletionList(i)},asInlineCompletionItem:ye}}},4031:e=>{const t=/^[0-9]+$/,n=(e,n)=>{const i=t.test(e),r=t.test(n);return i&&r&&(e=+e,n=+n),e===n?0:i&&!r?-1:r&&!i?1:e<n?-1:1};e.exports={compareIdentifiers:n,rcompareIdentifiers:(e,t)=>n(t,e)}},4092:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.SignatureHelpFeature=void 0;const s=n(1398),a=n(3434),c=n(9810),u=o(n(8820));class l extends c.TextDocumentLanguageFeature{constructor(e){super(e,a.SignatureHelpRequest.type)}fillClientCapabilities(e){const t=(0,c.ensure)((0,c.ensure)(e,"textDocument"),"signatureHelp");t.dynamicRegistration=!0,t.signatureInformation={documentationFormat:[a.MarkupKind.Markdown,a.MarkupKind.PlainText]},t.signatureInformation.parameterInformation={labelOffsetSupport:!0},t.signatureInformation.activeParameterSupport=!0,t.signatureInformation.noActiveParameterSupport=!0,t.contextSupport=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.signatureHelpProvider);n&&this.register({id:u.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t={provideSignatureHelp:(e,t,n,i)=>{const r=this._client,o=(e,t,n,i)=>r.sendRequest(a.SignatureHelpRequest.type,r.code2ProtocolConverter.asSignatureHelpParams(e,t,n),i).then(e=>i.isCancellationRequested?null:r.protocol2CodeConverter.asSignatureHelp(e,i),e=>r.handleFailedRequest(a.SignatureHelpRequest.type,i,e,null)),s=r.middleware;return s.provideSignatureHelp?s.provideSignatureHelp(e,t,i,n,o):o(e,t,i,n)}};return[this.registerProvider(e,t),t]}registerProvider(e,t){const n=this._client.protocol2CodeConverter.asDocumentSelector(e.documentSelector);if(void 0===e.retriggerCharacters){const i=e.triggerCharacters||[];return s.languages.registerSignatureHelpProvider(n,t,...i)}{const i={triggerCharacters:e.triggerCharacters||[],retriggerCharacters:e.retriggerCharacters||[]};return s.languages.registerSignatureHelpProvider(n,t,i)}}}t.SignatureHelpFeature=l},4201:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.WillDeleteFilesFeature=t.WillRenameFilesFeature=t.WillCreateFilesFeature=t.DidDeleteFilesFeature=t.DidRenameFilesFeature=t.DidCreateFilesFeature=void 0;const s=o(n(1398)),a=o(n(9624)),c=o(n(3434)),u=o(n(8820));function l(e,t){return void 0===e[t]&&(e[t]={}),e[t]}function d(e,t,n){e[t]=n}class h{_client;_event;_registrationType;_clientCapability;_serverCapability;_listener;_filters;constructor(e,t,n,i,r){this._client=e,this._event=t,this._registrationType=n,this._clientCapability=i,this._serverCapability=r,this._filters=new Map}getState(){return{kind:"workspace",id:this._registrationType.method,registrations:this._filters.size>0}}filterSize(){return this._filters.size}get registrationType(){return this._registrationType}fillClientCapabilities(e){const t=l(l(e,"workspace"),"fileOperations");d(t,"dynamicRegistration",!0),d(t,this._clientCapability,!0)}initialize(e){const t=e.workspace?.fileOperations,n=void 0!==t?t[this._serverCapability]:void 0;if(void 0!==n?.filters)try{this.register({id:u.generateUuid(),registerOptions:{filters:n.filters}})}catch(e){this._client.warn(`Ignoring invalid glob pattern for ${this._serverCapability} registration: ${e}`)}}register(e){this._listener||(this._listener=this._event(this.send,this));const t=e.registerOptions.filters.map(e=>{const t=new a.Minimatch(e.pattern.glob,h.asMinimatchOptions(e.pattern.options));if(!t.makeRe())throw new Error(`Invalid pattern ${e.pattern.glob}!`);return{scheme:e.scheme,matcher:t,kind:e.pattern.matches}});this._filters.set(e.id,t)}unregister(e){this._filters.delete(e),0===this._filters.size&&this._listener&&(this._listener.dispose(),this._listener=void 0)}clear(){this._filters.clear(),this._listener&&(this._listener.dispose(),this._listener=void 0)}getFileType(e){return h.getFileType(e)}async filter(e,t){const n=await Promise.all(e.files.map(async e=>{const n=t(e),i=n.fsPath.replace(/\\/g,"/");for(const e of this._filters.values())for(const t of e)if(void 0===t.scheme||t.scheme===n.scheme)if(t.matcher.match(i)){if(void 0===t.kind)return!0;const e=await this.getFileType(n);if(void 0===e)return this._client.error(`Failed to determine file type for ${n.toString()}.`),!0;if(e===s.FileType.File&&t.kind===c.FileOperationPatternKind.file||e===s.FileType.Directory&&t.kind===c.FileOperationPatternKind.folder)return!0}else if(t.kind===c.FileOperationPatternKind.folder&&await h.getFileType(n)===s.FileType.Directory&&t.matcher.match(`${i}/`))return!0;return!1})),i=e.files.filter((e,t)=>n[t]);return{...e,files:i}}static async getFileType(e){try{return(await s.workspace.fs.stat(e)).type}catch(e){return}}static asMinimatchOptions(e){const t={dot:!0};return!0===e?.ignoreCase&&(t.nocase=!0),t}}class p extends h{_notificationType;_accessUri;_createParams;constructor(e,t,n,i,r,o,s){super(e,t,n,i,r),this._notificationType=n,this._accessUri=o,this._createParams=s}async send(e){const t=await this.filter(e,this._accessUri);if(t.files.length){const e=async e=>this._client.sendNotification(this._notificationType,this._createParams(e));return this.doSend(t,e)}}}class f extends p{_willListener;_fsPathFileTypes=new Map;async getFileType(e){const t=e.fsPath;if(this._fsPathFileTypes.has(t))return this._fsPathFileTypes.get(t);const n=await h.getFileType(e);return n&&this._fsPathFileTypes.set(t,n),n}async cacheFileTypes(e,t){await this.filter(e,t)}clearFileTypeCache(){this._fsPathFileTypes.clear()}unregister(e){super.unregister(e),0===this.filterSize()&&this._willListener&&(this._willListener.dispose(),this._willListener=void 0)}clear(){super.clear(),this._willListener&&(this._willListener.dispose(),this._willListener=void 0)}}t.DidCreateFilesFeature=class extends p{constructor(e){super(e,s.workspace.onDidCreateFiles,c.DidCreateFilesNotification.type,"didCreate","didCreate",e=>e,e.code2ProtocolConverter.asDidCreateFilesParams)}doSend(e,t){const n=this._client.middleware.workspace;return n?.didCreateFiles?n.didCreateFiles(e,t):t(e)}},t.DidRenameFilesFeature=class extends f{constructor(e){super(e,s.workspace.onDidRenameFiles,c.DidRenameFilesNotification.type,"didRename","didRename",e=>e.oldUri,e.code2ProtocolConverter.asDidRenameFilesParams)}register(e){this._willListener||(this._willListener=s.workspace.onWillRenameFiles(this.willRename,this)),super.register(e)}willRename(e){e.waitUntil(this.cacheFileTypes(e,e=>e.oldUri))}doSend(e,t){this.clearFileTypeCache();const n=this._client.middleware.workspace;return n?.didRenameFiles?n.didRenameFiles(e,t):t(e)}},t.DidDeleteFilesFeature=class extends f{constructor(e){super(e,s.workspace.onDidDeleteFiles,c.DidDeleteFilesNotification.type,"didDelete","didDelete",e=>e,e.code2ProtocolConverter.asDidDeleteFilesParams)}register(e){this._willListener||(this._willListener=s.workspace.onWillDeleteFiles(this.willDelete,this)),super.register(e)}willDelete(e){e.waitUntil(this.cacheFileTypes(e,e=>e))}doSend(e,t){this.clearFileTypeCache();const n=this._client.middleware.workspace;return n?.didDeleteFiles?n.didDeleteFiles(e,t):t(e)}};class g extends h{_requestType;_accessUri;_createParams;constructor(e,t,n,i,r,o,s){super(e,t,n,i,r),this._requestType=n,this._accessUri=o,this._createParams=s}async send(e){const t=this.waitUntil(e);e.waitUntil(t)}async waitUntil(e){const t=await this.filter(e,this._accessUri);if(t.files.length){const e=e=>this._client.sendRequest(this._requestType,this._createParams(e),e.token).then(this._client.protocol2CodeConverter.asWorkspaceEdit);return this.doSend(t,e)}}}t.WillCreateFilesFeature=class extends g{constructor(e){super(e,s.workspace.onWillCreateFiles,c.WillCreateFilesRequest.type,"willCreate","willCreate",e=>e,e.code2ProtocolConverter.asWillCreateFilesParams)}doSend(e,t){const n=this._client.middleware.workspace;return n?.willCreateFiles?n.willCreateFiles(e,t):t(e)}},t.WillRenameFilesFeature=class extends g{constructor(e){super(e,s.workspace.onWillRenameFiles,c.WillRenameFilesRequest.type,"willRename","willRename",e=>e.oldUri,e.code2ProtocolConverter.asWillRenameFilesParams)}doSend(e,t){const n=this._client.middleware.workspace;return n?.willRenameFiles?n.willRenameFiles(e,t):t(e)}},t.WillDeleteFilesFeature=class extends g{constructor(e){super(e,s.workspace.onWillDeleteFiles,c.WillDeleteFilesRequest.type,"willDelete","willDelete",e=>e,e.code2ProtocolConverter.asWillDeleteFilesParams)}doSend(e,t){const n=this._client.middleware.workspace;return n?.willDeleteFiles?n.willDeleteFiles(e,t):t(e)}}},4231:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.HoverFeature=void 0;const s=n(1398),a=n(3434),c=n(9810),u=o(n(8820));class l extends c.TextDocumentLanguageFeature{constructor(e){super(e,a.HoverRequest.type)}fillClientCapabilities(e){const t=(0,c.ensure)((0,c.ensure)(e,"textDocument"),"hover");t.dynamicRegistration=!0,t.contentFormat=[a.MarkupKind.Markdown,a.MarkupKind.PlainText]}initialize(e,t){const n=this.getRegistrationOptions(t,e.hoverProvider);n&&this.register({id:u.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideHover:(e,t,n)=>{const i=this._client,r=(e,t,n)=>i.sendRequest(a.HoverRequest.type,i.code2ProtocolConverter.asTextDocumentPositionParams(e,t),n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asHover(e),e=>i.handleFailedRequest(a.HoverRequest.type,n,e,null)),o=i.middleware;return o.provideHover?o.provideHover(e,t,n,r):r(e,t,n)}};return[this.registerProvider(t,n),n]}registerProvider(e,t){return s.languages.registerHoverProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}}t.HoverFeature=l},4267:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.SyncConfigurationFeature=t.toJSONObject=t.ConfigurationFeature=void 0;const s=n(1398),a=n(3434),c=o(n(1027)),u=o(n(8820)),l=n(9810);function d(e){if(e){if(Array.isArray(e))return e.map(d);if("object"==typeof e){const t=Object.create(null);for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=d(e[n]));return t}}return e}t.ConfigurationFeature=class{_client;constructor(e){this._client=e}getState(){return{kind:"static"}}fillClientCapabilities(e){e.workspace=e.workspace||{},e.workspace.configuration=!0}initialize(){const e=this._client;e.onRequest(a.ConfigurationRequest.type,(t,n)=>{const i=e=>{const t=[];for(const n of e.items){const e=void 0!==n.scopeUri&&null!==n.scopeUri?this._client.protocol2CodeConverter.asUri(n.scopeUri):void 0;t.push(this.getConfiguration(e,null!==n.section?n.section:void 0))}return t},r=e.middleware.workspace;return r&&r.configuration?r.configuration(t,n,i):i(t)})}getConfiguration(e,t){let n=null;if(t){const i=t.lastIndexOf(".");if(-1===i)n=d(s.workspace.getConfiguration(void 0,e).get(t));else{const r=s.workspace.getConfiguration(t.substr(0,i),e);r&&(n=d(r.get(t.substr(i+1))))}}else{const t=s.workspace.getConfiguration(void 0,e);n={};for(const e of Object.keys(t))t.has(e)&&(n[e]=d(t.get(e)))}return void 0===n&&(n=null),n}clear(){}},t.toJSONObject=d,t.SyncConfigurationFeature=class{_client;isCleared;_listeners;constructor(e){this._client=e,this.isCleared=!1,this._listeners=new Map}getState(){return{kind:"workspace",id:this.registrationType.method,registrations:this._listeners.size>0}}get registrationType(){return a.DidChangeConfigurationNotification.type}fillClientCapabilities(e){(0,l.ensure)((0,l.ensure)(e,"workspace"),"didChangeConfiguration").dynamicRegistration=!0}initialize(){this.isCleared=!1;const e=this._client.clientOptions.synchronize?.configurationSection;void 0!==e&&this.register({id:u.generateUuid(),registerOptions:{section:e}})}register(e){const t=s.workspace.onDidChangeConfiguration(t=>{this.onDidChangeConfiguration(e.registerOptions.section,t)});this._listeners.set(e.id,t),void 0!==e.registerOptions.section&&this.onDidChangeConfiguration(e.registerOptions.section,void 0)}unregister(e){const t=this._listeners.get(e);t&&(this._listeners.delete(e),t.dispose())}clear(){for(const e of this._listeners.values())e.dispose();this._listeners.clear(),this.isCleared=!0}onDidChangeConfiguration(e,t){if(this.isCleared)return;let n;if(n=c.string(e)?[e]:e,void 0!==n&&void 0!==t&&!n.some(e=>t.affectsConfiguration(e)))return;const i=async e=>void 0===e?this._client.sendNotification(a.DidChangeConfigurationNotification.type,{settings:null}):this._client.sendNotification(a.DidChangeConfigurationNotification.type,{settings:this.extractSettingsInformation(e)}),r=this._client.middleware.workspace?.didChangeConfiguration;(r?r(n,i):i(n)).catch(e=>{this._client.error(`Sending notification ${a.DidChangeConfigurationNotification.type.method} failed`,e)})}extractSettingsInformation(e){function t(e,t){let n=e;for(let e=0;e<t.length-1;e++){let i=n[t[e]];i||(i=Object.create(null),n[t[e]]=i),n=i}return n}const n=this._client.clientOptions.workspaceFolder?this._client.clientOptions.workspaceFolder.uri:void 0,i=Object.create(null);for(let r=0;r<e.length;r++){const o=e[r],a=o.indexOf(".");let c=null;if(c=a>=0?s.workspace.getConfiguration(o.substr(0,a),n).get(o.substr(a+1)):s.workspace.getConfiguration(void 0,n).get(o),c){const n=e[r].split(".");t(i,n)[n[n.length-1]]=d(c)}}return i}}},4292:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.NotebookDocumentSyncFeature=void 0;const s=o(n(1398)),a=o(n(3434)),c=o(n(8820)),u=o(n(1027)),l=n(3563);function d(e,t){return void 0===e[t]&&(e[t]={}),e[t]}var h,p,f,g,m;!function(e){let t;!function(t){function n(e,t){return e.map(e=>r(e,t))}function i(e){return o(new Set,e)}function r(e,t){const n=a.NotebookCell.create(function(e){switch(e){case s.NotebookCellKind.Markup:return a.NotebookCellKind.Markup;case s.NotebookCellKind.Code:return a.NotebookCellKind.Code}}(e.kind),t.asUri(e.document.uri));return Object.keys(e.metadata).length>0&&(n.metadata=i(e.metadata)),void 0!==e.executionSummary&&u.number(e.executionSummary.executionOrder)&&u.boolean(e.executionSummary.success)&&(n.executionSummary={executionOrder:e.executionSummary.executionOrder,success:e.executionSummary.success}),n}function o(e,t){if(e.has(t))throw new Error("Can't deep copy cyclic structures.");if(Array.isArray(t)){const n=[];for(const i of t)if(null!==i&&"object"==typeof i||Array.isArray(i))n.push(o(e,i));else{if(i instanceof RegExp)throw new Error("Can't transfer regular expressions to the server");n.push(i)}return n}{const n=Object.keys(t),i=Object.create(null);for(const r of n){const n=t[r];if(null!==n&&"object"==typeof n||Array.isArray(n))i[r]=o(e,n);else{if(n instanceof RegExp)throw new Error("Can't transfer regular expressions to the server");i[r]=n}}return i}}t.asVersionedNotebookDocumentIdentifier=function(e,t){return{version:e.version,uri:t.asUri(e.uri)}},t.asNotebookDocument=function(e,t,r){const o=a.NotebookDocument.create(r.asUri(e.uri),e.notebookType,e.version,n(t,r));return Object.keys(e.metadata).length>0&&(o.metadata=i(e.metadata)),o},t.asNotebookCells=n,t.asMetadata=i,t.asNotebookCell=r,t.asTextContentChange=function(e,t){const n=t.asChangeTextDocumentParams(e,e.document.uri,e.document.version);return{document:n.textDocument,changes:n.contentChanges}},t.asNotebookDocumentChangeEvent=function(t,n){const i=Object.create(null);if(t.metadata&&(i.metadata=e.c2p.asMetadata(t.metadata)),void 0!==t.cells){const r=Object.create(null),o=t.cells;o.structure&&(r.structure={array:{start:o.structure.array.start,deleteCount:o.structure.array.deleteCount,cells:void 0!==o.structure.array.cells?o.structure.array.cells.map(t=>e.c2p.asNotebookCell(t,n)):void 0},didOpen:void 0!==o.structure.didOpen?o.structure.didOpen.map(e=>n.asOpenTextDocumentParams(e.document).textDocument):void 0,didClose:void 0!==o.structure.didClose?o.structure.didClose.map(e=>n.asCloseTextDocumentParams(e.document).textDocument):void 0}),void 0!==o.data&&(r.data=o.data.map(t=>e.c2p.asNotebookCell(t,n))),void 0!==o.textContent&&(r.textContent=o.textContent.map(t=>e.c2p.asTextContentChange(t,n))),Object.keys(r).length>0&&(i.cells=r)}return i}}(t=e.c2p||(e.c2p={}))}(h||(h={})),function(e){function t(e,t,i=!0){return!(e.kind!==t.kind||e.document.uri.toString()!==t.document.uri.toString()||e.document.languageId!==t.document.languageId||!function(e,t){return e===t||void 0!==e&&void 0!==t&&(e.executionOrder===t.executionOrder&&e.success===t.success&&function(e,t){return e===t||void 0!==e&&void 0!==t&&(e.startTime===t.startTime&&e.endTime===t.endTime)}(e.timing,t.timing))}(e.executionSummary,t.executionSummary))&&(!i||i&&n(e.metadata,t.metadata))}function n(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if(typeof e!=typeof t)return!1;if("object"!=typeof e)return!1;const r=Array.isArray(e),o=Array.isArray(t);if(r!==o)return!1;if(r&&o){if(e.length!==t.length)return!1;for(let i=0;i<e.length;i++)if(!n(e[i],t[i]))return!1}if(i(e)&&i(t)){const i=Object.keys(e),r=Object.keys(t);if(i.length!==r.length)return!1;if(i.sort(),r.sort(),!n(i,r))return!1;for(let r=0;r<i.length;r++){const o=i[r];if(!n(e[o],t[o]))return!1}return!0}return!1}function i(e){return null!==e&&"object"==typeof e}e.computeDiff=function(e,n,i){const r=e.length,o=n.length;let s=0;for(;s<o&&s<r&&t(e[s],n[s],i);)s++;if(s<o&&s<r){let a=r-1,c=o-1;for(;a>=0&&c>=0&&t(e[a],n[c],i);)a--,c--;const u=a+1-s,l=s===c+1?void 0:n.slice(s,c+1);return void 0!==l?{start:s,deleteCount:u,cells:l}:{start:s,deleteCount:u}}return s<o?{start:s,deleteCount:0,cells:n.slice(s)}:s<r?{start:s,deleteCount:r-s}:void 0},e.isObjectLiteral=i}(p||(p={})),function(e){e.matchNotebook=function(e,t){if("string"==typeof e)return"*"===e||t.notebookType===e;if(void 0!==e.notebookType&&"*"!==e.notebookType&&t.notebookType!==e.notebookType)return!1;const n=t.uri;return!(void 0!==e.scheme&&"*"!==e.scheme&&n.scheme!==e.scheme||void 0!==e.pattern&&!(0,l.matchGlobPattern)(e.pattern,n))}}(f||(f={})),function(e){function t(e,t,n,i){return void 0===t&&void 0===n?{notebook:e,language:i}:{notebook:{notebookType:e,scheme:t,pattern:n},language:i}}e.asDocumentSelector=function(e){const n=e.notebookSelector,i=[];for(const e of n){const n=("string"==typeof e.notebook?e.notebook:e.notebook?.notebookType)??"*",r="string"==typeof e.notebook?void 0:e.notebook?.scheme,o="string"==typeof e.notebook?void 0:e.notebook?.pattern;if(void 0!==e.cells)for(const s of e.cells)i.push(t(n,r,o,s.language));else i.push(t(n,r,o,void 0))}return i}}(g||(g={})),function(e){e.create=function(e){return{cells:e,uris:new Set(e.map(e=>e.document.uri.toString()))}}}(m||(m={}));class v{client;options;notebookSyncInfo;notebookDidOpen;disposables;selector;onChangeNotificationSent;onOpenNotificationSent;onCloseNotificationSent;onSaveNotificationSent;constructor(e,t,n,i,r,o){this.client=e,this.options=t,this.notebookSyncInfo=new Map,this.notebookDidOpen=new Set,this.disposables=[],this.selector=e.protocol2CodeConverter.asDocumentSelector(g.asDocumentSelector(t)),this.onChangeNotificationSent=n,this.onOpenNotificationSent=i,this.onCloseNotificationSent=r,this.onSaveNotificationSent=o,s.workspace.onDidOpenNotebookDocument(e=>{this.notebookDidOpen.add(e.uri.toString()),this.didOpen(e)},void 0,this.disposables);for(const e of s.workspace.notebookDocuments)this.notebookDidOpen.add(e.uri.toString()),this.didOpen(e);s.workspace.onDidChangeNotebookDocument(e=>this.didChangeNotebookDocument(e),void 0,this.disposables),!0===this.options.save&&s.workspace.onDidSaveNotebookDocument(e=>this.didSave(e),void 0,this.disposables),s.workspace.onDidCloseNotebookDocument(e=>{this.didClose(e),this.notebookDidOpen.delete(e.uri.toString())},void 0,this.disposables)}getState(){for(const e of s.workspace.notebookDocuments)if(void 0!==this.getMatchingCellsConsideringSyncInfo(e))return{kind:"document",id:"$internal",registrations:!0,matches:!0};return{kind:"document",id:"$internal",registrations:!0,matches:!1}}get mode(){return"notebook"}handles(e){if(s.languages.match(this.selector,e)>0)return!0;const t=e.uri.toString();for(const e of this.notebookSyncInfo.values())if(e.uris.has(t))return!0;return!1}didOpenNotebookCellTextDocument(e,t){if(0===s.languages.match(this.selector,t.document))return;if(!this.notebookDidOpen.has(e.uri.toString()))return;const n=this.getSyncInfo(e),i=this.cellMatches(e,t);if(void 0!==n){const r=n.uris.has(t.document.uri.toString());if(i&&r||!i&&!r)return;if(i){const i=this.mergeCells(e,n,[t]);if(void 0!==i){const t=this.asNotebookDocumentChangeEvent(e,void 0,n,i);void 0!==t&&this.doSendChange(t,i).catch(()=>{})}}}else i&&this.doSendOpen(e,[t]).catch(()=>{})}didChangeNotebookCellTextDocument(e,t,n){if(0===s.languages.match(this.selector,n.document))return;const i=this.getSyncInfo(e);void 0!==i&&i.uris.has(t.document.uri.toString())&&this.doSendChange({notebook:e,cells:{textContent:[n]}},i.cells).catch(()=>{})}didCloseNotebookCellTextDocument(e,t){const n=this.getSyncInfo(e);if(void 0===n)return;const i=t.document.uri,r=n.cells.findIndex(e=>e.document.uri.toString()===i.toString());if(-1!==r)if(0===r&&1===n.cells.length)this.doSendClose(e,n.cells).catch(()=>{});else{const t=n.cells.slice(),i=t.splice(r,1);this.doSendChange({notebook:e,cells:{structure:{array:{start:r,deleteCount:1},didClose:i}}},t).catch(()=>{})}}dispose(){for(const e of this.disposables)e.dispose()}didOpen(e,t,n=this.getSyncInfo(e)){if(void 0!==n)if(void 0===t&&(t=n.cells.slice()),void 0!==t){const i=this.asNotebookDocumentChangeEvent(e,void 0,n,t);void 0!==i&&this.doSendChange(i,t).catch(()=>{})}else this.doSendClose(e,[]).catch(()=>{});else{if(void 0===(t=this.getMatchingCells(e)))return;this.doSendOpen(e,t).catch(()=>{})}}didChangeNotebookDocument(e){const t=e.notebook,n=this.getSyncInfo(t);if(void 0===n){if(0===e.contentChanges.length)return;const i=this.getMatchingCells(t);if(void 0===i)return;this.didOpen(t,i,n)}else{const i=this.getMatchingCellsFromEvent(t,n,e);if(void 0===i)return void this.didClose(t,n);const r=this.asNotebookDocumentChangeEvent(e.notebook,e,n,i);void 0!==r&&this.doSendChange(r,i).catch(()=>{})}}didSave(e){void 0!==this.getSyncInfo(e)&&this.doSendSave(e).catch(()=>{})}didClose(e,t=this.getSyncInfo(e)){if(void 0===t)return;const n=e.getCells().filter(e=>t.uris.has(e.document.uri.toString()));this.doSendClose(e,n).catch(()=>{})}async sendDidOpenNotebookDocument(e){if(void 0!==this.getSyncInfo(e))throw new Error(`Notebook document ${e.uri.toString()} is already open`);const t=this.getMatchingCells(e);if(void 0!==t)return this.doSendOpen(e,t)}async doSendOpen(e,t){const n=async(e,t)=>{const n=t.map(e=>this.client.code2ProtocolConverter.asTextDocumentItem(e.document));try{await this.client.sendNotification(a.DidOpenNotebookDocumentNotification.type,{notebookDocument:h.c2p.asNotebookDocument(e,t,this.client.code2ProtocolConverter),cellTextDocuments:n}),this.onOpenNotificationSent.fire(e)}catch(e){throw this.client.error("Sending DidOpenNotebookDocumentNotification failed",e),e}},i=this.client.middleware?.notebooks;return this.notebookSyncInfo.set(e.uri.toString(),m.create(t)),void 0!==i?.didOpen?i.didOpen(e,t,n):n(e,t)}async sendDidChangeNotebookDocument(e){const t=this.getMatchingCellsFromSyncInfo(e.notebook);if(void 0===t)throw new Error(`Received changed event for un-synced notebook ${e.notebook.uri.toString()}`);return this.doSendChange(e,t)}async doSendChange(e,t){const n=async e=>{try{await this.client.sendNotification(a.DidChangeNotebookDocumentNotification.type,{notebookDocument:h.c2p.asVersionedNotebookDocumentIdentifier(e.notebook,this.client.code2ProtocolConverter),change:h.c2p.asNotebookDocumentChangeEvent(e,this.client.code2ProtocolConverter)}),this.onChangeNotificationSent.fire(e)}catch(e){throw this.client.error("Sending DidChangeNotebookDocumentNotification failed",e),e}},i=this.client.middleware?.notebooks;return void 0!==e.cells?.structure&&this.notebookSyncInfo.set(e.notebook.uri.toString(),m.create(t)),void 0!==i?.didChange?i?.didChange(e,n):n(e)}async sendDidSaveNotebookDocument(e){return this.doSendSave(e)}async doSendSave(e){const t=async e=>{try{await this.client.sendNotification(a.DidSaveNotebookDocumentNotification.type,{notebookDocument:{uri:this.client.code2ProtocolConverter.asUri(e.uri)}}),this.onSaveNotificationSent.fire(e)}catch(e){throw this.client.error("Sending DidSaveNotebookDocumentNotification failed",e),e}},n=this.client.middleware?.notebooks;return void 0!==n?.didSave?n.didSave(e,t):t(e)}async sendDidCloseNotebookDocument(e){const t=this.getMatchingCellsFromSyncInfo(e);if(void 0===t)throw new Error(`Received close event for un-synced notebook ${e.uri.toString()}`);return this.doSendClose(e,t)}async doSendClose(e,t){const n=async(e,t)=>{try{await this.client.sendNotification(a.DidCloseNotebookDocumentNotification.type,{notebookDocument:{uri:this.client.code2ProtocolConverter.asUri(e.uri)},cellTextDocuments:t.map(e=>this.client.code2ProtocolConverter.asTextDocumentIdentifier(e.document))}),this.onCloseNotificationSent.fire(e)}catch(e){throw this.client.error("Sending DidCloseNotebookDocumentNotification failed",e),e}},i=this.client.middleware?.notebooks;return this.notebookSyncInfo.delete(e.uri.toString()),void 0!==i?.didClose?i.didClose(e,t,n):n(e,t)}getSynchronizedCells(e){const t=this.getSyncInfo(e);return t?.cells}asNotebookDocumentChangeEvent(e,t,n,i){if(void 0!==t&&t.notebook!==e)throw new Error("Notebook must be identical");const r={notebook:e};let o;if(void 0!==t?.metadata&&(r.metadata=h.c2p.asMetadata(t.metadata)),void 0!==t?.cellChanges&&t.cellChanges.length>0){const e=[];o=new Set(i.map(e=>e.document.uri.toString()));for(const n of t.cellChanges)!o.has(n.cell.document.uri.toString())||void 0===n.executionSummary&&void 0===n.metadata||e.push(n.cell);e.length>0&&(r.cells=r.cells??{},r.cells.data=e)}if((void 0!==t?.contentChanges&&t.contentChanges.length>0||void 0===t)&&void 0!==n&&void 0!==i){const e=n.cells,t=i,o=p.computeDiff(e,t,!1);let s,a;if(void 0!==o){s=void 0===o.cells?new Map:new Map(o.cells.map(e=>[e.document.uri.toString(),e])),a=0===o.deleteCount?new Map:new Map(e.slice(o.start,o.start+o.deleteCount).map(e=>[e.document.uri.toString(),e]));for(const e of Array.from(a.keys()))s.has(e)&&(a.delete(e),s.delete(e));r.cells=r.cells??{};const t=[],n=[];if(s.size>0||a.size>0){for(const e of s.values())t.push(e);for(const e of a.values())n.push(e)}r.cells.structure={array:o,didOpen:t,didClose:n}}}return Object.keys(r).length>1?r:void 0}getMatchingCells(e,t=e.getCells()){if(void 0!==this.options.notebookSelector)for(const n of this.options.notebookSelector)if(void 0===n.notebook||f.matchNotebook(n.notebook,e)){const i=this.filterCells(e,t,n.cells);return 0===i.length?void 0:i}}getMatchingCellsFromEvent(e,t,n){if(void 0===this.options.notebookSelector)return;let i,r;for(const t of this.options.notebookSelector)if(void 0===t.notebook||f.matchNotebook(t.notebook,e)){i=t;break}if(void 0===i)return;if(!(void 0!==n.cellChanges&&0!==n.cellChanges.length||void 0!==n.contentChanges&&0!==n.contentChanges.length))return t.cells;if(void 0!==n.cellChanges&&n.cellChanges.length>0){const o=n.cellChanges.map(e=>e.cell),s=this.filterCells(e,o,i.cells);if(s.length!==o.length){r=new Set(t.uris);for(const e of o)r.delete(e.document.uri.toString());for(const e of s)r.add(e.document.uri.toString())}}if(void 0!==n.contentChanges&&n.contentChanges.length>0){void 0===r&&(r=new Set(t.uris));for(const t of n.contentChanges){for(const e of t.removedCells)r.delete(e.document.uri.toString());const n=this.filterCells(e,new Array(...t.addedCells),i.cells);for(const e of n)r.add(e.document.uri.toString())}}if(void 0===r)return t.cells;const o=[],s=e.getCells();for(const e of s)r.has(e.document.uri.toString())&&o.push(e);return o}getMatchingCellsFromSyncInfo(e){const t=this.getSyncInfo(e);return void 0!==t?t.cells:void 0}getMatchingCellsConsideringSyncInfo(e){const t=this.getSyncInfo(e);return void 0!==t?t.cells:this.getMatchingCells(e)}mergeCells(e,t,n){const i=[],r=new Set(t.uris);for(const e of n)r.add(e.document.uri.toString());for(const t of e.getCells())r.has(t.document.uri.toString())&&i.push(t);return i}cellMatches(e,t){const n=this.getMatchingCells(e,[t]);return void 0!==n&&n[0]===t}filterCells(e,t,n){const i=void 0!==n?t.filter(e=>{const t=e.document.languageId;return n.some(e=>"*"===e.language||t===e.language)}):t;return"function"==typeof this.client.clientOptions.notebookDocumentOptions?.filterCells?this.client.clientOptions.notebookDocumentOptions.filterCells(e,i):i}getSyncInfo(e){return this.notebookSyncInfo.get(e.uri.toString())}}class y{static CellScheme="vscode-notebook-cell";client;registrations;dedicatedChannel;_onChangeNotificationSent;_onOpenNotificationSent;_onCloseNotificationSent;_onSaveNotificationSent;constructor(e){this.client=e,this.registrations=new Map,this.registrationType=a.NotebookDocumentSyncRegistrationType.type,this._onChangeNotificationSent=new s.EventEmitter,this._onOpenNotificationSent=new s.EventEmitter,this._onCloseNotificationSent=new s.EventEmitter,this._onSaveNotificationSent=new s.EventEmitter,s.workspace.onDidOpenTextDocument(e=>{if(e.uri.scheme!==y.CellScheme)return;const[t,n]=this.findNotebookDocumentAndCell(e);if(void 0!==t&&void 0!==n)for(const e of this.registrations.values())e instanceof v&&e.didOpenNotebookCellTextDocument(t,n)}),s.workspace.onDidChangeTextDocument(e=>{if(0===e.contentChanges.length)return;const t=e.document;if(t.uri.scheme!==y.CellScheme)return;const[n,i]=this.findNotebookDocumentAndCell(t);if(void 0!==n&&void 0!==i)for(const t of this.registrations.values())t instanceof v&&t.didChangeNotebookCellTextDocument(n,i,e)}),s.workspace.onDidCloseTextDocument(e=>{if(e.uri.scheme!==y.CellScheme)return;const[t,n]=this.findNotebookDocumentAndCell(e);if(void 0!==t&&void 0!==n)for(const e of this.registrations.values())e instanceof v&&e.didCloseNotebookCellTextDocument(t,n)})}getState(){if(0===this.registrations.size)return{kind:"document",id:this.registrationType.method,registrations:!1,matches:!1};for(const e of this.registrations.values()){const t=e.getState();if("document"===t.kind&&!0===t.registrations&&!0===t.matches)return{kind:"document",id:this.registrationType.method,registrations:!0,matches:!0}}return{kind:"document",id:this.registrationType.method,registrations:!0,matches:!1}}registrationType;get onOpenNotificationSent(){return this._onOpenNotificationSent.event}get onChangeNotificationSent(){return this._onChangeNotificationSent.event}get onCloseNotificationSent(){return this._onCloseNotificationSent.event}get onSaveNotificationSent(){return this._onSaveNotificationSent.event}fillClientCapabilities(e){const t=d(d(e,"notebookDocument"),"synchronization");t.dynamicRegistration=!0,t.executionSummarySupport=!0}preInitialize(e){const t=e.notebookDocumentSync;void 0!==t&&(this.dedicatedChannel=this.client.protocol2CodeConverter.asDocumentSelector(g.asDocumentSelector(t)))}initialize(e){const t=e.notebookDocumentSync;if(void 0===t)return;const n=t.id??c.generateUuid();this.register({id:n,registerOptions:t})}register(e){const t=new v(this.client,e.registerOptions,this._onChangeNotificationSent,this._onOpenNotificationSent,this._onCloseNotificationSent,this._onSaveNotificationSent);this.registrations.set(e.id,t)}unregister(e){const t=this.registrations.get(e);void 0!==t&&(this.registrations.delete(e),t.dispose())}clear(){for(const e of this.registrations.values())e.dispose();this.registrations.clear(),this._onChangeNotificationSent.dispose(),this._onChangeNotificationSent=new s.EventEmitter,this._onOpenNotificationSent.dispose(),this._onOpenNotificationSent=new s.EventEmitter,this._onCloseNotificationSent.dispose(),this._onCloseNotificationSent=new s.EventEmitter,this._onSaveNotificationSent.dispose(),this._onSaveNotificationSent=new s.EventEmitter}handles(e){if(e.uri.scheme!==y.CellScheme)return!1;if(void 0!==this.dedicatedChannel&&s.languages.match(this.dedicatedChannel,e)>0)return!0;for(const t of this.registrations.values())if(t.handles(e))return!0;return!1}getProvider(e){for(const t of this.registrations.values())if(t.handles(e.document))return t}findNotebookDocumentAndCell(e){const t=e.uri.toString();for(const e of s.workspace.notebookDocuments)for(const n of e.getCells())if(n.document.uri.toString()===t)return[e,n];return[void 0,void 0]}}t.NotebookDocumentSyncFeature=y},4341:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TextDocumentContentRefreshRequest=t.TextDocumentContentRequest=void 0;const i=n(7096);var r,o;!function(e){e.method="workspace/textDocumentContent",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.TextDocumentContentRequest=r={})),function(e){e.method="workspace/textDocumentContent/refresh",e.messageDirection=i.MessageDirection.serverToClient,e.type=new i.ProtocolRequestType(e.method)}(o||(t.TextDocumentContentRefreshRequest=o={}))},4346:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.createConverter=void 0;const a=o(n(1398)),c=o(n(3434)),u=o(n(1027)),l=o(n(5671)),d=s(n(4752)),h=s(n(1936)),p=s(n(7252)),f=s(n(2946)),g=n(5146),m=s(n(4430)),v=s(n(5965)),y=s(n(1332)),b=s(n(107));var _;!function(e){e.is=function(e){const t=e;return t&&!!t.inserting&&!!t.replacing}}(_||(_={})),t.createConverter=function(e){const t=e||(e=>e.toString());function n(e){return t(e)}function i(e){return{uri:t(e.uri)}}function r(e){return{uri:t(e.uri),languageId:e.languageId,version:e.version,text:e.getText()}}function o(e){switch(e){case a.TextDocumentSaveReason.Manual:return c.TextDocumentSaveReason.Manual;case a.TextDocumentSaveReason.AfterDelay:return c.TextDocumentSaveReason.AfterDelay;case a.TextDocumentSaveReason.FocusOut:return c.TextDocumentSaveReason.FocusOut}return c.TextDocumentSaveReason.Manual}function s(e){switch(e){case a.CompletionTriggerKind.TriggerCharacter:return c.CompletionTriggerKind.TriggerCharacter;case a.CompletionTriggerKind.TriggerForIncompleteCompletions:return c.CompletionTriggerKind.TriggerForIncompleteCompletions;default:return c.CompletionTriggerKind.Invoked}}function C(e){switch(e){case a.SignatureHelpTriggerKind.Invoke:return c.SignatureHelpTriggerKind.Invoked;case a.SignatureHelpTriggerKind.TriggerCharacter:return c.SignatureHelpTriggerKind.TriggerCharacter;case a.SignatureHelpTriggerKind.ContentChange:return c.SignatureHelpTriggerKind.ContentChange}}function D(e){return{label:e.label}}function S(e){return{label:e.label,parameters:(t=e.parameters,t.map(D))};var t}function w(e){return{line:e.line,character:e.character}}function R(e){return null==e?e:{line:e.line>c.uinteger.MAX_VALUE?c.uinteger.MAX_VALUE:e.line,character:e.character>c.uinteger.MAX_VALUE?c.uinteger.MAX_VALUE:e.character}}function T(e){return null==e?e:{start:R(e.start),end:R(e.end)}}function P(e){return null==e?e:c.Location.create(n(e.uri),T(e.range))}function O(e){switch(e){case a.DiagnosticSeverity.Error:return c.DiagnosticSeverity.Error;case a.DiagnosticSeverity.Warning:return c.DiagnosticSeverity.Warning;case a.DiagnosticSeverity.Information:return c.DiagnosticSeverity.Information;case a.DiagnosticSeverity.Hint:return c.DiagnosticSeverity.Hint}}function k(e){switch(e){case a.DiagnosticTag.Unnecessary:return c.DiagnosticTag.Unnecessary;case a.DiagnosticTag.Deprecated:return c.DiagnosticTag.Deprecated;default:return}}function x(e){return{message:e.message,location:P(e.location)}}function E(e){const t=c.Diagnostic.create(T(e.range),e.message),i=e instanceof g.ProtocolDiagnostic?e:void 0;void 0!==i&&void 0!==i.data&&(t.data=i.data);const r=function(e){if(null!=e)return u.number(e)||u.string(e)?e:{value:e.value,target:n(e.target)}}(e.code);return g.DiagnosticCode.is(r)?void 0!==i&&i.hasDiagnosticCode?t.code=r:(t.code=r.value,t.codeDescription={href:r.target}):t.code=r,u.number(e.severity)&&(t.severity=O(e.severity)),Array.isArray(e.tags)&&(t.tags=function(e){if(!e)return;const t=[];for(const n of e){const e=k(n);void 0!==e&&t.push(e)}return t.length>0?t:void 0}(e.tags)),e.relatedInformation&&(t.relatedInformation=e.relatedInformation.map(x)),e.source&&(t.source=e.source),t}function M(e,t){return null==e?e:l.map(e,E,t)}function F(e){return null==e?e:e.map(E)}function q(e){if(e===a.CompletionItemTag.Deprecated)return c.CompletionItemTag.Deprecated}function I(e){return{range:T(e.range),newText:e.newText}}function N(e){return null==e?e:e.map(I)}function j(e){return e<=a.SymbolKind.TypeParameter?e+1:c.SymbolKind.Property}function L(e){return e}function A(e){return e.map(L)}function $(e){switch(e){case a.CodeActionTriggerKind.Invoke:return c.CodeActionTriggerKind.Invoked;case a.CodeActionTriggerKind.Automatic:return c.CodeActionTriggerKind.Automatic;default:return}}function H(e){if(null!=e)return e.value}function W(e){return{triggerKind:U(e.triggerKind),selectedCompletionInfo:K(e.selectedCompletionInfo)}}function U(e){switch(e){case a.InlineCompletionTriggerKind.Invoke:return c.InlineCompletionTriggerKind.Invoked;case a.InlineCompletionTriggerKind.Automatic:return c.InlineCompletionTriggerKind.Automatic}}function K(e){if(null!=e)return{range:T(e.range),text:e.text}}function z(e){const t=c.Command.create(e.title,e.command);return e.tooltip&&(t.tooltip=e.tooltip),e.arguments&&(t.arguments=e.arguments),t}function B(e){const t=c.InlayHintLabelPart.create(e.value);return void 0!==e.location&&(t.location=P(e.location)),void 0!==e.command&&(t.command=z(e.command)),void 0!==e.tooltip&&(t.tooltip=V(e.tooltip)),t}function V(e){return"string"==typeof e?e:{kind:c.MarkupKind.Markdown,value:e.value}}return{asUri:n,asTextDocumentIdentifier:i,asTextDocumentItem:r,asVersionedTextDocumentIdentifier:function(e){return{uri:t(e.uri),version:e.version}},asOpenTextDocumentParams:function(e){return{textDocument:r(e)}},asChangeTextDocumentParams:function(e,n,i){if(function(e){const t=e;return!!t.uri&&!!t.version}(e))return{textDocument:{uri:t(e.uri),version:e.version},contentChanges:[{text:e.getText()}]};if(function(e){const t=e;return!!t.document&&!!t.contentChanges}(e)){const r=i;return{textDocument:{uri:t(n),version:r},contentChanges:e.contentChanges.map(e=>{const t=e.range;return{range:{start:{line:t.start.line,character:t.start.character},end:{line:t.end.line,character:t.end.character}},rangeLength:e.rangeLength,text:e.text}})}}throw Error("Unsupported text document change parameter")},asCloseTextDocumentParams:function(e){return{textDocument:i(e)}},asSaveTextDocumentParams:function(e,t=!1){const n={textDocument:i(e)};return t&&(n.text=e.getText()),n},asWillSaveTextDocumentParams:function(e){return{textDocument:i(e.document),reason:o(e.reason)}},asDidCreateFilesParams:function(e){return{files:e.files.map(e=>({uri:t(e)}))}},asDidRenameFilesParams:function(e){return{files:e.files.map(e=>({oldUri:t(e.oldUri),newUri:t(e.newUri)}))}},asDidDeleteFilesParams:function(e){return{files:e.files.map(e=>({uri:t(e)}))}},asWillCreateFilesParams:function(e){return{files:e.files.map(e=>({uri:t(e)}))}},asWillRenameFilesParams:function(e){return{files:e.files.map(e=>({oldUri:t(e.oldUri),newUri:t(e.newUri)}))}},asWillDeleteFilesParams:function(e){return{files:e.files.map(e=>({uri:t(e)}))}},asTextDocumentPositionParams:function(e,t){return{textDocument:i(e),position:w(t)}},asCompletionParams:function(e,t,n){return{textDocument:i(e),position:w(t),context:{triggerKind:s(n.triggerKind),triggerCharacter:n.triggerCharacter}}},asSignatureHelpParams:function(e,t,n){return{textDocument:i(e),position:w(t),context:{isRetrigger:n.isRetrigger,triggerCharacter:n.triggerCharacter,triggerKind:C(n.triggerKind),activeSignatureHelp:(r=n.activeSignatureHelp,void 0===r?r:{signatures:(o=r.signatures,o.map(S)),activeSignature:r.activeSignature,activeParameter:r.activeParameter})}};var r,o},asWorkerPosition:w,asRange:T,asRanges:function(e){return e.map(T)},asPosition:R,asPositions:function(e,t){return l.map(e,R,t)},asPositionsSync:function(e){return e.map(R)},asLocation:P,asDiagnosticSeverity:O,asDiagnosticTag:k,asDiagnostic:E,asDiagnostics:M,asDiagnosticsSync:F,asCompletionItem:function(e,t=!1){let n,i;u.string(e.label)?n=e.label:(n=e.label.label,!t||void 0===e.label.detail&&void 0===e.label.description||(i={detail:e.label.detail,description:e.label.description}));const r={label:n};void 0!==i&&(r.labelDetails=i);const o=e instanceof d.default?e:void 0;var s,l;e.detail&&(r.detail=e.detail),e.documentation&&(o&&"$string"!==o.documentationFormat?r.documentation=function(e,t){switch(e){case"$string":return t;case c.MarkupKind.PlainText:return{kind:e,value:t};case c.MarkupKind.Markdown:return{kind:e,value:t.value};default:return`Unsupported Markup content received. Kind is: ${e}`}}(o.documentationFormat,e.documentation):r.documentation=e.documentation),e.filterText&&(r.filterText=e.filterText),function(e,t){let n,i,r=c.InsertTextFormat.PlainText;t.textEdit?(n=t.textEdit.newText,i=t.textEdit.range):t.insertText instanceof a.SnippetString?(r=c.InsertTextFormat.Snippet,n=t.insertText.value):n=t.insertText,t.range&&(i=t.range),e.insertTextFormat=r,t.fromEdit&&void 0!==n&&void 0!==i?e.textEdit=function(e,t){return _.is(t)?c.InsertReplaceEdit.create(e,T(t.inserting),T(t.replacing)):{newText:e,range:T(t)}}(n,i):e.insertText=n}(r,e),u.number(e.kind)&&(r.kind=(s=e.kind,void 0!==(l=o&&o.originalItemKind)?l:s+1)),e.sortText&&(r.sortText=e.sortText),e.additionalTextEdits&&(r.additionalTextEdits=N(e.additionalTextEdits)),e.commitCharacters&&(r.commitCharacters=e.commitCharacters.slice()),e.command&&(r.command=z(e.command)),!0!==e.preselect&&!1!==e.preselect||(r.preselect=e.preselect);const h=function(e){if(void 0===e)return e;const t=[];for(const n of e){const e=q(n);void 0!==e&&t.push(e)}return t}(e.tags);if(o){if(void 0!==o.data&&(r.data=o.data),!0===o.deprecated||!1===o.deprecated){if(!0===o.deprecated&&void 0!==h&&h.length>0){const e=h.indexOf(a.CompletionItemTag.Deprecated);-1!==e&&h.splice(e,1)}r.deprecated=o.deprecated}void 0!==o.insertTextMode&&(r.insertTextMode=o.insertTextMode)}return void 0!==h&&h.length>0&&(r.tags=h),void 0===r.insertTextMode&&!0===e.keepWhitespace&&(r.insertTextMode=c.InsertTextMode.adjustIndentation),r},asTextEdit:I,asSymbolKind:j,asSymbolTag:L,asSymbolTags:A,asReferenceParams:function(e,t,n){return{textDocument:i(e),position:w(t),context:{includeDeclaration:n.includeDeclaration}}},asCodeAction:async function(e,t){const n=c.CodeAction.create(e.title);if(e instanceof f.default&&void 0!==e.data&&(n.data=e.data),void 0!==e.kind&&(n.kind=H(e.kind)),void 0!==e.diagnostics&&(n.diagnostics=await M(e.diagnostics,t)),void 0!==e.edit)throw new Error("VS Code code actions can only be converted to a protocol code action without an edit.");return void 0!==e.command&&(n.command=z(e.command)),void 0!==e.isPreferred&&(n.isPreferred=e.isPreferred),void 0!==e.disabled&&(n.disabled={reason:e.disabled.reason}),n},asCodeActionSync:function(e){const t=c.CodeAction.create(e.title);if(e instanceof f.default&&void 0!==e.data&&(t.data=e.data),void 0!==e.kind&&(t.kind=H(e.kind)),void 0!==e.diagnostics&&(t.diagnostics=F(e.diagnostics)),void 0!==e.edit)throw new Error("VS Code code actions can only be converted to a protocol code action without an edit.");return void 0!==e.command&&(t.command=z(e.command)),void 0!==e.isPreferred&&(t.isPreferred=e.isPreferred),void 0!==e.disabled&&(t.disabled={reason:e.disabled.reason}),t},asCodeActionContext:async function(e,t){if(null==e)return e;let n;return e.only&&u.string(e.only.value)&&(n=[e.only.value]),c.CodeActionContext.create(await M(e.diagnostics,t),n,$(e.triggerKind))},asCodeActionContextSync:function(e){if(null==e)return e;let t;return e.only&&u.string(e.only.value)&&(t=[e.only.value]),c.CodeActionContext.create(F(e.diagnostics),t,$(e.triggerKind))},asInlineValueContext:function(e){return c.InlineValueContext.create(e.frameId,T(e.stoppedLocation))},asCommand:z,asCodeLens:function(e){const t=c.CodeLens.create(T(e.range));return e.command&&(t.command=z(e.command)),e instanceof h.default&&e.data&&(t.data=e.data),t},asFormattingOptions:function(e,t){const n={tabSize:e.tabSize,insertSpaces:e.insertSpaces};return t.trimTrailingWhitespace&&(n.trimTrailingWhitespace=!0),t.trimFinalNewlines&&(n.trimFinalNewlines=!0),t.insertFinalNewline&&(n.insertFinalNewline=!0),n},asDocumentSymbolParams:function(e){return{textDocument:i(e)}},asCodeLensParams:function(e){return{textDocument:i(e)}},asDocumentLink:function(e){const t=c.DocumentLink.create(T(e.range));e.target&&(t.target=n(e.target)),void 0!==e.tooltip&&(t.tooltip=e.tooltip);const i=e instanceof p.default?e:void 0;return i&&i.data&&(t.data=i.data),t},asDocumentLinkParams:function(e){return{textDocument:i(e)}},asCallHierarchyItem:function(e){const t={name:e.name,kind:j(e.kind),uri:n(e.uri),range:T(e.range),selectionRange:T(e.selectionRange)};return void 0!==e.detail&&e.detail.length>0&&(t.detail=e.detail),void 0!==e.tags&&(t.tags=A(e.tags)),e instanceof m.default&&void 0!==e.data&&(t.data=e.data),t},asTypeHierarchyItem:function(e){const t={name:e.name,kind:j(e.kind),uri:n(e.uri),range:T(e.range),selectionRange:T(e.selectionRange)};return void 0!==e.detail&&e.detail.length>0&&(t.detail=e.detail),void 0!==e.tags&&(t.tags=A(e.tags)),e instanceof v.default&&void 0!==e.data&&(t.data=e.data),t},asInlayHint:function(e){const t="string"==typeof e.label?e.label:e.label.map(B),n=c.InlayHint.create(R(e.position),t);return void 0!==e.kind&&(n.kind=e.kind),void 0!==e.textEdits&&(n.textEdits=N(e.textEdits)),void 0!==e.tooltip&&(n.tooltip=V(e.tooltip)),void 0!==e.paddingLeft&&(n.paddingLeft=e.paddingLeft),void 0!==e.paddingRight&&(n.paddingRight=e.paddingRight),e instanceof b.default&&void 0!==e.data&&(n.data=e.data),n},asWorkspaceSymbol:function(e){const n=e instanceof y.default?{name:e.name,kind:j(e.kind),location:e.hasRange?P(e.location):{uri:t(e.location.uri)},data:e.data}:{name:e.name,kind:j(e.kind),location:P(e.location)};return void 0!==e.tags&&(n.tags=A(e.tags)),""!==e.containerName&&(n.containerName=e.containerName),n},asInlineCompletionParams:function(e,t,n){return{textDocument:i(e),position:R(t),context:W(n)}},asInlineCompletionContext:W}}},4384:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.ReferencesFeature=void 0;const s=n(1398),a=n(3434),c=n(9810),u=o(n(8820));class l extends c.TextDocumentLanguageFeature{constructor(e){super(e,a.ReferencesRequest.type)}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"textDocument"),"references").dynamicRegistration=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.referencesProvider);n&&this.register({id:u.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideReferences:(e,t,n,i)=>{const r=this._client,o=(e,t,n,i)=>r.sendRequest(a.ReferencesRequest.type,r.code2ProtocolConverter.asReferenceParams(e,t,n),i).then(e=>i.isCancellationRequested?null:r.protocol2CodeConverter.asReferences(e,i),e=>r.handleFailedRequest(a.ReferencesRequest.type,i,e,null)),s=r.middleware;return s.provideReferences?s.provideReferences(e,t,n,i,o):o(e,t,n,i)}};return[this.registerProvider(t,n),n]}registerProvider(e,t){return s.languages.registerReferenceProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}}t.ReferencesFeature=l},4414:function(e,t,n){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(i=function(e){return i=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},i(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=i(e),s=0;s<n.length;s++)"default"!==n[s]&&r(t,e,n[s]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.Mimes=t.Schemes=void 0,t.getDocumentDir=function(e){const n=function(e){if(e.scheme===t.Schemes.notebookCell)for(const t of a.workspace.notebookDocuments)for(const n of t.getCells())if(n.document.uri.toString()===e.toString())return t.uri;return e}(e);return n.scheme===t.Schemes.untitled?a.workspace.workspaceFolders?.[0]?.uri:c.Utils.dirname(n)};const a=s(n(1398)),c=n(5554);t.Schemes=Object.freeze({file:"file",notebookCell:"vscode-notebook-cell",untitled:"untitled"}),t.Mimes=Object.freeze({plain:"text/plain",uriList:"text/uri-list"})},4430:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=o(n(1398));class a extends s.CallHierarchyItem{data;constructor(e,t,n,i,r,o,s){super(e,t,n,i,r,o),void 0!==s&&(this.data=s)}}t.default=a},4437:(e,t,n)=>{"use strict";var i,r,o,s,a,c,u,l,d,h,p,f,g,m,v,y,b,_,C,D,S,w,R,T,P,O,k,x,E,M,F,q,I,N,j,L,A,$,H,W,U,K,z,B,V,G,X,J,Q,Y,Z,ee,te,ne,ie,re,oe,se,ae,ce,ue,le,de,he,pe,fe,ge,me,ve,ye,be,_e,Ce,De,Se,we,Re,Te,Pe,Oe;n.r(t),n.d(t,{AnnotatedTextEdit:()=>R,ChangeAnnotation:()=>S,ChangeAnnotationIdentifier:()=>w,CodeAction:()=>ae,CodeActionContext:()=>se,CodeActionKind:()=>re,CodeActionTriggerKind:()=>oe,CodeDescription:()=>b,CodeLens:()=>ce,Color:()=>d,ColorInformation:()=>h,ColorPresentation:()=>p,Command:()=>C,CompletionItem:()=>z,CompletionItemKind:()=>A,CompletionItemLabelDetails:()=>K,CompletionItemTag:()=>H,CompletionList:()=>B,CreateFile:()=>P,DeleteFile:()=>k,Diagnostic:()=>_,DiagnosticRelatedInformation:()=>m,DiagnosticSeverity:()=>v,DiagnosticTag:()=>y,DocumentHighlight:()=>Y,DocumentHighlightKind:()=>Q,DocumentLink:()=>le,DocumentSymbol:()=>ie,DocumentUri:()=>i,EOL:()=>Me,FoldingRange:()=>g,FoldingRangeKind:()=>f,FormattingOptions:()=>ue,Hover:()=>G,InlayHint:()=>Ce,InlayHintKind:()=>be,InlayHintLabelPart:()=>_e,InlineCompletionContext:()=>Pe,InlineCompletionItem:()=>Se,InlineCompletionList:()=>we,InlineCompletionTriggerKind:()=>Re,InlineValueContext:()=>ye,InlineValueEvaluatableExpression:()=>ve,InlineValueText:()=>ge,InlineValueVariableLookup:()=>me,InsertReplaceEdit:()=>W,InsertTextFormat:()=>$,InsertTextMode:()=>U,LanguageKind:()=>I,Location:()=>u,LocationLink:()=>l,MarkedString:()=>V,MarkupContent:()=>L,MarkupKind:()=>j,OptionalVersionedTextDocumentIdentifier:()=>q,ParameterInformation:()=>X,Position:()=>a,Range:()=>c,RenameFile:()=>O,SelectedCompletionInfo:()=>Te,SelectionRange:()=>de,SemanticTokenModifiers:()=>pe,SemanticTokenTypes:()=>he,SemanticTokens:()=>fe,SignatureInformation:()=>J,SnippetTextEdit:()=>E,StringValue:()=>De,SymbolInformation:()=>te,SymbolKind:()=>Z,SymbolTag:()=>ee,TextDocument:()=>Fe,TextDocumentEdit:()=>T,TextDocumentIdentifier:()=>M,TextDocumentItem:()=>N,TextEdit:()=>D,URI:()=>r,VersionedTextDocumentIdentifier:()=>F,WorkspaceChange:()=>Ee,WorkspaceEdit:()=>x,WorkspaceFolder:()=>Oe,WorkspaceSymbol:()=>ne,integer:()=>o,uinteger:()=>s}),function(e){e.is=function(e){return"string"==typeof e}}(i||(i={})),function(e){e.is=function(e){return"string"==typeof e}}(r||(r={})),function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(o||(o={})),function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(s||(s={})),function(e){e.create=function(e,t){return e===Number.MAX_VALUE&&(e=s.MAX_VALUE),t===Number.MAX_VALUE&&(t=s.MAX_VALUE),{line:e,character:t}},e.is=function(e){const t=e;return qe.objectLiteral(t)&&qe.uinteger(t.line)&&qe.uinteger(t.character)}}(a||(a={})),function(e){e.create=function(e,t,n,i){if(qe.uinteger(e)&&qe.uinteger(t)&&qe.uinteger(n)&&qe.uinteger(i))return{start:a.create(e,t),end:a.create(n,i)};if(a.is(e)&&a.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${i}]`)},e.is=function(e){const t=e;return qe.objectLiteral(t)&&a.is(t.start)&&a.is(t.end)}}(c||(c={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){const t=e;return qe.objectLiteral(t)&&c.is(t.range)&&(qe.string(t.uri)||qe.undefined(t.uri))}}(u||(u={})),function(e){e.create=function(e,t,n,i){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:i}},e.is=function(e){const t=e;return qe.objectLiteral(t)&&c.is(t.targetRange)&&qe.string(t.targetUri)&&c.is(t.targetSelectionRange)&&(c.is(t.originSelectionRange)||qe.undefined(t.originSelectionRange))}}(l||(l={})),function(e){e.create=function(e,t,n,i){return{red:e,green:t,blue:n,alpha:i}},e.is=function(e){const t=e;return qe.objectLiteral(t)&&qe.numberRange(t.red,0,1)&&qe.numberRange(t.green,0,1)&&qe.numberRange(t.blue,0,1)&&qe.numberRange(t.alpha,0,1)}}(d||(d={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){const t=e;return qe.objectLiteral(t)&&c.is(t.range)&&d.is(t.color)}}(h||(h={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){const t=e;return qe.objectLiteral(t)&&qe.string(t.label)&&(qe.undefined(t.textEdit)||D.is(t))&&(qe.undefined(t.additionalTextEdits)||qe.typedArray(t.additionalTextEdits,D.is))}}(p||(p={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(f||(f={})),function(e){e.create=function(e,t,n,i,r,o){const s={startLine:e,endLine:t};return qe.defined(n)&&(s.startCharacter=n),qe.defined(i)&&(s.endCharacter=i),qe.defined(r)&&(s.kind=r),qe.defined(o)&&(s.collapsedText=o),s},e.is=function(e){const t=e;return qe.objectLiteral(t)&&qe.uinteger(t.startLine)&&qe.uinteger(t.startLine)&&(qe.undefined(t.startCharacter)||qe.uinteger(t.startCharacter))&&(qe.undefined(t.endCharacter)||qe.uinteger(t.endCharacter))&&(qe.undefined(t.kind)||qe.string(t.kind))}}(g||(g={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){const t=e;return qe.defined(t)&&u.is(t.location)&&qe.string(t.message)}}(m||(m={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(v||(v={})),function(e){e.Unnecessary=1,e.Deprecated=2}(y||(y={})),function(e){e.is=function(e){const t=e;return qe.objectLiteral(t)&&qe.string(t.href)}}(b||(b={})),function(e){e.create=function(e,t,n,i,r,o){const s={range:e,message:t};return qe.defined(n)&&(s.severity=n),qe.defined(i)&&(s.code=i),qe.defined(r)&&(s.source=r),qe.defined(o)&&(s.relatedInformation=o),s},e.is=function(e){var t;const n=e;return qe.defined(n)&&c.is(n.range)&&qe.string(n.message)&&(qe.number(n.severity)||qe.undefined(n.severity))&&(qe.integer(n.code)||qe.string(n.code)||qe.undefined(n.code))&&(qe.undefined(n.codeDescription)||qe.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(qe.string(n.source)||qe.undefined(n.source))&&(qe.undefined(n.relatedInformation)||qe.typedArray(n.relatedInformation,m.is))}}(_||(_={})),function(e){e.create=function(e,t,...n){const i={title:e,command:t};return qe.defined(n)&&n.length>0&&(i.arguments=n),i},e.is=function(e){const t=e;return qe.defined(t)&&qe.string(t.title)&&(void 0===t.tooltip||qe.string(t.tooltip))&&qe.string(t.command)}}(C||(C={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){const t=e;return qe.objectLiteral(t)&&qe.string(t.newText)&&c.is(t.range)}}(D||(D={})),function(e){e.create=function(e,t,n){const i={label:e};return void 0!==t&&(i.needsConfirmation=t),void 0!==n&&(i.description=n),i},e.is=function(e){const t=e;return qe.objectLiteral(t)&&qe.string(t.label)&&(qe.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(qe.string(t.description)||void 0===t.description)}}(S||(S={})),function(e){e.is=function(e){const t=e;return qe.string(t)}}(w||(w={})),function(e){e.replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},e.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},e.del=function(e,t){return{range:e,newText:"",annotationId:t}},e.is=function(e){const t=e;return D.is(t)&&(S.is(t.annotationId)||w.is(t.annotationId))}}(R||(R={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){const t=e;return qe.defined(t)&&q.is(t.textDocument)&&Array.isArray(t.edits)}}(T||(T={})),function(e){e.create=function(e,t,n){const i={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(i.options=t),void 0!==n&&(i.annotationId=n),i},e.is=function(e){const t=e;return t&&"create"===t.kind&&qe.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||qe.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||qe.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||w.is(t.annotationId))}}(P||(P={})),function(e){e.create=function(e,t,n,i){const r={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(r.options=n),void 0!==i&&(r.annotationId=i),r},e.is=function(e){const t=e;return t&&"rename"===t.kind&&qe.string(t.oldUri)&&qe.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||qe.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||qe.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||w.is(t.annotationId))}}(O||(O={})),function(e){e.create=function(e,t,n){const i={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(i.options=t),void 0!==n&&(i.annotationId=n),i},e.is=function(e){const t=e;return t&&"delete"===t.kind&&qe.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||qe.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||qe.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||w.is(t.annotationId))}}(k||(k={})),function(e){e.is=function(e){const t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every(e=>qe.string(e.kind)?P.is(e)||O.is(e)||k.is(e):T.is(e)))}}(x||(x={}));class ke{constructor(e,t){this.edits=e,this.changeAnnotations=t}insert(e,t,n){let i,r;if(void 0===n?i=D.insert(e,t):w.is(n)?(r=n,i=R.insert(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),r=this.changeAnnotations.manage(n),i=R.insert(e,t,r)),this.edits.push(i),void 0!==r)return r}replace(e,t,n){let i,r;if(void 0===n?i=D.replace(e,t):w.is(n)?(r=n,i=R.replace(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),r=this.changeAnnotations.manage(n),i=R.replace(e,t,r)),this.edits.push(i),void 0!==r)return r}delete(e,t){let n,i;if(void 0===t?n=D.del(e):w.is(t)?(i=t,n=R.del(e,t)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(t),n=R.del(e,i)),this.edits.push(n),void 0!==i)return i}add(e){this.edits.push(e)}all(){return this.edits}clear(){this.edits.splice(0,this.edits.length)}assertChangeAnnotations(e){if(void 0===e)throw new Error("Text edit change is not configured to manage change annotations.")}}!function(e){e.is=function(e){const t=e;return qe.objectLiteral(t)&&c.is(t.range)&&De.isSnippet(t.snippet)&&(void 0===t.annotationId||S.is(t.annotationId)||w.is(t.annotationId))}}(E||(E={}));class xe{constructor(e){this._annotations=void 0===e?Object.create(null):e,this._counter=0,this._size=0}all(){return this._annotations}get size(){return this._size}manage(e,t){let n;if(w.is(e)?n=e:(n=this.nextId(),t=e),void 0!==this._annotations[n])throw new Error(`Id ${n} is already in use.`);if(void 0===t)throw new Error(`No annotation provided for id ${n}`);return this._annotations[n]=t,this._size++,n}nextId(){return this._counter++,this._counter.toString()}}class Ee{constructor(e){this._textEditChanges=Object.create(null),void 0!==e?(this._workspaceEdit=e,e.documentChanges?(this._changeAnnotations=new xe(e.changeAnnotations),e.changeAnnotations=this._changeAnnotations.all(),e.documentChanges.forEach(e=>{if(T.is(e)){const t=new ke(e.edits,this._changeAnnotations);this._textEditChanges[e.textDocument.uri]=t}})):e.changes&&Object.keys(e.changes).forEach(t=>{const n=new ke(e.changes[t]);this._textEditChanges[t]=n})):this._workspaceEdit={}}get edit(){return this.initDocumentChanges(),void 0!==this._changeAnnotations&&(0===this._changeAnnotations.size?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit}getTextEditChange(e){if(q.is(e)){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");const t={uri:e.uri,version:e.version};let n=this._textEditChanges[t.uri];if(!n){const e=[],i={textDocument:t,edits:e};this._workspaceEdit.documentChanges.push(i),n=new ke(e,this._changeAnnotations),this._textEditChanges[t.uri]=n}return n}{if(this.initChanges(),void 0===this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");let t=this._textEditChanges[e];if(!t){const n=[];this._workspaceEdit.changes[e]=n,t=new ke(n),this._textEditChanges[e]=t}return t}}initDocumentChanges(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._changeAnnotations=new xe,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())}initChanges(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._workspaceEdit.changes=Object.create(null))}createFile(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");let i,r,o;if(S.is(t)||w.is(t)?i=t:n=t,void 0===i?r=P.create(e,n):(o=w.is(i)?i:this._changeAnnotations.manage(i),r=P.create(e,n,o)),this._workspaceEdit.documentChanges.push(r),void 0!==o)return o}renameFile(e,t,n,i){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");let r,o,s;if(S.is(n)||w.is(n)?r=n:i=n,void 0===r?o=O.create(e,t,i):(s=w.is(r)?r:this._changeAnnotations.manage(r),o=O.create(e,t,i,s)),this._workspaceEdit.documentChanges.push(o),void 0!==s)return s}deleteFile(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");let i,r,o;if(S.is(t)||w.is(t)?i=t:n=t,void 0===i?r=k.create(e,n):(o=w.is(i)?i:this._changeAnnotations.manage(i),r=k.create(e,n,o)),this._workspaceEdit.documentChanges.push(r),void 0!==o)return o}}!function(e){e.create=function(e){return{uri:e}},e.is=function(e){const t=e;return qe.defined(t)&&qe.string(t.uri)}}(M||(M={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){const t=e;return qe.defined(t)&&qe.string(t.uri)&&qe.integer(t.version)}}(F||(F={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){const t=e;return qe.defined(t)&&qe.string(t.uri)&&(null===t.version||qe.integer(t.version))}}(q||(q={})),function(e){e.ABAP="abap",e.WindowsBat="bat",e.BibTeX="bibtex",e.Clojure="clojure",e.Coffeescript="coffeescript",e.C="c",e.CPP="cpp",e.CSharp="csharp",e.CSS="css",e.D="d",e.Delphi="pascal",e.Diff="diff",e.Dart="dart",e.Dockerfile="dockerfile",e.Elixir="elixir",e.Erlang="erlang",e.FSharp="fsharp",e.GitCommit="git-commit",e.GitRebase="rebase",e.Go="go",e.Groovy="groovy",e.Handlebars="handlebars",e.Haskell="haskell",e.HTML="html",e.Ini="ini",e.Java="java",e.JavaScript="javascript",e.JavaScriptReact="javascriptreact",e.JSON="json",e.LaTeX="latex",e.Less="less",e.Lua="lua",e.Makefile="makefile",e.Markdown="markdown",e.ObjectiveC="objective-c",e.ObjectiveCPP="objective-cpp",e.Pascal="pascal",e.Perl="perl",e.Perl6="perl6",e.PHP="php",e.Powershell="powershell",e.Pug="jade",e.Python="python",e.R="r",e.Razor="razor",e.Ruby="ruby",e.Rust="rust",e.SCSS="scss",e.SASS="sass",e.Scala="scala",e.ShaderLab="shaderlab",e.ShellScript="shellscript",e.SQL="sql",e.Swift="swift",e.TypeScript="typescript",e.TypeScriptReact="typescriptreact",e.TeX="tex",e.VisualBasic="vb",e.XML="xml",e.XSL="xsl",e.YAML="yaml"}(I||(I={})),function(e){e.create=function(e,t,n,i){return{uri:e,languageId:t,version:n,text:i}},e.is=function(e){const t=e;return qe.defined(t)&&qe.string(t.uri)&&qe.string(t.languageId)&&qe.integer(t.version)&&qe.string(t.text)}}(N||(N={})),function(e){e.PlainText="plaintext",e.Markdown="markdown",e.is=function(t){const n=t;return n===e.PlainText||n===e.Markdown}}(j||(j={})),function(e){e.is=function(e){const t=e;return qe.objectLiteral(e)&&j.is(t.kind)&&qe.string(t.value)}}(L||(L={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(A||(A={})),function(e){e.PlainText=1,e.Snippet=2}($||($={})),function(e){e.Deprecated=1}(H||(H={})),function(e){e.create=function(e,t,n){return{newText:e,insert:t,replace:n}},e.is=function(e){const t=e;return t&&qe.string(t.newText)&&c.is(t.insert)&&c.is(t.replace)}}(W||(W={})),function(e){e.asIs=1,e.adjustIndentation=2}(U||(U={})),function(e){e.is=function(e){const t=e;return t&&(qe.string(t.detail)||void 0===t.detail)&&(qe.string(t.description)||void 0===t.description)}}(K||(K={})),function(e){e.create=function(e){return{label:e}}}(z||(z={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(B||(B={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){const t=e;return qe.string(t)||qe.objectLiteral(t)&&qe.string(t.language)&&qe.string(t.value)}}(V||(V={})),function(e){e.is=function(e){const t=e;return!!t&&qe.objectLiteral(t)&&(L.is(t.contents)||V.is(t.contents)||qe.typedArray(t.contents,V.is))&&(void 0===e.range||c.is(e.range))}}(G||(G={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(X||(X={})),function(e){e.create=function(e,t,...n){const i={label:e};return qe.defined(t)&&(i.documentation=t),qe.defined(n)?i.parameters=n:i.parameters=[],i}}(J||(J={})),function(e){e.Text=1,e.Read=2,e.Write=3}(Q||(Q={})),function(e){e.create=function(e,t){const n={range:e};return qe.number(t)&&(n.kind=t),n}}(Y||(Y={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(Z||(Z={})),function(e){e.Deprecated=1}(ee||(ee={})),function(e){e.create=function(e,t,n,i,r){const o={name:e,kind:t,location:{uri:i,range:n}};return r&&(o.containerName=r),o}}(te||(te={})),function(e){e.create=function(e,t,n,i){return void 0!==i?{name:e,kind:t,location:{uri:n,range:i}}:{name:e,kind:t,location:{uri:n}}}}(ne||(ne={})),function(e){e.create=function(e,t,n,i,r,o){const s={name:e,detail:t,kind:n,range:i,selectionRange:r};return void 0!==o&&(s.children=o),s},e.is=function(e){const t=e;return t&&qe.string(t.name)&&qe.number(t.kind)&&c.is(t.range)&&c.is(t.selectionRange)&&(void 0===t.detail||qe.string(t.detail))&&(void 0===t.deprecated||qe.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))}}(ie||(ie={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorMove="refactor.move",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll",e.Notebook="notebook"}(re||(re={})),function(e){e.Invoked=1,e.Automatic=2}(oe||(oe={})),function(e){e.create=function(e,t,n){const i={diagnostics:e};return null!=t&&(i.only=t),null!=n&&(i.triggerKind=n),i},e.is=function(e){const t=e;return qe.defined(t)&&qe.typedArray(t.diagnostics,_.is)&&(void 0===t.only||qe.typedArray(t.only,qe.string))&&(void 0===t.triggerKind||t.triggerKind===oe.Invoked||t.triggerKind===oe.Automatic)}}(se||(se={})),function(e){e.create=function(e,t,n){const i={title:e};let r=!0;return"string"==typeof t?(r=!1,i.kind=t):C.is(t)?i.command=t:i.edit=t,r&&void 0!==n&&(i.kind=n),i},e.is=function(e){const t=e;return t&&qe.string(t.title)&&(void 0===t.diagnostics||qe.typedArray(t.diagnostics,_.is))&&(void 0===t.kind||qe.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||C.is(t.command))&&(void 0===t.isPreferred||qe.boolean(t.isPreferred))&&(void 0===t.edit||x.is(t.edit))}}(ae||(ae={})),function(e){e.create=function(e,t){const n={range:e};return qe.defined(t)&&(n.data=t),n},e.is=function(e){const t=e;return qe.defined(t)&&c.is(t.range)&&(qe.undefined(t.command)||C.is(t.command))}}(ce||(ce={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){const t=e;return qe.defined(t)&&qe.uinteger(t.tabSize)&&qe.boolean(t.insertSpaces)}}(ue||(ue={})),function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){const t=e;return qe.defined(t)&&c.is(t.range)&&(qe.undefined(t.target)||qe.string(t.target))}}(le||(le={})),function(e){e.create=function(e,t){return{range:e,parent:t}},e.is=function(t){const n=t;return qe.objectLiteral(n)&&c.is(n.range)&&(void 0===n.parent||e.is(n.parent))}}(de||(de={})),function(e){e.namespace="namespace",e.type="type",e.class="class",e.enum="enum",e.interface="interface",e.struct="struct",e.typeParameter="typeParameter",e.parameter="parameter",e.variable="variable",e.property="property",e.enumMember="enumMember",e.event="event",e.function="function",e.method="method",e.macro="macro",e.keyword="keyword",e.modifier="modifier",e.comment="comment",e.string="string",e.number="number",e.regexp="regexp",e.operator="operator",e.decorator="decorator",e.label="label"}(he||(he={})),function(e){e.declaration="declaration",e.definition="definition",e.readonly="readonly",e.static="static",e.deprecated="deprecated",e.abstract="abstract",e.async="async",e.modification="modification",e.documentation="documentation",e.defaultLibrary="defaultLibrary"}(pe||(pe={})),function(e){e.is=function(e){const t=e;return qe.objectLiteral(t)&&(void 0===t.resultId||"string"==typeof t.resultId)&&Array.isArray(t.data)&&(0===t.data.length||"number"==typeof t.data[0])}}(fe||(fe={})),function(e){e.create=function(e,t){return{range:e,text:t}},e.is=function(e){const t=e;return null!=t&&c.is(t.range)&&qe.string(t.text)}}(ge||(ge={})),function(e){e.create=function(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}},e.is=function(e){const t=e;return null!=t&&c.is(t.range)&&qe.boolean(t.caseSensitiveLookup)&&(qe.string(t.variableName)||void 0===t.variableName)}}(me||(me={})),function(e){e.create=function(e,t){return{range:e,expression:t}},e.is=function(e){const t=e;return null!=t&&c.is(t.range)&&(qe.string(t.expression)||void 0===t.expression)}}(ve||(ve={})),function(e){e.create=function(e,t){return{frameId:e,stoppedLocation:t}},e.is=function(e){const t=e;return qe.defined(t)&&c.is(e.stoppedLocation)}}(ye||(ye={})),function(e){e.Type=1,e.Parameter=2,e.is=function(e){return 1===e||2===e}}(be||(be={})),function(e){e.create=function(e){return{value:e}},e.is=function(e){const t=e;return qe.objectLiteral(t)&&(void 0===t.tooltip||qe.string(t.tooltip)||L.is(t.tooltip))&&(void 0===t.location||u.is(t.location))&&(void 0===t.command||C.is(t.command))}}(_e||(_e={})),function(e){e.create=function(e,t,n){const i={position:e,label:t};return void 0!==n&&(i.kind=n),i},e.is=function(e){const t=e;return qe.objectLiteral(t)&&a.is(t.position)&&(qe.string(t.label)||qe.typedArray(t.label,_e.is))&&(void 0===t.kind||be.is(t.kind))&&void 0===t.textEdits||qe.typedArray(t.textEdits,D.is)&&(void 0===t.tooltip||qe.string(t.tooltip)||L.is(t.tooltip))&&(void 0===t.paddingLeft||qe.boolean(t.paddingLeft))&&(void 0===t.paddingRight||qe.boolean(t.paddingRight))}}(Ce||(Ce={})),function(e){e.createSnippet=function(e){return{kind:"snippet",value:e}},e.isSnippet=function(e){const t=e;return qe.objectLiteral(t)&&"snippet"===t.kind&&qe.string(t.value)}}(De||(De={})),function(e){e.create=function(e,t,n,i){return{insertText:e,filterText:t,range:n,command:i}}}(Se||(Se={})),function(e){e.create=function(e){return{items:e}}}(we||(we={})),function(e){e.Invoked=1,e.Automatic=2}(Re||(Re={})),function(e){e.create=function(e,t){return{range:e,text:t}}}(Te||(Te={})),function(e){e.create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}}}(Pe||(Pe={})),function(e){e.is=function(e){const t=e;return qe.objectLiteral(t)&&r.is(t.uri)&&qe.string(t.name)}}(Oe||(Oe={}));const Me=["\n","\r\n","\r"];var Fe,qe;!function(e){function t(e,n){if(e.length<=1)return e;const i=e.length/2|0,r=e.slice(0,i),o=e.slice(i);t(r,n),t(o,n);let s=0,a=0,c=0;for(;s<r.length&&a<o.length;){const t=n(r[s],o[a]);e[c++]=t<=0?r[s++]:o[a++]}for(;s<r.length;)e[c++]=r[s++];for(;a<o.length;)e[c++]=o[a++];return e}e.create=function(e,t,n,i){return new Ie(e,t,n,i)},e.is=function(e){const t=e;return!!(qe.defined(t)&&qe.string(t.uri)&&(qe.undefined(t.languageId)||qe.string(t.languageId))&&qe.uinteger(t.lineCount)&&qe.func(t.getText)&&qe.func(t.positionAt)&&qe.func(t.offsetAt))},e.applyEdits=function(e,n){let i=e.getText();const r=t(n,(e,t)=>{const n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n});let o=i.length;for(let t=r.length-1;t>=0;t--){const n=r[t],s=e.offsetAt(n.range.start),a=e.offsetAt(n.range.end);if(!(a<=o))throw new Error("Overlapping edit");i=i.substring(0,s)+n.newText+i.substring(a,i.length),o=s}return i}}(Fe||(Fe={}));class Ie{constructor(e,t,n,i){this._uri=e,this._languageId=t,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(void 0===this._lineOffsets){const e=[],t=this._content;let n=!0;for(let i=0;i<t.length;i++){n&&(e.push(i),n=!1);const r=t.charAt(i);n="\r"===r||"\n"===r,"\r"===r&&i+1<t.length&&"\n"===t.charAt(i+1)&&i++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let n=0,i=t.length;if(0===i)return a.create(0,e);for(;n<i;){const r=Math.floor((n+i)/2);t[r]>e?i=r:n=r+1}const r=n-1;return a.create(r,e-t[r])}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const n=t[e.line],i=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,i),n)}get lineCount(){return this.getLineOffsets().length}}!function(e){const t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,i){return"[object Number]"===t.call(e)&&n<=e&&e<=i},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(qe||(qe={}))},4486:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DeclarationRequest=void 0;const i=n(7096);var r;!function(e){e.method="textDocument/declaration",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.DeclarationRequest=r={}))},4523:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.startClient=async function(e,t,n){const u=(0,o.getCustomDataSource)(e.subscriptions),l=["css","scss","less"],d=l.map(e=>({languageId:e,settingId:`${e}.format.enable`,provider:void 0})),h={documentSelector:l,synchronize:{configurationSection:["css","scss","less"]},initializationOptions:{handledSchemas:["file"],provideFormatter:!1,customCapabilities:{rangeFormatting:{editLimit:1e4}}},middleware:{provideCompletionItem(e,t,n,r,o){function s(e){const n=e.range;n instanceof i.Range&&n.end.isAfter(t)&&n.start.isBeforeOrEqual(t)&&(e.range={inserting:new i.Range(n.start,t),replacing:n})}function a(e){e.kind===i.CompletionItemKind.Color&&(e.label={label:e.label,description:e.documentation})}function c(e){return e&&((Array.isArray(e)?e:e.items).forEach(s),(Array.isArray(e)?e:e.items).forEach(a)),e}const u=o(e,t,n,r);return(l=u)&&l.then?u.then(c):c(u);var l}}},p=t("css",i.l10n.t("CSS Language Server"),h);p.registerProposedFeatures(),await p.start(),p.sendNotification(a.type,u.uris),u.onDidChange(()=>{p.sendNotification(a.type,u.uris)});for(const t of d)f(t),e.subscriptions.push({dispose:()=>t.provider?.dispose()}),e.subscriptions.push(i.workspace.onDidChangeConfiguration(e=>e.affectsConfiguration(t.settingId)&&f(t)));function f(e){const t=i.workspace.getConfiguration().get(e.settingId);!t&&e.provider?(e.provider.dispose(),e.provider=void 0):t&&!e.provider&&(e.provider=i.languages.registerDocumentRangeFormattingEditProvider(e.languageId,{provideDocumentRangeFormattingEdits(t,n,o,s){const a=i.workspace.getConfiguration("files",t),u={trimTrailingWhitespace:a.get("trimTrailingWhitespace"),trimFinalNewlines:a.get("trimFinalNewlines"),insertFinalNewline:a.get("insertFinalNewline")},l={textDocument:p.code2ProtocolConverter.asTextDocumentIdentifier(t),range:p.code2ProtocolConverter.asRange(n),options:p.code2ProtocolConverter.asFormattingOptions(o,u)},d=i.workspace.getConfiguration(e.languageId,t).get("format");if(d)for(const e of c){const t=d[e];null!=t&&(l.options[e]=t)}return p.sendRequest(r.DocumentRangeFormattingRequest.type,l,s).then(p.protocol2CodeConverter.asTextEdits,e=>(p.handleFailedRequest(r.DocumentRangeFormattingRequest.type,void 0,e,[]),Promise.resolve([])))}}))}return(0,s.serveFileSystemRequests)(p,n),e.subscriptions.push(function(){const e=/^(\s*)(\/(\*\s*(#\w*)?)?)?$/;return i.languages.registerCompletionItemProvider(l,{provideCompletionItems(t,n){const r=t.getText(new i.Range(new i.Position(n.line,0),n)).match(e);if(r){const e=new i.Range(new i.Position(n.line,r[1].length),n),t=new i.CompletionItem("#region",i.CompletionItemKind.Snippet);t.range=e,i.TextEdit.replace(e,"/* #region */"),t.insertText=new i.SnippetString("/* #region $1*/"),t.documentation=i.l10n.t("Folding Region Start"),t.filterText=r[2],t.sortText="za";const o=new i.CompletionItem("#endregion",i.CompletionItemKind.Snippet);return o.range=e,o.insertText="/* #endregion */",o.documentation=i.l10n.t("Folding Region End"),o.sortText="zb",o.filterText=r[2],[t,o]}return null}})}()),i.commands.registerCommand("_css.applyCodeAction",function(e,t,n){const r=i.window.activeTextEditor;r&&r.document.uri.toString()===e&&(r.document.version!==t&&i.window.showInformationMessage(i.l10n.t("CSS fix is outdated and can't be applied to the document.")),r.edit(e=>{for(const t of n)e.replace(p.protocol2CodeConverter.asRange(t.range),t.newText)}).then(e=>{e||i.window.showErrorMessage(i.l10n.t("Failed to apply CSS fix to the document. Please consider opening an issue with steps to reproduce."))}))}),p};const i=n(1398),r=n(2685),o=n(7904),s=n(6149);var a;!function(e){e.type=new r.NotificationType("css/customDataChanged")}(a||(a={}));const c=["newlineBetweenSelectors","newlineBetweenRules","spaceAroundSelectorSeparator","braceStyle","preserveNewLines","maxPreserveNewLines"]},4656:function(e,t,n){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(i=function(e){return i=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},i(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=i(e),s=0;s<n.length;s++)"default"!==n[s]&&r(t,e,n[s]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.registerDropOrPasteResourceSupport=function(e){const t=new d;return c.Disposable.from(c.languages.registerDocumentDropEditProvider(e,t,{providedDropEditKinds:[t.kind],dropMimeTypes:[u.Mimes.uriList,"files"]}),c.languages.registerDocumentPasteEditProvider(e,t,{providedPasteEditKinds:[t.kind],pasteMimeTypes:[u.Mimes.uriList,"files"]}))};const a=s(n(6928)),c=s(n(1398)),u=n(4414),l=n(9629);class d{constructor(){this.kind=c.DocumentDropOrPasteEditKind.Empty.append("css","link","url")}async provideDocumentDropEdits(e,t,n,i){const r=await this.getUriList(n);if(!r.entries.length||i.isCancellationRequested)return;const o=await this.createUriListSnippet(e.uri,r);return o&&!i.isCancellationRequested?{kind:this.kind,title:o.label,insertText:o.snippet.value,yieldTo:this.pasteAsCssUrlByDefault(e,t)?[]:[c.DocumentDropOrPasteEditKind.Empty.append("uri")]}:void 0}async provideDocumentPasteEdits(e,t,n,i,r){const o=await this.getUriList(n);if(!o.entries.length||r.isCancellationRequested)return;const s=await this.createUriListSnippet(e.uri,o);return s&&!r.isCancellationRequested?[{kind:this.kind,title:s.label,insertText:s.snippet.value,yieldTo:this.pasteAsCssUrlByDefault(e,t[0].start)?[]:[c.DocumentDropOrPasteEditKind.Empty.append("uri")]}]:void 0}async getUriList(e){const t=await(e.get(u.Mimes.uriList)?.asString());if(t)return l.UriList.from(t);const n=[];for(const[t,i]of e){const e=i.asFile();e?.uri&&n.push(e.uri)}return new l.UriList(n.map(e=>({uri:e,str:e.toString(!0)})))}async createUriListSnippet(e,t){if(!t.entries.length)return;const n=new c.SnippetString;for(let i=0;i<t.entries.length;i++){const r=t.entries[i],o=h((0,u.getDocumentDir)(e),r.uri)??r.str;n.appendText(`url(${o})`),i!==t.entries.length-1&&n.appendText(" ")}return{snippet:n,label:t.entries.length>1?c.l10n.t("Insert url() Functions"):c.l10n.t("Insert url() Function")}}pasteAsCssUrlByDefault(e,t){const n=/url\(.+?\)/gi;for(const i of Array.from(e.lineAt(t.line).text.matchAll(n)))if(t.character>i.index&&t.character<i.index+i[0].length)return!1;return!0}}function h(e,t){if(e&&e.scheme===t.scheme&&e.authority===t.authority){if(t.scheme===u.Schemes.file){const n=a.relative(e.fsPath,t.fsPath);return a.posix.normalize(n.split(a.sep).join(a.posix.sep))}return a.posix.relative(e.path,t.path)}}},4735:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.DiagnosticRefreshRequest=t.WorkspaceDiagnosticRequest=t.DocumentDiagnosticRequest=t.DocumentDiagnosticReportKind=t.DiagnosticServerCancellationData=void 0;const s=n(9765),a=o(n(7786)),c=n(7096);var u,l,d,h,p;!function(e){e.is=function(e){const t=e;return t&&a.boolean(t.retriggerRequest)}}(u||(t.DiagnosticServerCancellationData=u={})),function(e){e.Full="full",e.Unchanged="unchanged"}(l||(t.DocumentDiagnosticReportKind=l={})),function(e){e.method="textDocument/diagnostic",e.messageDirection=c.MessageDirection.clientToServer,e.type=new c.ProtocolRequestType(e.method),e.partialResult=new s.ProgressType}(d||(t.DocumentDiagnosticRequest=d={})),function(e){e.method="workspace/diagnostic",e.messageDirection=c.MessageDirection.clientToServer,e.type=new c.ProtocolRequestType(e.method),e.partialResult=new s.ProgressType}(h||(t.WorkspaceDiagnosticRequest=h={})),function(e){e.method="workspace/diagnostic/refresh",e.messageDirection=c.MessageDirection.serverToClient,e.type=new c.ProtocolRequestType0(e.method)}(p||(t.DiagnosticRefreshRequest=p={}))},4752:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=o(n(1398));class a extends s.CompletionItem{data;fromEdit;documentationFormat;originalItemKind;deprecated;insertTextMode;constructor(e){super(e)}}t.default=a},4782:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.CancellationTokenSource=t.CancellationToken=void 0;const a=s(n(9042)),c=o(n(6357)),u=n(6712);var l;!function(e){e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:u.Event.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:u.Event.None}),e.is=function(t){const n=t;return n&&(n===e.None||n===e.Cancelled||c.boolean(n.isCancellationRequested)&&!!n.onCancellationRequested)}}(l||(t.CancellationToken=l={}));const d=Object.freeze(function(e,t){const n=(0,a.default)().timer.setTimeout(e.bind(t),0);return{dispose(){n.dispose()}}});class h{_isCancelled=!1;_emitter;cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?d:(this._emitter||(this._emitter=new u.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}t.CancellationTokenSource=class{_token;get token(){return this._token||(this._token=new h),this._token}cancel(){this._token?this._token.cancel():this._token=l.Cancelled}dispose(){this._token?this._token instanceof h&&this._token.dispose():this._token=l.None}}},4940:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlayHintRefreshRequest=t.InlayHintResolveRequest=t.InlayHintRequest=void 0;const i=n(7096);var r,o,s;!function(e){e.method="textDocument/inlayHint",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.InlayHintRequest=r={})),function(e){e.method="inlayHint/resolve",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(o||(t.InlayHintResolveRequest=o={})),function(e){e.method="workspace/inlayHint/refresh",e.messageDirection=i.MessageDirection.serverToClient,e.type=new i.ProtocolRequestType0(e.method)}(s||(t.InlayHintRefreshRequest=s={}))},5122:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.TextDocumentContentFeature=void 0;const s=o(n(1398)),a=n(3434),c=n(9810),u=o(n(8820));t.TextDocumentContentFeature=class{_client;_registrations=new Map;constructor(e){this._client=e}getState(){const e=this._registrations.size>0;return{kind:"workspace",id:a.TextDocumentContentRequest.method,registrations:e}}get registrationType(){return a.TextDocumentContentRequest.type}getProviders(){const e=[];for(const t of this._registrations.values())e.push(...t.providers);return e}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"workspace"),"textDocumentContent").dynamicRegistration=!0}initialize(e){const t=this._client;if(t.onRequest(a.TextDocumentContentRefreshRequest.type,async e=>{const n=t.protocol2CodeConverter.asUri(e.uri);for(const e of this._registrations.values())for(const t of e.providers)t.scheme!==n.scheme&&t.onDidChangeEmitter.fire(n)}),!e?.workspace?.textDocumentContent)return;const n=e.workspace.textDocumentContent,i=a.StaticRegistrationOptions.hasId(n)?n.id:u.generateUuid();this.register({id:i,registerOptions:n})}register(e){const t=[],n=[];for(const i of e.registerOptions.schemes){const[e,r]=this.registerTextDocumentContentProvider(i);t.push(r),n.push(e)}this._registrations.set(e.id,{disposable:s.Disposable.from(...n),providers:t})}registerTextDocumentContentProvider(e){const t=new s.EventEmitter,n={onDidChange:t.event,provideTextDocumentContent:(e,t)=>{const n=this._client,i=(e,t)=>{const i={uri:n.code2ProtocolConverter.asUri(e)};return n.sendRequest(a.TextDocumentContentRequest.type,i,t).then(e=>t.isCancellationRequested?null:e.text,e=>n.handleFailedRequest(a.TextDocumentContentRequest.type,t,e,null))},r=n.middleware;return r.provideTextDocumentContent?r.provideTextDocumentContent(e,t,i):i(e,t)}};return[s.workspace.registerTextDocumentContentProvider(e,n),{scheme:e,onDidChangeEmitter:t,provider:n}]}unregister(e){const t=this._registrations.get(e);void 0!==t&&(this._registrations.delete(e),t.disposable.dispose())}clear(){this._registrations.forEach(e=>{e.disposable.dispose()}),this._registrations.clear()}}},5140:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.LRUCache=t.LinkedMap=t.Touch=void 0,function(e){e.None=0,e.First=1,e.AsOld=e.First,e.Last=2,e.AsNew=e.Last}(n||(t.Touch=n={}));class i{[Symbol.toStringTag]="LinkedMap";_map;_head;_tail;_size;_state;constructor(){this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){return this._head?.value}get last(){return this._tail?.value}before(e){const t=this._map.get(e);return t?t.previous?.value:void 0}after(e){const t=this._map.get(e);return t?t.next?.value:void 0}has(e){return this._map.has(e)}get(e,t=n.None){const i=this._map.get(e);if(i)return t!==n.None&&this.touch(i,t),i.value}set(e,t,i=n.None){let r=this._map.get(e);if(r)r.value=t,i!==n.None&&this.touch(r,i);else{switch(r={key:e,value:t,next:void 0,previous:void 0},i){case n.None:this.addItemLast(r);break;case n.First:this.addItemFirst(r);break;case n.Last:default:this.addItemLast(r)}this._map.set(e,r),this._size++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this._map.get(e);if(t)return this._map.delete(e),this.removeItem(t),this._size--,t.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");const e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,t){const n=this._state;let i=this._head;for(;i;){if(t?e.bind(t)(i.value,i.key,this):e(i.value,i.key,this),this._state!==n)throw new Error("LinkedMap got modified during iteration.");i=i.next}}keys(){const e=this._state;let t=this._head;const n={[Symbol.iterator]:()=>n,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(t){const e={value:t.key,done:!1};return t=t.next,e}return{value:void 0,done:!0}}};return n}values(){const e=this._state;let t=this._head;const n={[Symbol.iterator]:()=>n,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(t){const e={value:t.value,done:!1};return t=t.next,e}return{value:void 0,done:!0}}};return n}entries(){const e=this._state;let t=this._head;const n={[Symbol.iterator]:()=>n,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(t){const e={value:[t.key,t.value],done:!1};return t=t.next,e}return{value:void 0,done:!0}}};return n}[Symbol.iterator](){return this.entries()}trimOld(e){if(e>=this.size)return;if(0===e)return void this.clear();let t=this._head,n=this.size;for(;t&&n>e;)this._map.delete(t.key),t=t.next,n--;this._head=t,this._size=n,t&&(t.previous=void 0),this._state++}addItemFirst(e){if(this._head||this._tail){if(!this._head)throw new Error("Invalid list");e.next=this._head,this._head.previous=e}else this._tail=e;this._head=e,this._state++}addItemLast(e){if(this._head||this._tail){if(!this._tail)throw new Error("Invalid list");e.previous=this._tail,this._tail.next=e}else this._head=e;this._tail=e,this._state++}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this._head=e.next}else if(e===this._tail){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this._tail=e.previous}else{const t=e.next,n=e.previous;if(!t||!n)throw new Error("Invalid list");t.previous=n,n.next=t}e.next=void 0,e.previous=void 0,this._state++}touch(e,t){if(!this._head||!this._tail)throw new Error("Invalid list");if(t===n.First||t===n.Last)if(t===n.First){if(e===this._head)return;const t=e.next,n=e.previous;e===this._tail?(n.next=void 0,this._tail=n):(t.previous=n,n.next=t),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e,this._state++}else if(t===n.Last){if(e===this._tail)return;const t=e.next,n=e.previous;e===this._head?(t.previous=void 0,this._head=t):(t.previous=n,n.next=t),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e,this._state++}}toJSON(){const e=[];return this.forEach((t,n)=>{e.push([n,t])}),e}fromJSON(e){this.clear();for(const[t,n]of e)this.set(t,n)}}t.LinkedMap=i,t.LRUCache=class extends i{_limit;_ratio;constructor(e,t=1){super(),this._limit=e,this._ratio=Math.min(Math.max(0,t),1)}get limit(){return this._limit}set limit(e){this._limit=e,this.checkTrim()}get ratio(){return this._ratio}set ratio(e){this._ratio=Math.min(Math.max(0,e),1),this.checkTrim()}get(e,t=n.AsNew){return super.get(e,t)}peek(e){return super.get(e,n.None)}set(e,t){return super.set(e,t,n.Last),this.checkTrim(),this}checkTrim(){this.size>this._limit&&this.trimOld(Math.round(this._limit*this._ratio))}}},5146:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.ProtocolDiagnostic=t.DiagnosticCode=void 0;const s=o(n(1398)),a=o(n(1027));var c;!function(e){e.is=function(e){const t=e;return null!=t&&(a.number(t.value)||a.string(t.value))&&a.string(t.target)}}(c||(t.DiagnosticCode=c={}));class u extends s.Diagnostic{data;hasDiagnosticCode;constructor(e,t,n,i){super(e,t,n),this.data=i,this.hasDiagnosticCode=!1}}t.ProtocolDiagnostic=u},5317:e=>{"use strict";e.exports=require("child_process")},5380:(e,t,n)=>{const i=n(7944);e.exports=(e,t,n)=>new i(e,n).compare(new i(t,n))},5554:(e,t,n)=>{"use strict";var i;n.r(t),n.d(t,{URI:()=>r,Utils:()=>o}),(()=>{var e={470:e=>{function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,i="",r=0,o=-1,s=0,a=0;a<=e.length;++a){if(a<e.length)n=e.charCodeAt(a);else{if(47===n)break;n=47}if(47===n){if(o===a-1||1===s);else if(o!==a-1&&2===s){if(i.length<2||2!==r||46!==i.charCodeAt(i.length-1)||46!==i.charCodeAt(i.length-2))if(i.length>2){var c=i.lastIndexOf("/");if(c!==i.length-1){-1===c?(i="",r=0):r=(i=i.slice(0,c)).length-1-i.lastIndexOf("/"),o=a,s=0;continue}}else if(2===i.length||1===i.length){i="",r=0,o=a,s=0;continue}t&&(i.length>0?i+="/..":i="..",r=2)}else i.length>0?i+="/"+e.slice(o+1,a):i=e.slice(o+1,a),r=a-o-1;o=a,s=0}else 46===n&&-1!==s?++s:s=-1}return i}var i={resolve:function(){for(var e,i="",r=!1,o=arguments.length-1;o>=-1&&!r;o--){var s;o>=0?s=arguments[o]:(void 0===e&&(e=process.cwd()),s=e),t(s),0!==s.length&&(i=s+"/"+i,r=47===s.charCodeAt(0))}return i=n(i,!r),r?i.length>0?"/"+i:"/":i.length>0?i:"."},normalize:function(e){if(t(e),0===e.length)return".";var i=47===e.charCodeAt(0),r=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!i)).length||i||(e="."),e.length>0&&r&&(e+="/"),i?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var r=arguments[n];t(r),r.length>0&&(void 0===e?e=r:e+="/"+r)}return void 0===e?".":i.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=i.resolve(e))===(n=i.resolve(n)))return"";for(var r=1;r<e.length&&47===e.charCodeAt(r);++r);for(var o=e.length,s=o-r,a=1;a<n.length&&47===n.charCodeAt(a);++a);for(var c=n.length-a,u=s<c?s:c,l=-1,d=0;d<=u;++d){if(d===u){if(c>u){if(47===n.charCodeAt(a+d))return n.slice(a+d+1);if(0===d)return n.slice(a+d)}else s>u&&(47===e.charCodeAt(r+d)?l=d:0===d&&(l=0));break}var h=e.charCodeAt(r+d);if(h!==n.charCodeAt(a+d))break;47===h&&(l=d)}var p="";for(d=r+l+1;d<=o;++d)d!==o&&47!==e.charCodeAt(d)||(0===p.length?p+="..":p+="/..");return p.length>0?p+n.slice(a+l):(a+=l,47===n.charCodeAt(a)&&++a,n.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),i=47===n,r=-1,o=!0,s=e.length-1;s>=1;--s)if(47===(n=e.charCodeAt(s))){if(!o){r=s;break}}else o=!1;return-1===r?i?"/":".":i&&1===r?"//":e.slice(0,r)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var i,r=0,o=-1,s=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var a=n.length-1,c=-1;for(i=e.length-1;i>=0;--i){var u=e.charCodeAt(i);if(47===u){if(!s){r=i+1;break}}else-1===c&&(s=!1,c=i+1),a>=0&&(u===n.charCodeAt(a)?-1==--a&&(o=i):(a=-1,o=c))}return r===o?o=c:-1===o&&(o=e.length),e.slice(r,o)}for(i=e.length-1;i>=0;--i)if(47===e.charCodeAt(i)){if(!s){r=i+1;break}}else-1===o&&(s=!1,o=i+1);return-1===o?"":e.slice(r,o)},extname:function(e){t(e);for(var n=-1,i=0,r=-1,o=!0,s=0,a=e.length-1;a>=0;--a){var c=e.charCodeAt(a);if(47!==c)-1===r&&(o=!1,r=a+1),46===c?-1===n?n=a:1!==s&&(s=1):-1!==n&&(s=-1);else if(!o){i=a+1;break}}return-1===n||-1===r||0===s||1===s&&n===r-1&&n===i+1?"":e.slice(n,r)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,i=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+i:n+"/"+i:i}(0,e)},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var i,r=e.charCodeAt(0),o=47===r;o?(n.root="/",i=1):i=0;for(var s=-1,a=0,c=-1,u=!0,l=e.length-1,d=0;l>=i;--l)if(47!==(r=e.charCodeAt(l)))-1===c&&(u=!1,c=l+1),46===r?-1===s?s=l:1!==d&&(d=1):-1!==s&&(d=-1);else if(!u){a=l+1;break}return-1===s||-1===c||0===d||1===d&&s===c-1&&s===a+1?-1!==c&&(n.base=n.name=0===a&&o?e.slice(1,c):e.slice(a,c)):(0===a&&o?(n.name=e.slice(1,s),n.base=e.slice(1,c)):(n.name=e.slice(a,s),n.base=e.slice(a,c)),n.ext=e.slice(s,c)),a>0?n.dir=e.slice(0,a-1):o&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};i.posix=i,e.exports=i}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var o=t[i]={exports:{}};return e[i](o,o.exports,n),o.exports}n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{let e;if(n.r(r),n.d(r,{URI:()=>l,Utils:()=>w}),"object"==typeof process)e="win32"===process.platform;else if("object"==typeof navigator){let t=navigator.userAgent;e=t.indexOf("Windows")>=0}const t=/^\w[\w\d+.-]*$/,i=/^\//,o=/^\/\//;function s(e,n){if(!e.scheme&&n)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!t.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!i.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(o.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}const a="",c="/",u=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class l{static isUri(e){return e instanceof l||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}scheme;authority;path;query;fragment;constructor(e,t,n,i,r,o=!1){"object"==typeof e?(this.scheme=e.scheme||a,this.authority=e.authority||a,this.path=e.path||a,this.query=e.query||a,this.fragment=e.fragment||a):(this.scheme=function(e,t){return e||t?e:"file"}(e,o),this.authority=t||a,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==c&&(t=c+t):t=c}return t}(this.scheme,n||a),this.query=i||a,this.fragment=r||a,s(this,o))}get fsPath(){return m(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:i,query:r,fragment:o}=e;return void 0===t?t=this.scheme:null===t&&(t=a),void 0===n?n=this.authority:null===n&&(n=a),void 0===i?i=this.path:null===i&&(i=a),void 0===r?r=this.query:null===r&&(r=a),void 0===o?o=this.fragment:null===o&&(o=a),t===this.scheme&&n===this.authority&&i===this.path&&r===this.query&&o===this.fragment?this:new h(t,n,i,r,o)}static parse(e,t=!1){const n=u.exec(e);return n?new h(n[2]||a,_(n[4]||a),_(n[5]||a),_(n[7]||a),_(n[9]||a),t):new h(a,a,a,a,a)}static file(t){let n=a;if(e&&(t=t.replace(/\\/g,c)),t[0]===c&&t[1]===c){const e=t.indexOf(c,2);-1===e?(n=t.substring(2),t=c):(n=t.substring(2,e),t=t.substring(e)||c)}return new h("file",n,t,a,a)}static from(e){const t=new h(e.scheme,e.authority,e.path,e.query,e.fragment);return s(t,!0),t}toString(e=!1){return v(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof l)return e;{const t=new h(e);return t._formatted=e.external,t._fsPath=e._sep===d?e.fsPath:null,t}}return e}}const d=e?1:void 0;class h extends l{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=m(this,!1)),this._fsPath}toString(e=!1){return e?v(this,!0):(this._formatted||(this._formatted=v(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=d),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const p={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function f(e,t,n){let i,r=-1;for(let o=0;o<e.length;o++){const s=e.charCodeAt(o);if(s>=97&&s<=122||s>=65&&s<=90||s>=48&&s<=57||45===s||46===s||95===s||126===s||t&&47===s||n&&91===s||n&&93===s||n&&58===s)-1!==r&&(i+=encodeURIComponent(e.substring(r,o)),r=-1),void 0!==i&&(i+=e.charAt(o));else{void 0===i&&(i=e.substr(0,o));const t=p[s];void 0!==t?(-1!==r&&(i+=encodeURIComponent(e.substring(r,o)),r=-1),i+=t):-1===r&&(r=o)}}return-1!==r&&(i+=encodeURIComponent(e.substring(r))),void 0!==i?i:e}function g(e){let t;for(let n=0;n<e.length;n++){const i=e.charCodeAt(n);35===i||63===i?(void 0===t&&(t=e.substr(0,n)),t+=p[i]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function m(t,n){let i;return i=t.authority&&t.path.length>1&&"file"===t.scheme?`//${t.authority}${t.path}`:47===t.path.charCodeAt(0)&&(t.path.charCodeAt(1)>=65&&t.path.charCodeAt(1)<=90||t.path.charCodeAt(1)>=97&&t.path.charCodeAt(1)<=122)&&58===t.path.charCodeAt(2)?n?t.path.substr(1):t.path[1].toLowerCase()+t.path.substr(2):t.path,e&&(i=i.replace(/\//g,"\\")),i}function v(e,t){const n=t?g:f;let i="",{scheme:r,authority:o,path:s,query:a,fragment:u}=e;if(r&&(i+=r,i+=":"),(o||"file"===r)&&(i+=c,i+=c),o){let e=o.indexOf("@");if(-1!==e){const t=o.substr(0,e);o=o.substr(e+1),e=t.lastIndexOf(":"),-1===e?i+=n(t,!1,!1):(i+=n(t.substr(0,e),!1,!1),i+=":",i+=n(t.substr(e+1),!1,!0)),i+="@"}o=o.toLowerCase(),e=o.lastIndexOf(":"),-1===e?i+=n(o,!1,!0):(i+=n(o.substr(0,e),!1,!0),i+=o.substr(e))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2)){const e=s.charCodeAt(1);e>=65&&e<=90&&(s=`/${String.fromCharCode(e+32)}:${s.substr(3)}`)}else if(s.length>=2&&58===s.charCodeAt(1)){const e=s.charCodeAt(0);e>=65&&e<=90&&(s=`${String.fromCharCode(e+32)}:${s.substr(2)}`)}i+=n(s,!0,!1)}return a&&(i+="?",i+=n(a,!1,!1)),u&&(i+="#",i+=t?u:f(u,!1,!1)),i}function y(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+y(e.substr(3)):e}}const b=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function _(e){return e.match(b)?e.replace(b,e=>y(e)):e}var C=n(470);const D=C.posix||C,S="/";var w;!function(e){e.joinPath=function(e,...t){return e.with({path:D.join(e.path,...t)})},e.resolvePath=function(e,...t){let n=e.path,i=!1;n[0]!==S&&(n=S+n,i=!0);let r=D.resolve(n,...t);return i&&r[0]===S&&!e.authority&&(r=r.substring(1)),e.with({path:r})},e.dirname=function(e){if(0===e.path.length||e.path===S)return e;let t=D.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)&&(t=""),e.with({path:t})},e.basename=function(e){return D.basename(e.path)},e.extname=function(e){return D.extname(e.path)}}(w||(w={}))})(),i=r})();const{URI:r,Utils:o}=i},5584:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SharedArrayReceiverStrategy=t.SharedArraySenderStrategy=void 0;const i=n(4782);var r;!function(e){e.Continue=0,e.Cancelled=1}(r||(r={})),t.SharedArraySenderStrategy=class{buffers;constructor(){this.buffers=new Map}enableCancellation(e){if(null===e.id)return;const t=new SharedArrayBuffer(4);new Int32Array(t,0,1)[0]=r.Continue,this.buffers.set(e.id,t),e.$cancellationData=t}async sendCancellation(e,t){const n=this.buffers.get(t);if(void 0===n)return;const i=new Int32Array(n,0,1);Atomics.store(i,0,r.Cancelled)}cleanup(e){this.buffers.delete(e)}dispose(){this.buffers.clear()}};class o{data;constructor(e){this.data=new Int32Array(e,0,1)}get isCancellationRequested(){return Atomics.load(this.data,0)===r.Cancelled}get onCancellationRequested(){throw new Error("Cancellation over SharedArrayBuffer doesn't support cancellation events")}}class s{token;constructor(e){this.token=new o(e)}cancel(){}dispose(){}}t.SharedArrayReceiverStrategy=class{kind="request";createCancellationTokenSource(e){const t=e.$cancellationData;return void 0===t?new i.CancellationTokenSource:new s(t)}}},5671:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.forEach=t.mapAsync=t.map=t.clearTestMode=t.setTestMode=t.Semaphore=t.Delayer=void 0;const i=n(3434);t.Delayer=class{defaultDelay;timeout;completionPromise;onSuccess;task;constructor(e){this.defaultDelay=e,this.timeout=void 0,this.completionPromise=void 0,this.onSuccess=void 0,this.task=void 0}trigger(e,t=this.defaultDelay){return this.task=e,t>=0&&this.cancelTimeout(),this.completionPromise||(this.completionPromise=new Promise(e=>{this.onSuccess=e}).then(()=>{this.completionPromise=void 0,this.onSuccess=void 0;const e=this.task();return this.task=void 0,e})),(t>=0||void 0===this.timeout)&&(this.timeout=(0,i.RAL)().timer.setTimeout(()=>{this.timeout=void 0,this.onSuccess(void 0)},t>=0?t:this.defaultDelay)),this.completionPromise}forceDelivery(){if(!this.completionPromise)return;this.cancelTimeout();const e=this.task();return this.completionPromise=void 0,this.onSuccess=void 0,this.task=void 0,e}isTriggered(){return void 0!==this.timeout}cancel(){this.cancelTimeout(),this.completionPromise=void 0}cancelTimeout(){void 0!==this.timeout&&(this.timeout.dispose(),this.timeout=void 0)}},t.Semaphore=class{_capacity;_active;_waiting;constructor(e=1){if(e<=0)throw new Error("Capacity must be greater than 0");this._capacity=e,this._active=0,this._waiting=[]}lock(e){return new Promise((t,n)=>{this._waiting.push({thunk:e,resolve:t,reject:n}),this.runNext()})}get active(){return this._active}runNext(){0!==this._waiting.length&&this._active!==this._capacity&&(0,i.RAL)().timer.setImmediate(()=>this.doRunNext())}doRunNext(){if(0===this._waiting.length||this._active===this._capacity)return;const e=this._waiting.shift();if(this._active++,this._active>this._capacity)throw new Error("To many thunks active");try{const t=e.thunk();t instanceof Promise?t.then(t=>{this._active--,e.resolve(t),this.runNext()},t=>{this._active--,e.reject(t),this.runNext()}):(this._active--,e.resolve(t),this.runNext())}catch(t){this._active--,e.reject(t),this.runNext()}}};let r=!1;t.setTestMode=function(){r=!0},t.clearTestMode=function(){r=!1};class o{yieldAfter;startTime;counter;total;counterInterval;constructor(e=15){this.yieldAfter=!0===r?Math.max(e,2):Math.max(e,15),this.startTime=Date.now(),this.counter=0,this.total=0,this.counterInterval=1}start(){this.counter=0,this.total=0,this.counterInterval=1,this.startTime=Date.now()}shouldYield(){if(++this.counter>=this.counterInterval){const e=Date.now()-this.startTime,t=Math.max(0,this.yieldAfter-e);if(this.total+=this.counter,this.counter=0,e>=this.yieldAfter||t<=1)return this.counterInterval=1,this.total=0,!0;switch(e){case 0:case 1:this.counterInterval=2*this.total}}return!1}}t.map=async function(e,t,n,r){if(0===e.length)return[];const s=new Array(e.length),a=new o(r?.yieldAfter);function c(n){a.start();for(let i=n;i<e.length;i++)if(s[i]=t(e[i]),a.shouldYield())return r?.yieldCallback&&r.yieldCallback(),i+1;return-1}let u=c(0);for(;-1!==u&&(void 0===n||!n.isCancellationRequested);)u=await new Promise(e=>{(0,i.RAL)().timer.setImmediate(()=>{e(c(u))})});return s},t.mapAsync=async function(e,t,n,r){if(0===e.length)return[];const s=new Array(e.length),a=new o(r?.yieldAfter);async function c(i){a.start();for(let o=i;o<e.length;o++)if(s[o]=await t(e[o],n),a.shouldYield())return r?.yieldCallback&&r.yieldCallback(),o+1;return-1}let u=await c(0);for(;-1!==u&&(void 0===n||!n.isCancellationRequested);)u=await new Promise(e=>{(0,i.RAL)().timer.setImmediate(()=>{e(c(u))})});return s},t.forEach=async function(e,t,n,r){if(0===e.length)return;const s=new o(r?.yieldAfter);function a(n){s.start();for(let i=n;i<e.length;i++)if(t(e[i]),s.shouldYield())return r?.yieldCallback&&r.yieldCallback(),i+1;return-1}let c=a(0);for(;-1!==c&&(void 0===n||!n.isCancellationRequested);)c=await new Promise(e=>{(0,i.RAL)().timer.setImmediate(()=>{e(a(c))})})}},5679:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.ProgressPart=void 0;const s=n(1398),a=n(3434),c=o(n(1027));t.ProgressPart=class{_client;_token;_infinite;_reported;_lspProgressDisposable;_progress;_cancellationToken;_tokenDisposable;_resolve;_reject;constructor(e,t,n){this._client=e,this._token=t,this._reported=0,this._infinite=!1,this._lspProgressDisposable=this._client.onProgress(a.WorkDoneProgress.type,this._token,e=>{switch(e.kind){case"begin":this.begin(e);break;case"report":this.report(e);break;case"end":this.done(),n&&n(this)}})}begin(e){this._infinite=void 0===e.percentage,void 0!==this._lspProgressDisposable&&s.window.withProgress({location:s.ProgressLocation.Window,cancellable:e.cancellable,title:e.title},async(t,n)=>{if(void 0!==this._lspProgressDisposable)return this._progress=t,this._cancellationToken=n,this._tokenDisposable=this._cancellationToken.onCancellationRequested(()=>{this._client.sendNotification(a.WorkDoneProgressCancelNotification.type,{token:this._token})}),this.report(e),new Promise((e,t)=>{this._resolve=e,this._reject=t})})}report(e){if(this._infinite&&c.string(e.message))void 0!==this._progress&&this._progress.report({message:e.message});else if(c.number(e.percentage)){const t=Math.max(0,Math.min(e.percentage,100)),n=Math.max(0,t-this._reported);this._reported+=n,void 0!==this._progress&&this._progress.report({message:e.message,increment:n})}}cancel(){this.cleanup(),void 0!==this._reject&&(this._reject(),this._resolve=void 0,this._reject=void 0)}done(){this.cleanup(),void 0!==this._resolve&&(this._resolve(),this._resolve=void 0,this._reject=void 0)}cleanup(){void 0!==this._lspProgressDisposable&&(this._lspProgressDisposable.dispose(),this._lspProgressDisposable=void 0),void 0!==this._tokenDisposable&&(this._tokenDisposable.dispose(),this._tokenDisposable=void 0),this._progress=void 0,this._cancellationToken=void 0}}},5689:(e,t,n)=>{"use strict";const i=n(9691),r=Symbol("max"),o=Symbol("length"),s=Symbol("lengthCalculator"),a=Symbol("allowStale"),c=Symbol("maxAge"),u=Symbol("dispose"),l=Symbol("noDisposeOnSet"),d=Symbol("lruList"),h=Symbol("cache"),p=Symbol("updateAgeOnGet"),f=()=>1,g=(e,t,n)=>{const i=e[h].get(t);if(i){const t=i.value;if(m(e,t)){if(y(e,i),!e[a])return}else n&&(e[p]&&(i.value.now=Date.now()),e[d].unshiftNode(i));return t.value}},m=(e,t)=>{if(!t||!t.maxAge&&!e[c])return!1;const n=Date.now()-t.now;return t.maxAge?n>t.maxAge:e[c]&&n>e[c]},v=e=>{if(e[o]>e[r])for(let t=e[d].tail;e[o]>e[r]&&null!==t;){const n=t.prev;y(e,t),t=n}},y=(e,t)=>{if(t){const n=t.value;e[u]&&e[u](n.key,n.value),e[o]-=n.length,e[h].delete(n.key),e[d].removeNode(t)}};class b{constructor(e,t,n,i,r){this.key=e,this.value=t,this.length=n,this.now=i,this.maxAge=r||0}}const _=(e,t,n,i)=>{let r=n.value;m(e,r)&&(y(e,n),e[a]||(r=void 0)),r&&t.call(i,r.value,r.key,e)};e.exports=class{constructor(e){if("number"==typeof e&&(e={max:e}),e||(e={}),e.max&&("number"!=typeof e.max||e.max<0))throw new TypeError("max must be a non-negative number");this[r]=e.max||1/0;const t=e.length||f;if(this[s]="function"!=typeof t?f:t,this[a]=e.stale||!1,e.maxAge&&"number"!=typeof e.maxAge)throw new TypeError("maxAge must be a number");this[c]=e.maxAge||0,this[u]=e.dispose,this[l]=e.noDisposeOnSet||!1,this[p]=e.updateAgeOnGet||!1,this.reset()}set max(e){if("number"!=typeof e||e<0)throw new TypeError("max must be a non-negative number");this[r]=e||1/0,v(this)}get max(){return this[r]}set allowStale(e){this[a]=!!e}get allowStale(){return this[a]}set maxAge(e){if("number"!=typeof e)throw new TypeError("maxAge must be a non-negative number");this[c]=e,v(this)}get maxAge(){return this[c]}set lengthCalculator(e){"function"!=typeof e&&(e=f),e!==this[s]&&(this[s]=e,this[o]=0,this[d].forEach(e=>{e.length=this[s](e.value,e.key),this[o]+=e.length})),v(this)}get lengthCalculator(){return this[s]}get length(){return this[o]}get itemCount(){return this[d].length}rforEach(e,t){t=t||this;for(let n=this[d].tail;null!==n;){const i=n.prev;_(this,e,n,t),n=i}}forEach(e,t){t=t||this;for(let n=this[d].head;null!==n;){const i=n.next;_(this,e,n,t),n=i}}keys(){return this[d].toArray().map(e=>e.key)}values(){return this[d].toArray().map(e=>e.value)}reset(){this[u]&&this[d]&&this[d].length&&this[d].forEach(e=>this[u](e.key,e.value)),this[h]=new Map,this[d]=new i,this[o]=0}dump(){return this[d].map(e=>!m(this,e)&&{k:e.key,v:e.value,e:e.now+(e.maxAge||0)}).toArray().filter(e=>e)}dumpLru(){return this[d]}set(e,t,n){if((n=n||this[c])&&"number"!=typeof n)throw new TypeError("maxAge must be a number");const i=n?Date.now():0,a=this[s](t,e);if(this[h].has(e)){if(a>this[r])return y(this,this[h].get(e)),!1;const s=this[h].get(e).value;return this[u]&&(this[l]||this[u](e,s.value)),s.now=i,s.maxAge=n,s.value=t,this[o]+=a-s.length,s.length=a,this.get(e),v(this),!0}const p=new b(e,t,a,i,n);return p.length>this[r]?(this[u]&&this[u](e,t),!1):(this[o]+=p.length,this[d].unshift(p),this[h].set(e,this[d].head),v(this),!0)}has(e){if(!this[h].has(e))return!1;const t=this[h].get(e).value;return!m(this,t)}get(e){return g(this,e,!0)}peek(e){return g(this,e,!1)}pop(){const e=this[d].tail;return e?(y(this,e),e.value):null}del(e){y(this,this[h].get(e))}load(e){this.reset();const t=Date.now();for(let n=e.length-1;n>=0;n--){const i=e[n],r=i.e||0;if(0===r)this.set(i.k,i.v);else{const e=r-t;e>0&&this.set(i.k,i.v,e)}}}prune(){this[h].forEach((e,t)=>g(this,t,!1))}}},5860:(e,t,n)=>{const i=n(7944);e.exports=(e,t,n=!1)=>{if(e instanceof i)return e;try{return new i(e,t)}catch(e){if(!n)return null;throw e}}},5908:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ColorPresentationRequest=t.DocumentColorRequest=void 0;const i=n(7096);var r,o;!function(e){e.method="textDocument/documentColor",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.DocumentColorRequest=r={})),function(e){e.method="textDocument/colorPresentation",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(o||(t.ColorPresentationRequest=o={}))},5923:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WorkDoneProgressCancelNotification=t.WorkDoneProgressCreateRequest=t.WorkDoneProgress=void 0;const i=n(9765),r=n(7096);var o,s,a;!function(e){e.type=new i.ProgressType,e.is=function(t){return t===e.type}}(o||(t.WorkDoneProgress=o={})),function(e){e.method="window/workDoneProgress/create",e.messageDirection=r.MessageDirection.serverToClient,e.type=new r.ProtocolRequestType(e.method)}(s||(t.WorkDoneProgressCreateRequest=s={})),function(e){e.method="window/workDoneProgress/cancel",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolNotificationType(e.method)}(a||(t.WorkDoneProgressCancelNotification=a={}))},5965:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=o(n(1398));class a extends s.TypeHierarchyItem{data;constructor(e,t,n,i,r,o,s){super(e,t,n,i,r,o),void 0!==s&&(this.data=s)}}t.default=a},6149:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FileType=t.FsReadDirRequest=t.FsStatRequest=t.FsContentRequest=void 0,t.serveFileSystemRequests=function(e,t){e.onRequest(o.type,e=>{const n=i.Uri.parse(e.uri);return"file"===n.scheme&&t.fs?t.fs.getContent(e.uri):i.workspace.fs.readFile(n).then(n=>new t.TextDecoder(e.encoding).decode(n))}),e.onRequest(a.type,e=>{const n=i.Uri.parse(e);return"file"===n.scheme&&t.fs?t.fs.readDirectory(e):i.workspace.fs.readDirectory(n)}),e.onRequest(s.type,e=>{const n=i.Uri.parse(e);return"file"===n.scheme&&t.fs?t.fs.stat(e):i.workspace.fs.stat(n)})};const i=n(1398),r=n(2685);var o,s,a,c;!function(e){e.type=new r.RequestType("fs/content")}(o||(t.FsContentRequest=o={})),function(e){e.type=new r.RequestType("fs/stat")}(s||(t.FsStatRequest=s={})),function(e){e.type=new r.RequestType("fs/readDir")}(a||(t.FsReadDirRequest=a={})),function(e){e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=64]="SymbolicLink"}(c||(t.FileType=c={}))},6272:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.WorkspaceFoldersFeature=t.arrayDiff=void 0;const s=o(n(8820)),a=n(1398),c=n(3434);function u(e,t){if(null!=e)return e[t]}function l(e,t){return e.filter(e=>t.indexOf(e)<0)}t.arrayDiff=l,t.WorkspaceFoldersFeature=class{_client;_listeners;_initialFolders;constructor(e){this._client=e,this._listeners=new Map}getState(){return{kind:"workspace",id:this.registrationType.method,registrations:this._listeners.size>0}}get registrationType(){return c.DidChangeWorkspaceFoldersNotification.type}fillInitializeParams(e){const t=a.workspace.workspaceFolders;this.initializeWithFolders(t),e.workspaceFolders=void 0===t?null:t.map(e=>this.asProtocol(e))}initializeWithFolders(e){this._initialFolders=e}fillClientCapabilities(e){e.workspace=e.workspace||{},e.workspace.workspaceFolders=!0}initialize(e){const t=this._client;t.onRequest(c.WorkspaceFoldersRequest.type,e=>{const n=()=>{const e=a.workspace.workspaceFolders;return void 0===e?null:e.map(e=>this.asProtocol(e))},i=t.middleware.workspace;return i&&i.workspaceFolders?i.workspaceFolders(e,n):n()});const n=u(u(u(e,"workspace"),"workspaceFolders"),"changeNotifications");let i;"string"==typeof n?i=n:!0===n&&(i=s.generateUuid()),i&&this.register({id:i,registerOptions:void 0})}sendInitialEvent(e){let t;if(this._initialFolders&&e){const n=l(this._initialFolders,e),i=l(e,this._initialFolders);(i.length>0||n.length>0)&&(t=this.doSendEvent(i,n))}else this._initialFolders?t=this.doSendEvent([],this._initialFolders):e&&(t=this.doSendEvent(e,[]));void 0!==t&&t.catch(e=>{this._client.error(`Sending notification ${c.DidChangeWorkspaceFoldersNotification.type.method} failed`,e)})}doSendEvent(e,t){const n={event:{added:e.map(e=>this.asProtocol(e)),removed:t.map(e=>this.asProtocol(e))}};return this._client.sendNotification(c.DidChangeWorkspaceFoldersNotification.type,n)}register(e){const t=e.id,n=this._client,i=a.workspace.onDidChangeWorkspaceFolders(e=>{const t=e=>this.doSendEvent(e.added,e.removed),i=n.middleware.workspace;(i&&i.didChangeWorkspaceFolders?i.didChangeWorkspaceFolders(e,t):t(e)).catch(e=>{this._client.error(`Sending notification ${c.DidChangeWorkspaceFoldersNotification.type.method} failed`,e)})});this._listeners.set(t,i),this.sendInitialEvent(a.workspace.workspaceFolders)}unregister(e){const t=this._listeners.get(e);void 0!==t&&(this._listeners.delete(e),t.dispose())}clear(){for(const e of this._listeners.values())e.dispose();this._listeners.clear()}asProtocol(e){return void 0===e?null:{uri:this._client.code2ProtocolConverter.asUri(e.uri),name:e.name}}}},6284:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WillDeleteFilesRequest=t.DidDeleteFilesNotification=t.DidRenameFilesNotification=t.WillRenameFilesRequest=t.DidCreateFilesNotification=t.WillCreateFilesRequest=t.FileOperationPatternKind=void 0;const i=n(7096);var r,o,s,a,c,u,l;!function(e){e.file="file",e.folder="folder"}(r||(t.FileOperationPatternKind=r={})),function(e){e.method="workspace/willCreateFiles",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(o||(t.WillCreateFilesRequest=o={})),function(e){e.method="workspace/didCreateFiles",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolNotificationType(e.method)}(s||(t.DidCreateFilesNotification=s={})),function(e){e.method="workspace/willRenameFiles",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(a||(t.WillRenameFilesRequest=a={})),function(e){e.method="workspace/didRenameFiles",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolNotificationType(e.method)}(c||(t.DidRenameFilesNotification=c={})),function(e){e.method="workspace/didDeleteFiles",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolNotificationType(e.method)}(u||(t.DidDeleteFilesNotification=u={})),function(e){e.method="workspace/willDeleteFiles",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(l||(t.WillDeleteFilesRequest=l={}))},6297:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CallHierarchyOutgoingCallsRequest=t.CallHierarchyIncomingCallsRequest=t.CallHierarchyPrepareRequest=void 0;const i=n(7096);var r,o,s;!function(e){e.method="textDocument/prepareCallHierarchy",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.CallHierarchyPrepareRequest=r={})),function(e){e.method="callHierarchy/incomingCalls",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(o||(t.CallHierarchyIncomingCallsRequest=o={})),function(e){e.method="callHierarchy/outgoingCalls",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(s||(t.CallHierarchyOutgoingCallsRequest=s={}))},6357:(e,t)=>{"use strict";function n(e){return"string"==typeof e||e instanceof String}function i(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.stringArray=t.array=t.func=t.error=t.number=t.string=t.boolean=void 0,t.boolean=function(e){return!0===e||!1===e},t.string=n,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=function(e){return"function"==typeof e},t.array=i,t.stringArray=function(e){return i(e)&&e.every(e=>n(e))}},6576:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FileSystemWatcherFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);t.FileSystemWatcherFeature=class{_client;_notifyFileEvent;_watchers;constructor(e,t){this._client=e,this._notifyFileEvent=t,this._watchers=new Map}getState(){return{kind:"workspace",id:this.registrationType.method,registrations:this._watchers.size>0}}get registrationType(){return r.DidChangeWatchedFilesNotification.type}fillClientCapabilities(e){(0,o.ensure)((0,o.ensure)(e,"workspace"),"didChangeWatchedFiles").dynamicRegistration=!0,(0,o.ensure)((0,o.ensure)(e,"workspace"),"didChangeWatchedFiles").relativePatternSupport=!0}initialize(e,t){}register(e){if(!Array.isArray(e.registerOptions.watchers))return;const t=[];for(const n of e.registerOptions.watchers){const e=this._client.protocol2CodeConverter.asGlobPattern(n.globPattern);if(void 0===e)continue;let o=!0,s=!0,a=!0;void 0!==n.kind&&null!==n.kind&&(o=0!==(n.kind&r.WatchKind.Create),s=0!==(n.kind&r.WatchKind.Change),a=0!==(n.kind&r.WatchKind.Delete));const c=i.workspace.createFileSystemWatcher(e,!o,!s,!a);this.hookListeners(c,o,s,a,t),t.push(c)}this._watchers.set(e.id,t)}registerRaw(e,t){const n=[];for(const e of t)this.hookListeners(e,!0,!0,!0,n);this._watchers.set(e,n)}hookListeners(e,t,n,i,o){t&&e.onDidCreate(e=>this._notifyFileEvent({uri:this._client.code2ProtocolConverter.asUri(e),type:r.FileChangeType.Created}),null,o),n&&e.onDidChange(e=>this._notifyFileEvent({uri:this._client.code2ProtocolConverter.asUri(e),type:r.FileChangeType.Changed}),null,o),i&&e.onDidDelete(e=>this._notifyFileEvent({uri:this._client.code2ProtocolConverter.asUri(e),type:r.FileChangeType.Deleted}),null,o)}unregister(e){const t=this._watchers.get(e);if(t){this._watchers.delete(e);for(const e of t)e.dispose()}}clear(){this._watchers.forEach(e=>{for(const t of e)t.dispose()}),this._watchers.clear()}}},6712:function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Emitter=t.Event=void 0;const r=i(n(9042));var o;!function(e){const t={dispose(){}};e.None=function(){return t}}(o||(t.Event=o={}));class s{_callbacks;_contexts;add(e,t=null,n){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(n)&&n.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(!this._callbacks)return;let n=!1;for(let i=0,r=this._callbacks.length;i<r;i++)if(this._callbacks[i]===e){if(this._contexts[i]===t)return this._callbacks.splice(i,1),void this._contexts.splice(i,1);n=!0}if(n)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];const t=[],n=this._callbacks.slice(0),i=this._contexts.slice(0);for(let o=0,s=n.length;o<s;o++)try{t.push(n[o].apply(i[o],e))}catch(e){(0,r.default)().console.error(e)}return t}isEmpty(){return!this._callbacks||0===this._callbacks.length}dispose(){this._callbacks=void 0,this._contexts=void 0}}class a{_options;static _noop=function(){};_event;_callbacks;constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,n)=>{this._callbacks||(this._callbacks=new s),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);const i={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,t),i.dispose=a._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(n)&&n.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}t.Emitter=a},6811:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlayHintsFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);class s extends o.TextDocumentLanguageFeature{constructor(e){super(e,r.InlayHintRequest.type)}fillClientCapabilities(e){const t=(0,o.ensure)((0,o.ensure)(e,"textDocument"),"inlayHint");t.dynamicRegistration=!0,t.resolveSupport={properties:["tooltip","textEdits","label.tooltip","label.location","label.command"]},(0,o.ensure)((0,o.ensure)(e,"workspace"),"inlayHint").refreshSupport=!0}initialize(e,t){this._client.onRequest(r.InlayHintRefreshRequest.type,async()=>{for(const e of this.getAllProviders())e.onDidChangeInlayHints.fire()});const[n,i]=this.getRegistration(t,e.inlayHintProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=e.documentSelector,n=new i.EventEmitter,o={onDidChangeInlayHints:n.event,provideInlayHints:(e,t,n)=>{const i=this._client,o=async(e,t,n)=>{const o={textDocument:i.code2ProtocolConverter.asTextDocumentIdentifier(e),range:i.code2ProtocolConverter.asRange(t)};try{const e=await i.sendRequest(r.InlayHintRequest.type,o,n);return n.isCancellationRequested?null:i.protocol2CodeConverter.asInlayHints(e,n)}catch(e){return i.handleFailedRequest(r.InlayHintRequest.type,n,e,null)}},s=i.middleware;return s.provideInlayHints?s.provideInlayHints(e,t,n,o):o(e,t,n)}};return o.resolveInlayHint=!0===e.resolveProvider?(e,t)=>{const n=this._client,i=async(e,t)=>{try{const i=await n.sendRequest(r.InlayHintResolveRequest.type,n.code2ProtocolConverter.asInlayHint(e),t);if(t.isCancellationRequested)return null;const o=n.protocol2CodeConverter.asInlayHint(i,t);return t.isCancellationRequested?null:o}catch(e){return n.handleFailedRequest(r.InlayHintResolveRequest.type,t,e,null)}},o=n.middleware;return o.resolveInlayHint?o.resolveInlayHint(e,t,i):i(e,t)}:void 0,[this.registerProvider(t,o),{provider:o,onDidChangeInlayHints:n}]}registerProvider(e,t){return i.languages.registerInlayHintsProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}}t.InlayHintsFeature=s},6850:(e,t,n)=>{const{MAX_SAFE_COMPONENT_LENGTH:i,MAX_SAFE_BUILD_LENGTH:r,MAX_LENGTH:o}=n(9630),s=n(9796),a=(t=e.exports={}).re=[],c=t.safeRe=[],u=t.src=[],l=t.t={};let d=0;const h="[a-zA-Z0-9-]",p=[["\\s",1],["\\d",o],[h,r]],f=(e,t,n)=>{const i=(e=>{for(const[t,n]of p)e=e.split(`${t}*`).join(`${t}{0,${n}}`).split(`${t}+`).join(`${t}{1,${n}}`);return e})(t),r=d++;s(e,r,t),l[e]=r,u[r]=t,a[r]=new RegExp(t,n?"g":void 0),c[r]=new RegExp(i,n?"g":void 0)};f("NUMERICIDENTIFIER","0|[1-9]\\d*"),f("NUMERICIDENTIFIERLOOSE","\\d+"),f("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${h}*`),f("MAINVERSION",`(${u[l.NUMERICIDENTIFIER]})\\.(${u[l.NUMERICIDENTIFIER]})\\.(${u[l.NUMERICIDENTIFIER]})`),f("MAINVERSIONLOOSE",`(${u[l.NUMERICIDENTIFIERLOOSE]})\\.(${u[l.NUMERICIDENTIFIERLOOSE]})\\.(${u[l.NUMERICIDENTIFIERLOOSE]})`),f("PRERELEASEIDENTIFIER",`(?:${u[l.NUMERICIDENTIFIER]}|${u[l.NONNUMERICIDENTIFIER]})`),f("PRERELEASEIDENTIFIERLOOSE",`(?:${u[l.NUMERICIDENTIFIERLOOSE]}|${u[l.NONNUMERICIDENTIFIER]})`),f("PRERELEASE",`(?:-(${u[l.PRERELEASEIDENTIFIER]}(?:\\.${u[l.PRERELEASEIDENTIFIER]})*))`),f("PRERELEASELOOSE",`(?:-?(${u[l.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[l.PRERELEASEIDENTIFIERLOOSE]})*))`),f("BUILDIDENTIFIER",`${h}+`),f("BUILD",`(?:\\+(${u[l.BUILDIDENTIFIER]}(?:\\.${u[l.BUILDIDENTIFIER]})*))`),f("FULLPLAIN",`v?${u[l.MAINVERSION]}${u[l.PRERELEASE]}?${u[l.BUILD]}?`),f("FULL",`^${u[l.FULLPLAIN]}$`),f("LOOSEPLAIN",`[v=\\s]*${u[l.MAINVERSIONLOOSE]}${u[l.PRERELEASELOOSE]}?${u[l.BUILD]}?`),f("LOOSE",`^${u[l.LOOSEPLAIN]}$`),f("GTLT","((?:<|>)?=?)"),f("XRANGEIDENTIFIERLOOSE",`${u[l.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),f("XRANGEIDENTIFIER",`${u[l.NUMERICIDENTIFIER]}|x|X|\\*`),f("XRANGEPLAIN",`[v=\\s]*(${u[l.XRANGEIDENTIFIER]})(?:\\.(${u[l.XRANGEIDENTIFIER]})(?:\\.(${u[l.XRANGEIDENTIFIER]})(?:${u[l.PRERELEASE]})?${u[l.BUILD]}?)?)?`),f("XRANGEPLAINLOOSE",`[v=\\s]*(${u[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[l.XRANGEIDENTIFIERLOOSE]})(?:${u[l.PRERELEASELOOSE]})?${u[l.BUILD]}?)?)?`),f("XRANGE",`^${u[l.GTLT]}\\s*${u[l.XRANGEPLAIN]}$`),f("XRANGELOOSE",`^${u[l.GTLT]}\\s*${u[l.XRANGEPLAINLOOSE]}$`),f("COERCEPLAIN",`(^|[^\\d])(\\d{1,${i}})(?:\\.(\\d{1,${i}}))?(?:\\.(\\d{1,${i}}))?`),f("COERCE",`${u[l.COERCEPLAIN]}(?:$|[^\\d])`),f("COERCEFULL",u[l.COERCEPLAIN]+`(?:${u[l.PRERELEASE]})?`+`(?:${u[l.BUILD]})?(?:$|[^\\d])`),f("COERCERTL",u[l.COERCE],!0),f("COERCERTLFULL",u[l.COERCEFULL],!0),f("LONETILDE","(?:~>?)"),f("TILDETRIM",`(\\s*)${u[l.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",f("TILDE",`^${u[l.LONETILDE]}${u[l.XRANGEPLAIN]}$`),f("TILDELOOSE",`^${u[l.LONETILDE]}${u[l.XRANGEPLAINLOOSE]}$`),f("LONECARET","(?:\\^)"),f("CARETTRIM",`(\\s*)${u[l.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",f("CARET",`^${u[l.LONECARET]}${u[l.XRANGEPLAIN]}$`),f("CARETLOOSE",`^${u[l.LONECARET]}${u[l.XRANGEPLAINLOOSE]}$`),f("COMPARATORLOOSE",`^${u[l.GTLT]}\\s*(${u[l.LOOSEPLAIN]})$|^$`),f("COMPARATOR",`^${u[l.GTLT]}\\s*(${u[l.FULLPLAIN]})$|^$`),f("COMPARATORTRIM",`(\\s*)${u[l.GTLT]}\\s*(${u[l.LOOSEPLAIN]}|${u[l.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",f("HYPHENRANGE",`^\\s*(${u[l.XRANGEPLAIN]})\\s+-\\s+(${u[l.XRANGEPLAIN]})\\s*$`),f("HYPHENRANGELOOSE",`^\\s*(${u[l.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[l.XRANGEPLAINLOOSE]})\\s*$`),f("STAR","(<|>)?=?\\s*\\*"),f("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),f("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},6909:(e,t,n)=>{const i=n(5380);e.exports=(e,t,n)=>0===i(e,t,n)},6928:e=>{"use strict";e.exports=require("path")},6948:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.CodeActionRequest=t.DocumentSymbolRequest=t.DocumentHighlightRequest=t.ReferencesRequest=t.DefinitionRequest=t.SignatureHelpRequest=t.SignatureHelpTriggerKind=t.HoverRequest=t.CompletionResolveRequest=t.CompletionRequest=t.CompletionTriggerKind=t.PublishDiagnosticsNotification=t.WatchKind=t.GlobPattern=t.RelativePattern=t.FileChangeType=t.DidChangeWatchedFilesNotification=t.WillSaveTextDocumentWaitUntilRequest=t.WillSaveTextDocumentNotification=t.TextDocumentSaveReason=t.DidSaveTextDocumentNotification=t.DidCloseTextDocumentNotification=t.DidChangeTextDocumentNotification=t.TextDocumentContentChangeEvent=t.DidOpenTextDocumentNotification=t.TextDocumentSyncKind=t.TelemetryEventNotification=t.LogMessageNotification=t.ShowMessageRequest=t.ShowMessageNotification=t.MessageType=t.DidChangeConfigurationNotification=t.ExitNotification=t.ShutdownRequest=t.InitializedNotification=t.InitializeErrorCodes=t.InitializeRequest=t.WorkDoneProgressOptions=t.TextDocumentRegistrationOptions=t.StaticRegistrationOptions=t.PositionEncodingKind=t.RegularExpressionEngineKind=t.FailureHandlingKind=t.ResourceOperationKind=t.UnregistrationRequest=t.RegistrationRequest=t.DocumentSelector=t.NotebookCellTextDocumentFilter=t.NotebookDocumentFilter=t.TextDocumentFilter=void 0,t.UniquenessLevel=t.WillDeleteFilesRequest=t.DidDeleteFilesNotification=t.WillRenameFilesRequest=t.DidRenameFilesNotification=t.WillCreateFilesRequest=t.DidCreateFilesNotification=t.FileOperationPatternKind=t.LinkedEditingRangeRequest=t.ShowDocumentRequest=t.SemanticTokensRegistrationType=t.SemanticTokensRefreshRequest=t.SemanticTokensRangeRequest=t.SemanticTokensDeltaRequest=t.SemanticTokensRequest=t.TokenFormat=t.CallHierarchyPrepareRequest=t.CallHierarchyOutgoingCallsRequest=t.CallHierarchyIncomingCallsRequest=t.WorkDoneProgressCancelNotification=t.WorkDoneProgressCreateRequest=t.WorkDoneProgress=t.SelectionRangeRequest=t.DeclarationRequest=t.FoldingRangeRefreshRequest=t.FoldingRangeRequest=t.ColorPresentationRequest=t.DocumentColorRequest=t.ConfigurationRequest=t.DidChangeWorkspaceFoldersNotification=t.WorkspaceFoldersRequest=t.TypeDefinitionRequest=t.ImplementationRequest=t.ApplyWorkspaceEditRequest=t.ExecuteCommandRequest=t.PrepareRenameRequest=t.RenameRequest=t.PrepareSupportDefaultBehavior=t.DocumentOnTypeFormattingRequest=t.DocumentRangesFormattingRequest=t.DocumentRangeFormattingRequest=t.DocumentFormattingRequest=t.DocumentLinkResolveRequest=t.DocumentLinkRequest=t.CodeLensRefreshRequest=t.CodeLensResolveRequest=t.CodeLensRequest=t.WorkspaceSymbolResolveRequest=t.WorkspaceSymbolRequest=t.CodeActionResolveRequest=void 0,t.TextDocumentContentRefreshRequest=t.TextDocumentContentRequest=t.InlineCompletionRequest=t.DidCloseNotebookDocumentNotification=t.DidSaveNotebookDocumentNotification=t.DidChangeNotebookDocumentNotification=t.NotebookCellArrayChange=t.DidOpenNotebookDocumentNotification=t.NotebookDocumentSyncRegistrationType=t.NotebookDocument=t.NotebookCell=t.ExecutionSummary=t.NotebookCellKind=t.DiagnosticRefreshRequest=t.WorkspaceDiagnosticRequest=t.DocumentDiagnosticRequest=t.DocumentDiagnosticReportKind=t.DiagnosticServerCancellationData=t.InlayHintRefreshRequest=t.InlayHintResolveRequest=t.InlayHintRequest=t.InlineValueRefreshRequest=t.InlineValueRequest=t.TypeHierarchySupertypesRequest=t.TypeHierarchySubtypesRequest=t.TypeHierarchyPrepareRequest=t.MonikerRequest=t.MonikerKind=void 0;const s=n(7096),a=n(4437),c=o(n(7786)),u=n(2434);Object.defineProperty(t,"ImplementationRequest",{enumerable:!0,get:function(){return u.ImplementationRequest}});const l=n(1121);Object.defineProperty(t,"TypeDefinitionRequest",{enumerable:!0,get:function(){return l.TypeDefinitionRequest}});const d=n(3219);Object.defineProperty(t,"WorkspaceFoldersRequest",{enumerable:!0,get:function(){return d.WorkspaceFoldersRequest}}),Object.defineProperty(t,"DidChangeWorkspaceFoldersNotification",{enumerable:!0,get:function(){return d.DidChangeWorkspaceFoldersNotification}});const h=n(9488);Object.defineProperty(t,"ConfigurationRequest",{enumerable:!0,get:function(){return h.ConfigurationRequest}});const p=n(5908);Object.defineProperty(t,"DocumentColorRequest",{enumerable:!0,get:function(){return p.DocumentColorRequest}}),Object.defineProperty(t,"ColorPresentationRequest",{enumerable:!0,get:function(){return p.ColorPresentationRequest}});const f=n(8958);Object.defineProperty(t,"FoldingRangeRequest",{enumerable:!0,get:function(){return f.FoldingRangeRequest}}),Object.defineProperty(t,"FoldingRangeRefreshRequest",{enumerable:!0,get:function(){return f.FoldingRangeRefreshRequest}});const g=n(4486);Object.defineProperty(t,"DeclarationRequest",{enumerable:!0,get:function(){return g.DeclarationRequest}});const m=n(7267);Object.defineProperty(t,"SelectionRangeRequest",{enumerable:!0,get:function(){return m.SelectionRangeRequest}});const v=n(5923);Object.defineProperty(t,"WorkDoneProgress",{enumerable:!0,get:function(){return v.WorkDoneProgress}}),Object.defineProperty(t,"WorkDoneProgressCreateRequest",{enumerable:!0,get:function(){return v.WorkDoneProgressCreateRequest}}),Object.defineProperty(t,"WorkDoneProgressCancelNotification",{enumerable:!0,get:function(){return v.WorkDoneProgressCancelNotification}});const y=n(6297);Object.defineProperty(t,"CallHierarchyIncomingCallsRequest",{enumerable:!0,get:function(){return y.CallHierarchyIncomingCallsRequest}}),Object.defineProperty(t,"CallHierarchyOutgoingCallsRequest",{enumerable:!0,get:function(){return y.CallHierarchyOutgoingCallsRequest}}),Object.defineProperty(t,"CallHierarchyPrepareRequest",{enumerable:!0,get:function(){return y.CallHierarchyPrepareRequest}});const b=n(3562);Object.defineProperty(t,"TokenFormat",{enumerable:!0,get:function(){return b.TokenFormat}}),Object.defineProperty(t,"SemanticTokensRequest",{enumerable:!0,get:function(){return b.SemanticTokensRequest}}),Object.defineProperty(t,"SemanticTokensDeltaRequest",{enumerable:!0,get:function(){return b.SemanticTokensDeltaRequest}}),Object.defineProperty(t,"SemanticTokensRangeRequest",{enumerable:!0,get:function(){return b.SemanticTokensRangeRequest}}),Object.defineProperty(t,"SemanticTokensRefreshRequest",{enumerable:!0,get:function(){return b.SemanticTokensRefreshRequest}}),Object.defineProperty(t,"SemanticTokensRegistrationType",{enumerable:!0,get:function(){return b.SemanticTokensRegistrationType}});const _=n(9248);Object.defineProperty(t,"ShowDocumentRequest",{enumerable:!0,get:function(){return _.ShowDocumentRequest}});const C=n(9744);Object.defineProperty(t,"LinkedEditingRangeRequest",{enumerable:!0,get:function(){return C.LinkedEditingRangeRequest}});const D=n(6284);Object.defineProperty(t,"FileOperationPatternKind",{enumerable:!0,get:function(){return D.FileOperationPatternKind}}),Object.defineProperty(t,"DidCreateFilesNotification",{enumerable:!0,get:function(){return D.DidCreateFilesNotification}}),Object.defineProperty(t,"WillCreateFilesRequest",{enumerable:!0,get:function(){return D.WillCreateFilesRequest}}),Object.defineProperty(t,"DidRenameFilesNotification",{enumerable:!0,get:function(){return D.DidRenameFilesNotification}}),Object.defineProperty(t,"WillRenameFilesRequest",{enumerable:!0,get:function(){return D.WillRenameFilesRequest}}),Object.defineProperty(t,"DidDeleteFilesNotification",{enumerable:!0,get:function(){return D.DidDeleteFilesNotification}}),Object.defineProperty(t,"WillDeleteFilesRequest",{enumerable:!0,get:function(){return D.WillDeleteFilesRequest}});const S=n(2795);Object.defineProperty(t,"UniquenessLevel",{enumerable:!0,get:function(){return S.UniquenessLevel}}),Object.defineProperty(t,"MonikerKind",{enumerable:!0,get:function(){return S.MonikerKind}}),Object.defineProperty(t,"MonikerRequest",{enumerable:!0,get:function(){return S.MonikerRequest}});const w=n(7553);Object.defineProperty(t,"TypeHierarchyPrepareRequest",{enumerable:!0,get:function(){return w.TypeHierarchyPrepareRequest}}),Object.defineProperty(t,"TypeHierarchySubtypesRequest",{enumerable:!0,get:function(){return w.TypeHierarchySubtypesRequest}}),Object.defineProperty(t,"TypeHierarchySupertypesRequest",{enumerable:!0,get:function(){return w.TypeHierarchySupertypesRequest}});const R=n(744);Object.defineProperty(t,"InlineValueRequest",{enumerable:!0,get:function(){return R.InlineValueRequest}}),Object.defineProperty(t,"InlineValueRefreshRequest",{enumerable:!0,get:function(){return R.InlineValueRefreshRequest}});const T=n(4940);Object.defineProperty(t,"InlayHintRequest",{enumerable:!0,get:function(){return T.InlayHintRequest}}),Object.defineProperty(t,"InlayHintResolveRequest",{enumerable:!0,get:function(){return T.InlayHintResolveRequest}}),Object.defineProperty(t,"InlayHintRefreshRequest",{enumerable:!0,get:function(){return T.InlayHintRefreshRequest}});const P=n(4735);Object.defineProperty(t,"DiagnosticServerCancellationData",{enumerable:!0,get:function(){return P.DiagnosticServerCancellationData}}),Object.defineProperty(t,"DocumentDiagnosticReportKind",{enumerable:!0,get:function(){return P.DocumentDiagnosticReportKind}}),Object.defineProperty(t,"DocumentDiagnosticRequest",{enumerable:!0,get:function(){return P.DocumentDiagnosticRequest}}),Object.defineProperty(t,"WorkspaceDiagnosticRequest",{enumerable:!0,get:function(){return P.WorkspaceDiagnosticRequest}}),Object.defineProperty(t,"DiagnosticRefreshRequest",{enumerable:!0,get:function(){return P.DiagnosticRefreshRequest}});const O=n(3633);Object.defineProperty(t,"NotebookCellKind",{enumerable:!0,get:function(){return O.NotebookCellKind}}),Object.defineProperty(t,"ExecutionSummary",{enumerable:!0,get:function(){return O.ExecutionSummary}}),Object.defineProperty(t,"NotebookCell",{enumerable:!0,get:function(){return O.NotebookCell}}),Object.defineProperty(t,"NotebookDocument",{enumerable:!0,get:function(){return O.NotebookDocument}}),Object.defineProperty(t,"NotebookDocumentSyncRegistrationType",{enumerable:!0,get:function(){return O.NotebookDocumentSyncRegistrationType}}),Object.defineProperty(t,"DidOpenNotebookDocumentNotification",{enumerable:!0,get:function(){return O.DidOpenNotebookDocumentNotification}}),Object.defineProperty(t,"NotebookCellArrayChange",{enumerable:!0,get:function(){return O.NotebookCellArrayChange}}),Object.defineProperty(t,"DidChangeNotebookDocumentNotification",{enumerable:!0,get:function(){return O.DidChangeNotebookDocumentNotification}}),Object.defineProperty(t,"DidSaveNotebookDocumentNotification",{enumerable:!0,get:function(){return O.DidSaveNotebookDocumentNotification}}),Object.defineProperty(t,"DidCloseNotebookDocumentNotification",{enumerable:!0,get:function(){return O.DidCloseNotebookDocumentNotification}});const k=n(8575);Object.defineProperty(t,"InlineCompletionRequest",{enumerable:!0,get:function(){return k.InlineCompletionRequest}});const x=n(4341);var E,M,F,q,I,N,j,L,A,$,H,W,U,K,z,B,V,G,X,J,Q,Y,Z,ee,te,ne,ie,re,oe,se,ae,ce,ue,le,de,he,pe,fe,ge,me,ve,ye,be,_e,Ce,De,Se,we,Re,Te,Pe,Oe,ke,xe,Ee,Me,Fe,qe,Ie,Ne,je,Le,Ae,$e,He,We,Ue;Object.defineProperty(t,"TextDocumentContentRequest",{enumerable:!0,get:function(){return x.TextDocumentContentRequest}}),Object.defineProperty(t,"TextDocumentContentRefreshRequest",{enumerable:!0,get:function(){return x.TextDocumentContentRefreshRequest}}),function(e){e.is=function(e){const t=e;return c.string(t)||c.string(t.language)||c.string(t.scheme)||pe.is(t.pattern)}}(E||(t.TextDocumentFilter=E={})),function(e){e.is=function(e){const t=e;return c.objectLiteral(t)&&(c.string(t.notebookType)||c.string(t.scheme)||c.string(t.pattern))}}(M||(t.NotebookDocumentFilter=M={})),function(e){e.is=function(e){const t=e;return c.objectLiteral(t)&&(c.string(t.notebook)||M.is(t.notebook))&&(void 0===t.language||c.string(t.language))}}(F||(t.NotebookCellTextDocumentFilter=F={})),function(e){e.is=function(e){if(!Array.isArray(e))return!1;for(const t of e)if(!c.string(t)&&!E.is(t)&&!F.is(t))return!1;return!0}}(q||(t.DocumentSelector=q={})),function(e){e.method="client/registerCapability",e.messageDirection=s.MessageDirection.serverToClient,e.type=new s.ProtocolRequestType(e.method)}(I||(t.RegistrationRequest=I={})),function(e){e.method="client/unregisterCapability",e.messageDirection=s.MessageDirection.serverToClient,e.type=new s.ProtocolRequestType(e.method)}(N||(t.UnregistrationRequest=N={})),function(e){e.Create="create",e.Rename="rename",e.Delete="delete"}(j||(t.ResourceOperationKind=j={})),function(e){e.Abort="abort",e.Transactional="transactional",e.TextOnlyTransactional="textOnlyTransactional",e.Undo="undo"}(L||(t.FailureHandlingKind=L={})),function(e){e.ES2020="ES2020"}(A||(t.RegularExpressionEngineKind=A={})),function(e){e.UTF8="utf-8",e.UTF16="utf-16",e.UTF32="utf-32"}($||(t.PositionEncodingKind=$={})),function(e){e.hasId=function(e){const t=e;return t&&c.string(t.id)&&t.id.length>0}}(H||(t.StaticRegistrationOptions=H={})),function(e){e.is=function(e){const t=e;return t&&(null===t.documentSelector||q.is(t.documentSelector))}}(W||(t.TextDocumentRegistrationOptions=W={})),function(e){e.is=function(e){const t=e;return c.objectLiteral(t)&&(void 0===t.workDoneProgress||c.boolean(t.workDoneProgress))},e.hasWorkDoneProgress=function(e){const t=e;return t&&c.boolean(t.workDoneProgress)}}(U||(t.WorkDoneProgressOptions=U={})),function(e){e.method="initialize",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(K||(t.InitializeRequest=K={})),function(e){e.unknownProtocolVersion=1}(z||(t.InitializeErrorCodes=z={})),function(e){e.method="initialized",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolNotificationType(e.method)}(B||(t.InitializedNotification=B={})),function(e){e.method="shutdown",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType0(e.method)}(V||(t.ShutdownRequest=V={})),function(e){e.method="exit",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolNotificationType0(e.method)}(G||(t.ExitNotification=G={})),function(e){e.method="workspace/didChangeConfiguration",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolNotificationType(e.method)}(X||(t.DidChangeConfigurationNotification=X={})),function(e){e.Error=1,e.Warning=2,e.Info=3,e.Log=4,e.Debug=5}(J||(t.MessageType=J={})),function(e){e.method="window/showMessage",e.messageDirection=s.MessageDirection.serverToClient,e.type=new s.ProtocolNotificationType(e.method)}(Q||(t.ShowMessageNotification=Q={})),function(e){e.method="window/showMessageRequest",e.messageDirection=s.MessageDirection.serverToClient,e.type=new s.ProtocolRequestType(e.method)}(Y||(t.ShowMessageRequest=Y={})),function(e){e.method="window/logMessage",e.messageDirection=s.MessageDirection.serverToClient,e.type=new s.ProtocolNotificationType(e.method)}(Z||(t.LogMessageNotification=Z={})),function(e){e.method="telemetry/event",e.messageDirection=s.MessageDirection.serverToClient,e.type=new s.ProtocolNotificationType(e.method)}(ee||(t.TelemetryEventNotification=ee={})),function(e){e.None=0,e.Full=1,e.Incremental=2}(te||(t.TextDocumentSyncKind=te={})),function(e){e.method="textDocument/didOpen",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolNotificationType(e.method)}(ne||(t.DidOpenTextDocumentNotification=ne={})),function(e){e.isIncremental=function(e){const t=e;return null!=t&&"string"==typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"==typeof t.rangeLength)},e.isFull=function(e){const t=e;return null!=t&&"string"==typeof t.text&&void 0===t.range&&void 0===t.rangeLength}}(ie||(t.TextDocumentContentChangeEvent=ie={})),function(e){e.method="textDocument/didChange",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolNotificationType(e.method)}(re||(t.DidChangeTextDocumentNotification=re={})),function(e){e.method="textDocument/didClose",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolNotificationType(e.method)}(oe||(t.DidCloseTextDocumentNotification=oe={})),function(e){e.method="textDocument/didSave",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolNotificationType(e.method)}(se||(t.DidSaveTextDocumentNotification=se={})),function(e){e.Manual=1,e.AfterDelay=2,e.FocusOut=3}(ae||(t.TextDocumentSaveReason=ae={})),function(e){e.method="textDocument/willSave",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolNotificationType(e.method)}(ce||(t.WillSaveTextDocumentNotification=ce={})),function(e){e.method="textDocument/willSaveWaitUntil",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(ue||(t.WillSaveTextDocumentWaitUntilRequest=ue={})),function(e){e.method="workspace/didChangeWatchedFiles",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolNotificationType(e.method)}(le||(t.DidChangeWatchedFilesNotification=le={})),function(e){e.Created=1,e.Changed=2,e.Deleted=3}(de||(t.FileChangeType=de={})),function(e){e.is=function(e){const t=e;return c.objectLiteral(t)&&(a.URI.is(t.baseUri)||a.WorkspaceFolder.is(t.baseUri))&&c.string(t.pattern)}}(he||(t.RelativePattern=he={})),function(e){e.is=function(e){const t=e;return c.string(t)||he.is(t)}}(pe||(t.GlobPattern=pe={})),function(e){e.Create=1,e.Change=2,e.Delete=4}(fe||(t.WatchKind=fe={})),function(e){e.method="textDocument/publishDiagnostics",e.messageDirection=s.MessageDirection.serverToClient,e.type=new s.ProtocolNotificationType(e.method)}(ge||(t.PublishDiagnosticsNotification=ge={})),function(e){e.Invoked=1,e.TriggerCharacter=2,e.TriggerForIncompleteCompletions=3}(me||(t.CompletionTriggerKind=me={})),function(e){e.method="textDocument/completion",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(ve||(t.CompletionRequest=ve={})),function(e){e.method="completionItem/resolve",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(ye||(t.CompletionResolveRequest=ye={})),function(e){e.method="textDocument/hover",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(be||(t.HoverRequest=be={})),function(e){e.Invoked=1,e.TriggerCharacter=2,e.ContentChange=3}(_e||(t.SignatureHelpTriggerKind=_e={})),function(e){e.method="textDocument/signatureHelp",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Ce||(t.SignatureHelpRequest=Ce={})),function(e){e.method="textDocument/definition",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(De||(t.DefinitionRequest=De={})),function(e){e.method="textDocument/references",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Se||(t.ReferencesRequest=Se={})),function(e){e.method="textDocument/documentHighlight",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(we||(t.DocumentHighlightRequest=we={})),function(e){e.method="textDocument/documentSymbol",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Re||(t.DocumentSymbolRequest=Re={})),function(e){e.method="textDocument/codeAction",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Te||(t.CodeActionRequest=Te={})),function(e){e.method="codeAction/resolve",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Pe||(t.CodeActionResolveRequest=Pe={})),function(e){e.method="workspace/symbol",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Oe||(t.WorkspaceSymbolRequest=Oe={})),function(e){e.method="workspaceSymbol/resolve",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(ke||(t.WorkspaceSymbolResolveRequest=ke={})),function(e){e.method="textDocument/codeLens",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(xe||(t.CodeLensRequest=xe={})),function(e){e.method="codeLens/resolve",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Ee||(t.CodeLensResolveRequest=Ee={})),function(e){e.method="workspace/codeLens/refresh",e.messageDirection=s.MessageDirection.serverToClient,e.type=new s.ProtocolRequestType0(e.method)}(Me||(t.CodeLensRefreshRequest=Me={})),function(e){e.method="textDocument/documentLink",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Fe||(t.DocumentLinkRequest=Fe={})),function(e){e.method="documentLink/resolve",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(qe||(t.DocumentLinkResolveRequest=qe={})),function(e){e.method="textDocument/formatting",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Ie||(t.DocumentFormattingRequest=Ie={})),function(e){e.method="textDocument/rangeFormatting",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Ne||(t.DocumentRangeFormattingRequest=Ne={})),function(e){e.method="textDocument/rangesFormatting",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(je||(t.DocumentRangesFormattingRequest=je={})),function(e){e.method="textDocument/onTypeFormatting",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(Le||(t.DocumentOnTypeFormattingRequest=Le={})),function(e){e.Identifier=1}(Ae||(t.PrepareSupportDefaultBehavior=Ae={})),function(e){e.method="textDocument/rename",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}($e||(t.RenameRequest=$e={})),function(e){e.method="textDocument/prepareRename",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(He||(t.PrepareRenameRequest=He={})),function(e){e.method="workspace/executeCommand",e.messageDirection=s.MessageDirection.clientToServer,e.type=new s.ProtocolRequestType(e.method)}(We||(t.ExecuteCommandRequest=We={})),function(e){e.method="workspace/applyEdit",e.messageDirection=s.MessageDirection.serverToClient,e.type=new s.ProtocolRequestType("workspace/applyEdit")}(Ue||(t.ApplyWorkspaceEditRequest=Ue={}))},6982:e=>{"use strict";e.exports=require("crypto")},7034:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DiagnosticFeature=t.DiagnosticPullMode=t.vsdiag=void 0;const i=n(1398),r=n(3434),o=n(8820),s=n(3563),a=n(9810);function c(e,t){return void 0===e[t]&&(e[t]={}),e[t]}var u,l,d,h,p;!function(e){let t;!function(e){e.full="full",e.unChanged="unChanged"}(t=e.DocumentDiagnosticReportKind||(e.DocumentDiagnosticReportKind={}))}(u||(t.vsdiag=u={})),function(e){e.onType="onType",e.onSave="onSave",e.onFocus="onFocus"}(l||(t.DiagnosticPullMode=l={})),function(e){e.active="open",e.reschedule="reschedule",e.outDated="drop"}(d||(d={})),function(e){e[e.document=1]="document",e[e.workspace=2]="workspace"}(h||(h={})),function(e){e.asKey=function(e){return e instanceof i.Uri?e.toString():e.uri.toString()}}(p||(p={}));class f{documentPullStates;workspacePullStates;constructor(){this.documentPullStates=new Map,this.workspacePullStates=new Map}track(e,t,n){const r=e===h.document?this.documentPullStates:this.workspacePullStates,[o,s,a]=t instanceof i.Uri?[t.toString(),t,n]:[t.uri.toString(),t.uri,t.version];let c=r.get(o);return void 0===c&&(c={document:s,pulledVersion:a,resultId:void 0},r.set(o,c)),c}update(e,t,n,r){const o=e===h.document?this.documentPullStates:this.workspacePullStates,[s,a,c,u]=t instanceof i.Uri?[t.toString(),t,n,r]:[t.uri.toString(),t.uri,t.version,n];let l=o.get(s);void 0===l?(l={document:a,pulledVersion:c,resultId:u},o.set(s,l)):(l.pulledVersion=c,l.resultId=u)}unTrack(e,t){const n=p.asKey(t);(e===h.document?this.documentPullStates:this.workspacePullStates).delete(n)}tracks(e,t){const n=p.asKey(t);return(e===h.document?this.documentPullStates:this.workspacePullStates).has(n)}getResultId(e,t){const n=p.asKey(t),i=e===h.document?this.documentPullStates:this.workspacePullStates;return i.get(n)?.resultId}getAllResultIds(){const e=[];for(let[t,n]of this.workspacePullStates)this.documentPullStates.has(t)&&(n=this.documentPullStates.get(t)),void 0!==n.resultId&&e.push({uri:t,value:n.resultId});return e}}class g{isDisposed;client;tabs;options;onDidChangeDiagnosticsEmitter;provider;diagnostics;openRequests;documentStates;workspaceErrorCounter;workspaceCancellation;workspaceTimeout;constructor(e,t,n){this.client=e,this.tabs=t,this.options=n,this.isDisposed=!1,this.onDidChangeDiagnosticsEmitter=new i.EventEmitter,this.provider=this.createProvider(),this.diagnostics=i.languages.createDiagnosticCollection(n.identifier),this.openRequests=new Map,this.documentStates=new f,this.workspaceErrorCounter=0}knows(e,t){const n=t instanceof i.Uri?t:t.uri;return this.documentStates.tracks(e,t)||this.openRequests.has(n.toString())}forget(e,t){this.documentStates.unTrack(e,t)}pull(e,t){if(this.isDisposed)return;const n=e instanceof i.Uri?e:e.uri;this.pullAsync(e).then(()=>{t&&t()},e=>{this.client.error(`Document pull failed for text document ${n.toString()}`,e,!1)})}async pullAsync(e,t){if(this.isDisposed)return;const n=e instanceof i.Uri,o=n?e:e.uri,s=o.toString();t=n?t:e.version;const c=this.openRequests.get(s),l=n?this.documentStates.track(h.document,e,t):this.documentStates.track(h.document,e);if(void 0===c){const n=new i.CancellationTokenSource;let c,p;this.openRequests.set(s,{state:d.active,document:e,version:t,tokenSource:n});try{c=await this.provider.provideDiagnostics(e,l.resultId,n.token)??{kind:u.DocumentDiagnosticReportKind.full,items:[]}}catch(t){if(t instanceof a.LSPCancellationError&&r.DiagnosticServerCancellationData.is(t.data)&&!1===t.data.retriggerRequest&&(p={state:d.outDated,document:e}),!(void 0===p&&t instanceof i.CancellationError))throw t;p={state:d.reschedule,document:e}}if(p=p??this.openRequests.get(s),void 0===p)return this.client.error(`Lost request state in diagnostic pull model. Clearing diagnostics for ${s}`),void this.diagnostics.delete(o);if(this.openRequests.delete(s),!this.tabs.isVisible(e))return void this.documentStates.unTrack(h.document,e);if(p.state===d.outDated)return;void 0!==c&&(c.kind===u.DocumentDiagnosticReportKind.full&&this.diagnostics.set(o,c.items),l.pulledVersion=t,l.resultId=c.resultId),p.state===d.reschedule&&this.pull(e)}else c.state===d.active?(c.tokenSource.cancel(),this.openRequests.set(s,{state:d.reschedule,document:c.document})):c.state===d.outDated&&this.openRequests.set(s,{state:d.reschedule,document:c.document})}forgetDocument(e){const t=e instanceof i.Uri?e:e.uri,n=t.toString(),r=this.openRequests.get(n);this.options.workspaceDiagnostics?void 0!==r?this.openRequests.set(n,{state:d.reschedule,document:e}):this.pull(e,()=>{this.forget(h.document,e)}):(void 0!==r&&(r.state===d.active&&r.tokenSource.cancel(),this.openRequests.set(n,{state:d.outDated,document:e})),this.diagnostics.delete(t),this.forget(h.document,e))}pullWorkspace(){this.isDisposed||this.pullWorkspaceAsync().then(()=>{this.workspaceTimeout=(0,r.RAL)().timer.setTimeout(()=>{this.pullWorkspace()},2e3)},e=>{e instanceof a.LSPCancellationError||r.DiagnosticServerCancellationData.is(e.data)||(this.client.error("Workspace diagnostic pull failed.",e,!1),this.workspaceErrorCounter++),this.workspaceErrorCounter<=5&&(this.workspaceTimeout=(0,r.RAL)().timer.setTimeout(()=>{this.pullWorkspace()},2e3))})}async pullWorkspaceAsync(){if(!this.provider.provideWorkspaceDiagnostics||this.isDisposed)return;void 0!==this.workspaceCancellation&&(this.workspaceCancellation.cancel(),this.workspaceCancellation=void 0),this.workspaceCancellation=new i.CancellationTokenSource;const e=this.documentStates.getAllResultIds().map(e=>({uri:this.client.protocol2CodeConverter.asUri(e.uri),value:e.value}));await this.provider.provideWorkspaceDiagnostics(e,this.workspaceCancellation.token,e=>{if(e&&!this.isDisposed)for(const t of e.items)t.kind===u.DocumentDiagnosticReportKind.full&&(this.documentStates.tracks(h.document,t.uri)||this.diagnostics.set(t.uri,t.items)),this.documentStates.update(h.workspace,t.uri,t.version??void 0,t.resultId)})}createProvider(){const e={onDidChangeDiagnostics:this.onDidChangeDiagnosticsEmitter.event,provideDiagnostics:(e,t,n)=>{const o=(e,t,n)=>{const o={identifier:this.options.identifier,textDocument:{uri:this.client.code2ProtocolConverter.asUri(e instanceof i.Uri?e:e.uri)},previousResultId:t};return!0!==this.isDisposed&&this.client.isRunning()?this.client.sendRequest(r.DocumentDiagnosticRequest.type,o,n).then(async e=>null==e||this.isDisposed||n.isCancellationRequested?{kind:u.DocumentDiagnosticReportKind.full,items:[]}:e.kind===r.DocumentDiagnosticReportKind.Full?{kind:u.DocumentDiagnosticReportKind.full,resultId:e.resultId,items:await this.client.protocol2CodeConverter.asDiagnostics(e.items,n)}:{kind:u.DocumentDiagnosticReportKind.unChanged,resultId:e.resultId},e=>this.client.handleFailedRequest(r.DocumentDiagnosticRequest.type,n,e,{kind:u.DocumentDiagnosticReportKind.full,items:[]},!0,!0)):{kind:u.DocumentDiagnosticReportKind.full,items:[]}},s=this.client.middleware;return s.provideDiagnostics?s.provideDiagnostics(e,t,n,o):o(e,t,n)}};return this.options.workspaceDiagnostics&&(e.provideWorkspaceDiagnostics=(e,t,n)=>{const i=async e=>e.kind===r.DocumentDiagnosticReportKind.Full?{kind:u.DocumentDiagnosticReportKind.full,uri:this.client.protocol2CodeConverter.asUri(e.uri),resultId:e.resultId,version:e.version,items:await this.client.protocol2CodeConverter.asDiagnostics(e.items,t)}:{kind:u.DocumentDiagnosticReportKind.unChanged,uri:this.client.protocol2CodeConverter.asUri(e.uri),resultId:e.resultId,version:e.version},s=e=>{const t=[];for(const n of e)t.push({uri:this.client.code2ProtocolConverter.asUri(n.uri),value:n.value});return t},a=(e,t)=>{const a=(0,o.generateUuid)(),c=this.client.onProgress(r.WorkspaceDiagnosticRequest.partialResult,a,async e=>{if(null==e)return void n(null);const t={items:[]};for(const n of e.items)try{t.items.push(await i(n))}catch(e){this.client.error("Converting workspace diagnostics failed.",e)}n(t)}),u={identifier:this.options.identifier,previousResultIds:s(e),partialResultToken:a};return!0!==this.isDisposed&&this.client.isRunning()?this.client.sendRequest(r.WorkspaceDiagnosticRequest.type,u,t).then(async e=>{if(t.isCancellationRequested)return{items:[]};const r={items:[]};for(const t of e.items)r.items.push(await i(t));return c.dispose(),n(r),{items:[]}},e=>(c.dispose(),this.client.handleFailedRequest(r.DocumentDiagnosticRequest.type,t,e,{items:[]}))):{items:[]}},c=this.client.middleware;return c.provideWorkspaceDiagnostics?c.provideWorkspaceDiagnostics(e,t,n,a):a(e,t)}),e}dispose(){this.isDisposed=!0,this.workspaceCancellation?.cancel(),this.workspaceTimeout?.dispose();for(const[e,t]of this.openRequests)t.state===d.active&&t.tokenSource.cancel(),this.openRequests.set(e,{state:d.outDated,document:t.document});this.diagnostics.dispose()}}class m{client;diagnosticRequestor;lastDocumentToPull;documents;timeoutHandle;isDisposed;constructor(e,t){this.client=e,this.diagnosticRequestor=t,this.documents=new r.LinkedMap,this.isDisposed=!1}add(e){if(!0===this.isDisposed)return;const t=p.asKey(e);this.documents.has(t)||(this.documents.set(t,e,r.Touch.Last),this.lastDocumentToPull=e)}remove(e){const t=p.asKey(e);if(this.documents.delete(t),0!==this.documents.size){if(t===this.lastDocumentToPullKey()){const e=this.documents.before(t);void 0===e?this.stop():this.lastDocumentToPull=e}}else this.stop()}trigger(){this.lastDocumentToPull=this.documents.last,this.runLoop()}runLoop(){!0!==this.isDisposed&&(0!==this.documents.size?void 0!==this.lastDocumentToPull&&void 0===this.timeoutHandle&&(this.timeoutHandle=(0,r.RAL)().timer.setTimeout(()=>{const e=this.documents.first;if(void 0===e)return;const t=p.asKey(e);this.diagnosticRequestor.pullAsync(e).catch(e=>{this.client.error(`Document pull failed for text document ${t}`,e,!1)}).finally(()=>{this.timeoutHandle=void 0,this.documents.set(t,e,r.Touch.Last),t!==this.lastDocumentToPullKey()&&this.runLoop()})},500)):this.stop())}dispose(){this.isDisposed=!0,this.stop(),this.documents.clear(),this.lastDocumentToPull=void 0}stop(){this.timeoutHandle?.dispose(),this.timeoutHandle=void 0,this.lastDocumentToPull=void 0}lastDocumentToPullKey(){return void 0!==this.lastDocumentToPull?p.asKey(this.lastDocumentToPull):void 0}}class v{disposable;diagnosticRequestor;activeTextDocument;backgroundScheduler;constructor(e,t,n){const o=Object.assign({onChange:!1,onSave:!1,onFocus:!1},e.clientOptions.diagnosticPullOptions),a=e.protocol2CodeConverter.asDocumentSelector(n.documentSelector),c=[],u=(e,t)=>!("string"==typeof e||void 0!==e.language&&"*"!==e.language||void 0!==e.scheme&&"*"!==e.scheme&&e.scheme!==t.scheme||void 0!==e.pattern&&!(0,s.matchGlobPattern)(e.pattern,t)),d=e=>e instanceof i.Uri?(e=>{const t=n.documentSelector;if(void 0!==o.match)return o.match(t,e);for(const n of t)if(r.TextDocumentFilter.is(n)&&u(n,e))return!0;return!1})(e):i.languages.match(a,e)>0&&t.isVisible(e),p=e=>i.languages.match(a,e.document)>0&&t.isVisible(e.notebook.uri),f=e=>e instanceof i.Uri?this.activeTextDocument?.uri.toString()===e.toString():this.activeTextDocument===e;this.diagnosticRequestor=new g(e,t,n),this.backgroundScheduler=new m(e,this.diagnosticRequestor);const v=e=>{d(e)&&n.interFileDependencies&&!f(e)&&!1!==o.onChange&&this.backgroundScheduler.add(e)},y=(e,t)=>(void 0===o.filter||!o.filter(e,t))&&this.diagnosticRequestor.knows(h.document,e);this.activeTextDocument=i.window.activeTextEditor?.document,c.push(i.window.onDidChangeActiveTextEditor(e=>{const t=this.activeTextDocument;this.activeTextDocument=e?.document,void 0!==t&&v(t),void 0!==this.activeTextDocument&&(this.backgroundScheduler.remove(this.activeTextDocument),!0===o.onFocus&&d(this.activeTextDocument)&&y(this.activeTextDocument,l.onFocus)&&this.diagnosticRequestor.pull(this.activeTextDocument))}));const b=e.getFeature(r.DidOpenTextDocumentNotification.method);c.push(b.onNotificationSent(e=>{const t=e.textDocument;this.diagnosticRequestor.knows(h.document,t)||d(t)&&this.diagnosticRequestor.pull(t,()=>{v(t)})}));const _=e.getFeature(r.NotebookDocumentSyncRegistrationType.method);c.push(_.onOpenNotificationSent(e=>{for(const t of e.getCells())p(t)&&this.diagnosticRequestor.pull(t.document,()=>{v(t.document)})})),c.push(t.onOpen(e=>{for(const t of e){if(this.diagnosticRequestor.knows(h.document,t))continue;const e=t.toString();let n;for(const t of i.workspace.textDocuments)if(e===t.uri.toString()){n=t;break}void 0!==n&&d(n)&&this.diagnosticRequestor.pull(n,()=>{v(n)})}}));const C=new Set;for(const e of i.workspace.textDocuments)d(e)&&(this.diagnosticRequestor.pull(e,()=>{v(e)}),C.add(e.uri.toString()));for(const e of i.workspace.notebookDocuments)for(const t of e.getCells())p(t)&&(this.diagnosticRequestor.pull(t.document,()=>{v(t.document)}),C.add(t.document.uri.toString()));if(!0===o.onTabs)for(const e of t.getTabResources())!C.has(e.toString())&&d(e)&&this.diagnosticRequestor.pull(e,()=>{v(e)});if(!0===o.onChange){const t=e.getFeature(r.DidChangeTextDocumentNotification.method);c.push(t.onNotificationSent(async e=>{const t=e.textDocument;y(t,l.onType)&&this.diagnosticRequestor.pull(t,()=>{this.backgroundScheduler.trigger()})})),c.push(_.onChangeNotificationSent(async e=>{const t=(e.cells?.textContent||[]).map(t=>e.notebook.getCells().find(e=>e.document.uri.toString()===t.document.uri.toString()));for(const e of t)e&&p(e)&&this.diagnosticRequestor.pull(e.document,()=>{this.backgroundScheduler.trigger()});const n=e.cells?.structure?.didClose||[];for(const e of n)this.diagnosticRequestor.forgetDocument(e.document);const i=e.cells?.structure?.didOpen||[];for(const e of i)p(e)&&this.diagnosticRequestor.pull(e.document,()=>{this.backgroundScheduler.trigger()})}))}if(!0===o.onSave){const t=e.getFeature(r.DidSaveTextDocumentNotification.method);c.push(t.onNotificationSent(e=>{const t=e.textDocument;y(t,l.onSave)&&this.diagnosticRequestor.pull(e.textDocument)})),c.push(_.onSaveNotificationSent(e=>{for(const t of e.getCells())p(t)&&this.diagnosticRequestor.pull(t.document)}))}const D=e.getFeature(r.DidCloseTextDocumentNotification.method);c.push(D.onNotificationSent(e=>{this.cleanUpDocument(e.textDocument)})),c.push(_.onCloseNotificationSent(e=>{for(const t of e.getCells())this.cleanUpDocument(t.document)})),t.onClose(e=>{for(const t of e)this.cleanUpDocument(t)}),this.diagnosticRequestor.onDidChangeDiagnosticsEmitter.event(()=>{for(const e of i.workspace.textDocuments)d(e)&&this.diagnosticRequestor.pull(e)}),!0===n.workspaceDiagnostics&&"da348dc5-c30a-4515-9d98-31ff3be38d14"!==n.identifier&&this.diagnosticRequestor.pullWorkspace(),this.disposable=i.Disposable.from(...c,this.backgroundScheduler,this.diagnosticRequestor)}get onDidChangeDiagnosticsEmitter(){return this.diagnosticRequestor.onDidChangeDiagnosticsEmitter}get diagnostics(){return this.diagnosticRequestor.provider}forget(e){this.cleanUpDocument(e)}cleanUpDocument(e){this.backgroundScheduler.remove(e),this.diagnosticRequestor.knows(h.document,e)&&this.diagnosticRequestor.forgetDocument(e)}}class y extends a.TextDocumentLanguageFeature{constructor(e){super(e,r.DocumentDiagnosticRequest.type)}fillClientCapabilities(e){const t=c(c(e,"textDocument"),"diagnostic");t.relatedInformation=!0,t.tagSupport={valueSet:[r.DiagnosticTag.Unnecessary,r.DiagnosticTag.Deprecated]},t.codeDescriptionSupport=!0,t.dataSupport=!0,t.dynamicRegistration=!0,t.relatedDocumentSupport=!1,c(c(e,"workspace"),"diagnostics").refreshSupport=!0}initialize(e,t){this._client.onRequest(r.DiagnosticRefreshRequest.type,async()=>{for(const e of this.getAllProviders())e.onDidChangeDiagnosticsEmitter.fire()});const[n,i]=this.getRegistration(t,e.diagnosticProvider);n&&i&&this.register({id:n,registerOptions:i})}clear(){super.clear()}refresh(){for(const e of this.getAllProviders())e.onDidChangeDiagnosticsEmitter.fire()}registerLanguageProvider(e){const t=new v(this._client,this._client.tabsModel,e);return[t.disposable,t]}}t.DiagnosticFeature=y},7086:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SelectionRangeFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);class s extends o.TextDocumentLanguageFeature{constructor(e){super(e,r.SelectionRangeRequest.type)}fillClientCapabilities(e){(0,o.ensure)((0,o.ensure)(e,"textDocument"),"selectionRange").dynamicRegistration=!0}initialize(e,t){const[n,i]=this.getRegistration(t,e.selectionRangeProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=e.documentSelector,n={provideSelectionRanges:(e,t,n)=>{const i=this._client,o=async(e,t,n)=>{const o={textDocument:i.code2ProtocolConverter.asTextDocumentIdentifier(e),positions:i.code2ProtocolConverter.asPositionsSync(t,n)};return i.sendRequest(r.SelectionRangeRequest.type,o,n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asSelectionRanges(e,n),e=>i.handleFailedRequest(r.SelectionRangeRequest.type,n,e,null))},s=i.middleware;return s.provideSelectionRanges?s.provideSelectionRanges(e,t,n,o):o(e,t,n)}};return[this.registerProvider(t,n),n]}registerProvider(e,t){return i.languages.registerSelectionRangeProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}}t.SelectionRangeFeature=s},7092:(e,t,n)=>{const i=n(5380);e.exports=(e,t,n)=>i(e,t,n)<=0},7095:e=>{const t=Object.freeze({loose:!0}),n=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:n},7096:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ProtocolNotificationType=t.ProtocolNotificationType0=t.ProtocolRequestType=t.ProtocolRequestType0=t.RegistrationType=t.MessageDirection=void 0;const i=n(9765);var r;!function(e){e.clientToServer="clientToServer",e.serverToClient="serverToClient",e.both="both"}(r||(t.MessageDirection=r={})),t.RegistrationType=class{____;method;constructor(e){this.method=e}};class o extends i.RequestType0{__;___;____;_pr;constructor(e){super(e)}}t.ProtocolRequestType0=o;class s extends i.RequestType{__;___;____;_pr;constructor(e){super(e,i.ParameterStructures.byName)}}t.ProtocolRequestType=s;class a extends i.NotificationType0{___;____;constructor(e){super(e)}}t.ProtocolNotificationType0=a;class c extends i.NotificationType{___;____;constructor(e){super(e,i.ParameterStructures.byName)}}t.ProtocolNotificationType=c},7103:e=>{"use strict";e.exports=function(e){e.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}},7252:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=o(n(1398));class a extends s.DocumentLink{data;constructor(e,t){super(e,t)}}t.default=a},7267:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SelectionRangeRequest=void 0;const i=n(7096);var r;!function(e){e.method="textDocument/selectionRange",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.SelectionRangeRequest=r={}))},7327:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ColorProviderFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);class s extends o.TextDocumentLanguageFeature{constructor(e){super(e,r.DocumentColorRequest.type)}fillClientCapabilities(e){(0,o.ensure)((0,o.ensure)(e,"textDocument"),"colorProvider").dynamicRegistration=!0}initialize(e,t){const[n,i]=this.getRegistration(t,e.colorProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=e.documentSelector,n={provideColorPresentations:(e,t,n)=>{const i=this._client,o=(e,t,n)=>{const o={color:e,textDocument:i.code2ProtocolConverter.asTextDocumentIdentifier(t.document),range:i.code2ProtocolConverter.asRange(t.range)};return i.sendRequest(r.ColorPresentationRequest.type,o,n).then(e=>n.isCancellationRequested?null:this._client.protocol2CodeConverter.asColorPresentations(e,n),e=>i.handleFailedRequest(r.ColorPresentationRequest.type,n,e,null))},s=i.middleware;return s.provideColorPresentations?s.provideColorPresentations(e,t,n,o):o(e,t,n)},provideDocumentColors:(e,t)=>{const n=this._client,i=(e,t)=>{const i={textDocument:n.code2ProtocolConverter.asTextDocumentIdentifier(e)};return n.sendRequest(r.DocumentColorRequest.type,i,t).then(e=>t.isCancellationRequested?null:this._client.protocol2CodeConverter.asColorInformations(e,t),e=>n.handleFailedRequest(r.DocumentColorRequest.type,t,e,null))},o=n.middleware;return o.provideDocumentColors?o.provideDocumentColors(e,t,i):i(e,t)}};return[i.languages.registerColorProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),n),n]}}t.ColorProviderFeature=s},7365:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.createMessageConnection=t.ConnectionOptions=t.MessageStrategy=t.CancellationStrategy=t.CancellationSenderStrategy=t.CancellationReceiverStrategy=t.RequestCancellationReceiverStrategy=t.IdCancellationReceiverStrategy=t.ConnectionStrategy=t.ConnectionError=t.ConnectionErrors=t.LogTraceNotification=t.SetTraceNotification=t.TraceFormat=t.TraceValues=t.TraceValue=t.Trace=t.NullLogger=t.ProgressType=t.ProgressToken=void 0;const a=s(n(9042)),c=o(n(6357)),u=n(8045),l=n(5140),d=n(6712),h=n(4782);var p,f,g,m,v,y,b,_,C,D,S,w,R,T,P,O,k,x,E;!function(e){e.type=new u.NotificationType("$/cancelRequest")}(p||(p={})),function(e){e.is=function(e){return"string"==typeof e||"number"==typeof e}}(f||(t.ProgressToken=f={})),function(e){e.type=new u.NotificationType("$/progress")}(g||(g={})),t.ProgressType=class{__;_pr;constructor(){}},function(e){e.is=function(e){return c.func(e)}}(m||(m={})),t.NullLogger=Object.freeze({error:()=>{},warn:()=>{},info:()=>{},log:()=>{}}),function(e){e[e.Off=0]="Off",e[e.Messages=1]="Messages",e[e.Compact=2]="Compact",e[e.Verbose=3]="Verbose"}(v||(t.Trace=v={})),function(e){e.Off="off",e.Messages="messages",e.Compact="compact",e.Verbose="verbose"}(y||(t.TraceValue=y={})),t.TraceValues=y,function(e){e.fromString=function(t){if(!c.string(t))return e.Off;switch(t=t.toLowerCase()){case"off":default:return e.Off;case"messages":return e.Messages;case"compact":return e.Compact;case"verbose":return e.Verbose}},e.toString=function(t){switch(t){case e.Off:return"off";case e.Messages:return"messages";case e.Compact:return"compact";case e.Verbose:return"verbose";default:return"off"}}}(v||(t.Trace=v={})),function(e){e.Text="text",e.JSON="json"}(b||(t.TraceFormat=b={})),function(e){e.fromString=function(t){return c.string(t)&&"json"===(t=t.toLowerCase())?e.JSON:e.Text}}(b||(t.TraceFormat=b={})),function(e){e.type=new u.NotificationType("$/setTrace")}(_||(t.SetTraceNotification=_={})),function(e){e.type=new u.NotificationType("$/logTrace")}(C||(t.LogTraceNotification=C={})),function(e){e[e.Closed=1]="Closed",e[e.Disposed=2]="Disposed",e[e.AlreadyListening=3]="AlreadyListening"}(D||(t.ConnectionErrors=D={}));class M extends Error{code;constructor(e,t){super(t),this.code=e,Object.setPrototypeOf(this,M.prototype)}}t.ConnectionError=M,function(e){e.is=function(e){const t=e;return t&&c.func(t.cancelUndispatched)}}(S||(t.ConnectionStrategy=S={})),function(e){e.is=function(e){const t=e;return t&&(void 0===t.kind||"id"===t.kind)&&c.func(t.createCancellationTokenSource)&&(void 0===t.dispose||c.func(t.dispose))}}(w||(t.IdCancellationReceiverStrategy=w={})),function(e){e.is=function(e){const t=e;return t&&"request"===t.kind&&c.func(t.createCancellationTokenSource)&&(void 0===t.dispose||c.func(t.dispose))}}(R||(t.RequestCancellationReceiverStrategy=R={})),function(e){e.Message=Object.freeze({createCancellationTokenSource:e=>new h.CancellationTokenSource}),e.is=function(e){return w.is(e)||R.is(e)}}(T||(t.CancellationReceiverStrategy=T={})),function(e){e.Message=Object.freeze({sendCancellation:(e,t)=>e.sendNotification(p.type,{id:t}),cleanup(e){}}),e.is=function(e){const t=e;return t&&c.func(t.sendCancellation)&&c.func(t.cleanup)}}(P||(t.CancellationSenderStrategy=P={})),function(e){e.Message=Object.freeze({receiver:T.Message,sender:P.Message}),e.is=function(e){const t=e;return t&&T.is(t.receiver)&&P.is(t.sender)}}(O||(t.CancellationStrategy=O={})),function(e){e.is=function(e){const t=e;return t&&c.func(t.handleMessage)}}(k||(t.MessageStrategy=k={})),function(e){e.is=function(e){const t=e;return t&&(O.is(t.cancellationStrategy)||S.is(t.connectionStrategy)||k.is(t.messageStrategy)||c.number(t.maxParallelism))}}(x||(t.ConnectionOptions=x={})),function(e){e[e.New=1]="New",e[e.Listening=2]="Listening",e[e.Closed=3]="Closed",e[e.Disposed=4]="Disposed"}(E||(E={})),t.createMessageConnection=function(e,n,i,r){const o=void 0!==i?i:t.NullLogger;let s=0,y=0,S=0;const R="2.0",T=r?.maxParallelism??-1;let P,x=0;const F=new Map;let q;const I=new Map,N=new Map;let j,L,A=new l.LinkedMap,$=new Map,H=new Set,W=new Map,U=v.Off,K=b.Text,z=E.New;const B=new d.Emitter,V=new d.Emitter,G=new d.Emitter,X=new d.Emitter,J=new d.Emitter,Q=r&&r.cancellationStrategy?r.cancellationStrategy:O.Message;function Y(e){}function Z(){return z===E.Listening}function ee(){return z===E.Closed}function te(){return z===E.Disposed}function ne(){z!==E.New&&z!==E.Listening||(z=E.Closed,V.fire(void 0))}function ie(e){if(null===e)throw new Error("Can't send requests with id null since the response can't be correlated.");return"req-"+e.toString()}function re(){j||0===A.size||-1!==T&&x>=T||(j=(0,a.default)().timer.setImmediate(async()=>{if(j=void 0,0===A.size)return;if(-1!==T&&x>=T)return;const e=A.shift();let t;try{x++;const n=r?.messageStrategy;t=k.is(n)?n.handleMessage(e,oe):oe(e)}catch(e){o.error(`Processing message queue failed: ${e.toString()}`)}finally{t instanceof Promise?t.then(()=>{x--,re()}).catch(e=>{o.error(`Processing message queue failed: ${e.toString()}`)}):x--,re()}}))}async function oe(e){return u.Message.isRequest(e)?async function(e){if(te())return Promise.resolve();function t(t,i,r){const o={jsonrpc:R,id:e.id};return t instanceof u.ResponseError?o.error=t.toJson():o.result=void 0===t?null:t,ue(o,i,r),n.write(o)}function i(t,i,r){const o={jsonrpc:R,id:e.id,error:t.toJson()};return ue(o,i,r),n.write(o)}!function(e){if(U!==v.Off&&L)if(K===b.Text){let t;U!==v.Verbose&&U!==v.Compact||!e.params||(t=`Params: ${ce(e.params)}\n\n`),L.log(`Received request '${e.method} - (${e.id})'.`,t)}else de("receive-request",e)}(e);const r=F.get(e.method);let o,s;r&&(o=r.type,s=r.handler);const a=Date.now();if(s||P){const n=e.id??String(Date.now()),r=w.is(Q.receiver)?Q.receiver.createCancellationTokenSource(n):Q.receiver.createCancellationTokenSource(e);null!==e.id&&H.has(e.id)&&r.cancel(),null!==e.id&&W.set(n,r);try{let n;if(s)if(void 0===e.params){if(void 0!==o&&0!==o.numberOfParams)return i(new u.ResponseError(u.ErrorCodes.InvalidParams,`Request ${e.method} defines ${o.numberOfParams} params but received none.`),e.method,a);n=s(r.token)}else if(Array.isArray(e.params)){if(void 0!==o&&o.parameterStructures===u.ParameterStructures.byName)return i(new u.ResponseError(u.ErrorCodes.InvalidParams,`Request ${e.method} defines parameters by name but received parameters by position`),e.method,a);n=s(...e.params,r.token)}else{if(void 0!==o&&o.parameterStructures===u.ParameterStructures.byPosition)return i(new u.ResponseError(u.ErrorCodes.InvalidParams,`Request ${e.method} defines parameters by position but received parameters by name`),e.method,a);n=s(e.params,r.token)}else P&&(n=P(e.method,e.params,r.token));const c=await n;await t(c,e.method,a)}catch(n){n instanceof u.ResponseError?await t(n,e.method,a):n&&c.string(n.message)?await i(new u.ResponseError(u.ErrorCodes.InternalError,`Request ${e.method} failed with message: ${n.message}`),e.method,a):await i(new u.ResponseError(u.ErrorCodes.InternalError,`Request ${e.method} failed unexpectedly without providing any details.`),e.method,a)}finally{W.delete(n)}}else await i(new u.ResponseError(u.ErrorCodes.MethodNotFound,`Unhandled method ${e.method}`),e.method,a)}(e):u.Message.isNotification(e)?async function(e){if(te())return;let t,n;if(e.method===p.type.method){const t=e.params.id;return H.delete(t),void le(e)}{const i=I.get(e.method);i&&(n=i.handler,t=i.type)}if(n||q)try{if(le(e),n)if(void 0===e.params)void 0!==t&&0!==t.numberOfParams&&t.parameterStructures!==u.ParameterStructures.byName&&o.error(`Notification ${e.method} defines ${t.numberOfParams} params but received none.`),await n();else if(Array.isArray(e.params)){const i=e.params;e.method===g.type.method&&2===i.length&&f.is(i[0])?await n({token:i[0],value:i[1]}):(void 0!==t&&(t.parameterStructures===u.ParameterStructures.byName&&o.error(`Notification ${e.method} defines parameters by name but received parameters by position`),t.numberOfParams!==e.params.length&&o.error(`Notification ${e.method} defines ${t.numberOfParams} params but received ${i.length} arguments`)),await n(...i))}else void 0!==t&&t.parameterStructures===u.ParameterStructures.byPosition&&o.error(`Notification ${e.method} defines parameters by position but received parameters by name`),await n(e.params);else q&&await q(e.method,e.params)}catch(t){t.message?o.error(`Notification handler '${e.method}' failed with message: ${t.message}`):o.error(`Notification handler '${e.method}' failed unexpectedly.`)}else G.fire(e)}(e):u.Message.isResponse(e)?ae(e):function(e){if(!e)return void o.error("Received empty message.");o.error(`Received message which is neither a response nor a notification message:\n${JSON.stringify(e,null,4)}`);const t=e;if(c.string(t.id)||c.number(t.id)){const e=t.id,n=$.get(e);n&&n.reject(new Error("The received response has neither a result nor an error property."))}}(e)}e.onClose(ne),e.onError(function(e){B.fire([e,void 0,void 0])}),n.onClose(ne),n.onError(function(e){B.fire(e)});const se=e=>{try{if(u.Message.isNotification(e)&&e.method===p.type.method){const t=e.params.id,i=ie(t),s=A.get(i);if(u.Message.isRequest(s)){const a=r?.connectionStrategy,c=a&&a.cancelUndispatched?a.cancelUndispatched(s,Y):void 0;if(c&&(void 0!==c.error||void 0!==c.result))return A.delete(i),W.delete(t),c.id=s.id,ue(c,e.method,Date.now()),void n.write(c).catch(()=>o.error("Sending response for canceled message failed."))}const a=W.get(t);if(void 0!==a)return a.cancel(),void le(e);H.add(t)}!function(e,t){var n;u.Message.isRequest(t)?e.set(ie(t.id),t):u.Message.isResponse(t)?-1===T?e.set(null===(n=t.id)?"res-unknown-"+(++S).toString():"res-"+n.toString(),t):ae(t):e.set("not-"+(++y).toString(),t)}(A,e)}finally{re()}};function ae(e){if(!te())if(null===e.id)e.error?o.error(`Received response message without id: Error is: \n${JSON.stringify(e.error,void 0,4)}`):o.error("Received response message without id. No further error information provided.");else{const t=e.id,n=$.get(t);if(function(e,t){if(U!==v.Off&&L)if(K===b.Text){let n;if(U!==v.Verbose&&U!==v.Compact||(e.error&&e.error.data?n=`Error data: ${ce(e.error.data)}\n\n`:e.result?n=`Result: ${ce(e.result)}\n\n`:void 0===e.error&&(n="No result returned.\n\n")),t){const i=e.error?` Request failed: ${e.error.message} (${e.error.code}).`:"";L.log(`Received response '${t.method} - (${e.id})' in ${Date.now()-t.timerStart}ms.${i}`,n)}else L.log(`Received response ${e.id} without active response promise.`,n)}else de("receive-response",e)}(e,n),void 0!==n){$.delete(t);try{if(e.error){const t=e.error;n.reject(new u.ResponseError(t.code,t.message,t.data))}else{if(void 0===e.result)throw new Error("Should never happen.");n.resolve(e.result)}}catch(e){e.message?o.error(`Response handler '${n.method}' failed with message: ${e.message}`):o.error(`Response handler '${n.method}' failed unexpectedly.`)}}}}function ce(e){if(null!=e)switch(U){case v.Verbose:return JSON.stringify(e,null,4);case v.Compact:return JSON.stringify(e);default:return}}function ue(e,t,n){if(U!==v.Off&&L)if(K===b.Text){let i;U!==v.Verbose&&U!==v.Compact||(e.error&&e.error.data?i=`Error data: ${ce(e.error.data)}\n\n`:e.result?i=`Result: ${ce(e.result)}\n\n`:void 0===e.error&&(i="No result returned.\n\n")),L.log(`Sending response '${t} - (${e.id})'. Processing request took ${Date.now()-n}ms`,i)}else de("send-response",e)}function le(e){if(U!==v.Off&&L&&e.method!==C.type.method)if(K===b.Text){let t;U!==v.Verbose&&U!==v.Compact||(t=e.params?`Params: ${ce(e.params)}\n\n`:"No parameters provided.\n\n"),L.log(`Received notification '${e.method}'.`,t)}else de("receive-notification",e)}function de(e,t){if(!L||U===v.Off)return;const n={isLSPMessage:!0,type:e,message:t,timestamp:Date.now()};L.log(n)}function he(){if(ee())throw new M(D.Closed,"Connection is closed.");if(te())throw new M(D.Disposed,"Connection is disposed.")}function pe(e){return void 0===e?null:e}function fe(e){return null===e?void 0:e}function ge(e){return null!=e&&!Array.isArray(e)&&"object"==typeof e}function me(e,t){switch(e){case u.ParameterStructures.auto:return ge(t)?fe(t):[pe(t)];case u.ParameterStructures.byName:if(!ge(t))throw new Error("Received parameters by name but param is not an object literal.");return fe(t);case u.ParameterStructures.byPosition:return[pe(t)];default:throw new Error(`Unknown parameter structure ${e.toString()}`)}}function ve(e,t){let n;const i=e.numberOfParams;switch(i){case 0:n=void 0;break;case 1:n=me(e.parameterStructures,t[0]);break;default:n=[];for(let e=0;e<t.length&&e<i;e++)n.push(pe(t[e]));if(t.length<i)for(let e=t.length;e<i;e++)n.push(null)}return n}const ye={sendNotification:(e,...t)=>{let i,r;if(he(),c.string(e)){i=e;const n=t[0];let o=0,s=u.ParameterStructures.auto;u.ParameterStructures.is(n)&&(o=1,s=n);const a=t.length,c=a-o;switch(c){case 0:r=void 0;break;case 1:r=me(s,t[o]);break;default:if(s===u.ParameterStructures.byName)throw new Error(`Received ${c} parameters for 'by Name' notification parameter structure.`);r=t.slice(o,a).map(e=>pe(e))}}else{const n=t;i=e.method,r=ve(e,n)}const s={jsonrpc:R,method:i,params:r};return function(e){if(U!==v.Off&&L)if(K===b.Text){let t;U!==v.Verbose&&U!==v.Compact||(t=e.params?`Params: ${ce(e.params)}\n\n`:"No parameters provided.\n\n"),L.log(`Sending notification '${e.method}'.`,t)}else de("send-notification",e)}(s),n.write(s).catch(e=>{throw o.error("Sending notification failed."),e})},onNotification:(e,t)=>{let n;return he(),c.func(e)?q=e:t&&(c.string(e)?(n=e,I.set(e,{type:void 0,handler:t})):(n=e.method,I.set(e.method,{type:e,handler:t}))),{dispose:()=>{void 0!==n?I.delete(n):q=void 0}}},onProgress:(e,t,n)=>{if(N.has(t))throw new Error(`Progress handler for token ${t} already registered`);return N.set(t,n),{dispose:()=>{N.delete(t)}}},sendProgress:(e,t,n)=>ye.sendNotification(g.type,{token:t,value:n}),onUnhandledProgress:X.event,sendRequest:(e,...t)=>{function i(e,t){const n=Q.sender.sendCancellation(e,t);void 0===n?o.log(`Received no promise from cancellation strategy when cancelling id ${t}`):n.catch(()=>{o.log(`Sending cancellation messages for id ${t} failed.`)})}let r,a,l;if(he(),function(){if(!Z())throw new Error("Call listen() first.")}(),c.string(e)){r=e;const n=t[0],i=t[t.length-1];let o=0,s=u.ParameterStructures.auto;u.ParameterStructures.is(n)&&(o=1,s=n);let c=t.length;h.CancellationToken.is(i)&&(c-=1,l=i);const d=c-o;switch(d){case 0:a=void 0;break;case 1:a=me(s,t[o]);break;default:if(s===u.ParameterStructures.byName)throw new Error(`Received ${d} parameters for 'by Name' request parameter structure.`);a=t.slice(o,c).map(e=>pe(e))}}else{const n=t;r=e.method,a=ve(e,n);const i=e.numberOfParams;l=h.CancellationToken.is(n[i])?n[i]:void 0}const d=s++;let p,f=!1;void 0!==l&&(l.isCancellationRequested?f=!0:p=l.onCancellationRequested(()=>{i(ye,d)}));const g={jsonrpc:R,id:d,method:r,params:a};return function(e){if(U!==v.Off&&L)if(K===b.Text){let t;U!==v.Verbose&&U!==v.Compact||!e.params||(t=`Params: ${ce(e.params)}\n\n`),L.log(`Sending request '${e.method} - (${e.id})'.`,t)}else de("send-request",e)}(g),"function"==typeof Q.sender.enableCancellation&&Q.sender.enableCancellation(g),new Promise(async(e,t)=>{const s={method:r,timerStart:Date.now(),resolve:t=>{e(t),Q.sender.cleanup(d),p?.dispose()},reject:e=>{t(e),Q.sender.cleanup(d),p?.dispose()}};try{$.set(d,s),await n.write(g),f&&i(ye,d)}catch(e){throw $.delete(d),s.reject(new u.ResponseError(u.ErrorCodes.MessageWriteError,e.message?e.message:"Unknown reason")),o.error("Sending request failed."),e}})},onRequest:(e,t)=>{he();let n=null;return m.is(e)?(n=void 0,P=e):c.string(e)?(n=null,void 0!==t&&(n=e,F.set(e,{handler:t,type:void 0}))):void 0!==t&&(n=e.method,F.set(e.method,{type:e,handler:t})),{dispose:()=>{null!==n&&(void 0!==n?F.delete(n):P=void 0)}}},hasPendingResponse:()=>$.size>0,trace:async(e,t,n)=>{let i=!1,r=b.Text;void 0!==n&&(c.boolean(n)?i=n:(i=n.sendNotification||!1,r=n.traceFormat||b.Text)),U=e,K=r,L=U===v.Off?void 0:t,!i||ee()||te()||await ye.sendNotification(_.type,{value:v.toString(e)})},onError:B.event,onClose:V.event,onUnhandledNotification:G.event,onDispose:J.event,end:()=>{n.end()},dispose:()=>{if(te())return;z=E.Disposed,J.fire(void 0);const t=new u.ResponseError(u.ErrorCodes.PendingResponseRejected,"Pending response rejected since connection got disposed");for(const e of $.values())e.reject(t);$=new Map,W=new Map,H=new Set,A=new l.LinkedMap,c.func(n.dispose)&&n.dispose(),c.func(e.dispose)&&e.dispose()},listen:()=>{he(),function(){if(Z())throw new M(D.AlreadyListening,"Connection is already listening")}(),z=E.Listening,e.listen(se)},inspect:()=>{(0,a.default)().console.log("inspect")}};return ye.onNotification(C.type,e=>{if(U===v.Off||!L)return;const t=U===v.Verbose||U===v.Compact;L.log(e.message,t?e.verbose:void 0)}),ye.onNotification(g.type,async e=>{const t=N.get(e.token);t?await t(e.value):X.fire(e)}),ye}},7408:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractMessageBuffer=void 0,t.AbstractMessageBuffer=class{_encoding;_chunks;_totalLength;constructor(e="utf-8"){this._encoding=e,this._chunks=[],this._totalLength=0}get encoding(){return this._encoding}append(e){const t="string"==typeof e?this.fromString(e,this._encoding):e;this._chunks.push(t),this._totalLength+=t.byteLength}tryReadHeaders(e=!1){if(0===this._chunks.length)return;let t=0,n=0,i=0,r=0;e:for(;n<this._chunks.length;){const e=this._chunks[n];for(i=0;i<e.length;){switch(e[i]){case 13:switch(t){case 0:t=1;break;case 2:t=3;break;default:t=0}break;case 10:switch(t){case 1:t=2;break;case 3:t=4,i++;break e;default:t=0}break;default:t=0}i++}r+=e.byteLength,n++}if(4!==t)return;const o=this._read(r+i),s=new Map,a=this.toString(o,"ascii").split("\r\n");if(a.length<2)return s;for(let t=0;t<a.length-2;t++){const n=a[t],i=n.indexOf(":");if(-1===i)throw new Error(`Message header must separate key and value using ':'\n${n}`);const r=n.substr(0,i),o=n.substr(i+1).trim();s.set(e?r.toLowerCase():r,o)}return s}tryReadBody(e){if(!(this._totalLength<e))return this._read(e)}get numberOfBytes(){return this._totalLength}_read(e){if(0===e)return this.emptyBuffer();if(e>this._totalLength)throw new Error("Cannot read so many bytes!");if(this._chunks[0].byteLength===e){const t=this._chunks[0];return this._chunks.shift(),this._totalLength-=e,this.asNative(t)}if(this._chunks[0].byteLength>e){const t=this._chunks[0],n=this.asNative(t,e);return this._chunks[0]=t.slice(e),this._totalLength-=e,n}const t=this.allocNative(e);let n=0;for(;e>0;){const i=this._chunks[0];if(i.byteLength>e){const r=i.slice(0,e);t.set(r,n),n+=e,this._chunks[0]=i.slice(e),this._totalLength-=e,e-=e}else t.set(i,n),n+=i.byteLength,this._chunks.shift(),this._totalLength-=i.byteLength,e-=i.byteLength}return t}}},7511:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlineValueFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);class s extends o.TextDocumentLanguageFeature{constructor(e){super(e,r.InlineValueRequest.type)}fillClientCapabilities(e){(0,o.ensure)((0,o.ensure)(e,"textDocument"),"inlineValue").dynamicRegistration=!0,(0,o.ensure)((0,o.ensure)(e,"workspace"),"inlineValue").refreshSupport=!0}initialize(e,t){this._client.onRequest(r.InlineValueRefreshRequest.type,async()=>{for(const e of this.getAllProviders())e.onDidChangeInlineValues.fire()});const[n,i]=this.getRegistration(t,e.inlineValueProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=e.documentSelector,n=new i.EventEmitter,o={onDidChangeInlineValues:n.event,provideInlineValues:(e,t,n,i)=>{const o=this._client,s=(e,t,n,i)=>{const s={textDocument:o.code2ProtocolConverter.asTextDocumentIdentifier(e),range:o.code2ProtocolConverter.asRange(t),context:o.code2ProtocolConverter.asInlineValueContext(n)};return o.sendRequest(r.InlineValueRequest.type,s,i).then(e=>i.isCancellationRequested?null:o.protocol2CodeConverter.asInlineValues(e,i),e=>o.handleFailedRequest(r.InlineValueRequest.type,i,e,null))},a=o.middleware;return a.provideInlineValues?a.provideInlineValues(e,t,n,i,s):s(e,t,n,i)}};return[this.registerProvider(t,o),{provider:o,onDidChangeInlineValues:n}]}registerProvider(e,t){return i.languages.registerInlineValuesProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}}t.InlineValueFeature=s},7523:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ImplementationFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);class s extends o.TextDocumentLanguageFeature{constructor(e){super(e,r.ImplementationRequest.type)}fillClientCapabilities(e){const t=(0,o.ensure)((0,o.ensure)(e,"textDocument"),"implementation");t.dynamicRegistration=!0,t.linkSupport=!0}initialize(e,t){const[n,i]=this.getRegistration(t,e.implementationProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=e.documentSelector,n={provideImplementation:(e,t,n)=>{const i=this._client,o=(e,t,n)=>i.sendRequest(r.ImplementationRequest.type,i.code2ProtocolConverter.asTextDocumentPositionParams(e,t),n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asDefinitionResult(e,n),e=>i.handleFailedRequest(r.ImplementationRequest.type,n,e,null)),s=i.middleware;return s.provideImplementation?s.provideImplementation(e,t,n,o):o(e,t,n)}};return[this.registerProvider(t,n),n]}registerProvider(e,t){return i.languages.registerImplementationProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}}t.ImplementationFeature=s},7553:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TypeHierarchySubtypesRequest=t.TypeHierarchySupertypesRequest=t.TypeHierarchyPrepareRequest=void 0;const i=n(7096);var r,o,s;!function(e){e.method="textDocument/prepareTypeHierarchy",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.TypeHierarchyPrepareRequest=r={})),function(e){e.method="typeHierarchy/supertypes",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(o||(t.TypeHierarchySupertypesRequest=o={})),function(e){e.method="typeHierarchy/subtypes",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(s||(t.TypeHierarchySubtypesRequest=s={}))},7659:(e,t,n)=>{const i=n(5380);e.exports=(e,t,n)=>0!==i(e,t,n)},7784:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createProtocolConnection=void 0;const i=n(9765);t.createProtocolConnection=function(e,t,n,r){return i.ConnectionStrategy.is(r)&&(r={connectionStrategy:r}),(0,i.createMessageConnection)(e,t,n,r)}},7786:(e,t)=>{"use strict";function n(e){return"string"==typeof e||e instanceof String}function i(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.objectLiteral=t.typedArray=t.stringArray=t.array=t.func=t.error=t.number=t.string=t.boolean=void 0,t.boolean=function(e){return!0===e||!1===e},t.string=n,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=function(e){return"function"==typeof e},t.array=i,t.stringArray=function(e){return i(e)&&e.every(e=>n(e))},t.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)},t.objectLiteral=function(e){return null!==e&&"object"==typeof e}},7904:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getCustomDataSource=function(e){let t=o(),n=s();const r=new i.EventEmitter;return e.push(i.extensions.onDidChange(e=>{const t=s();t.length===n.length&&t.every((e,t)=>e===n[t])||(n=t,r.fire())})),e.push(i.workspace.onDidChangeConfiguration(e=>{e.affectsConfiguration("css.customData")&&(t=o(),r.fire())})),{get uris(){return t.concat(n)},get onDidChange(){return r.event}}};const i=n(1398),r=n(5554);function o(){const e=i.workspace.workspaceFolders,t=[];if(!e)return t;const n=(e,n)=>{if(Array.isArray(e))for(const i of e)"string"==typeof i&&t.push(r.Utils.resolvePath(n,i).toString())};for(let t=0;t<e.length;t++){const r=e[t].uri,o=i.workspace.getConfiguration("css",r).inspect("customData");o&&(n(o.workspaceFolderValue,r),0===t&&(i.workspace.workspaceFile&&n(o.workspaceValue,i.workspace.workspaceFile),n(o.globalValue,r)))}return t}function s(){const e=[];for(const t of i.extensions.all){const n=t.packageJSON?.contributes?.css?.customData;if(Array.isArray(n))for(const i of n)e.push(r.Utils.joinPath(t.extensionUri,i).toString())}return e}},7921:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DeclarationFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);class s extends o.TextDocumentLanguageFeature{constructor(e){super(e,r.DeclarationRequest.type)}fillClientCapabilities(e){const t=(0,o.ensure)((0,o.ensure)(e,"textDocument"),"declaration");t.dynamicRegistration=!0,t.linkSupport=!0}initialize(e,t){const[n,i]=this.getRegistration(t,e.declarationProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=e.documentSelector,n={provideDeclaration:(e,t,n)=>{const i=this._client,o=(e,t,n)=>i.sendRequest(r.DeclarationRequest.type,i.code2ProtocolConverter.asTextDocumentPositionParams(e,t),n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asDeclarationResult(e,n),e=>i.handleFailedRequest(r.DeclarationRequest.type,n,e,null)),s=i.middleware;return s.provideDeclaration?s.provideDeclaration(e,t,n,o):o(e,t,n)}};return[this.registerProvider(t,n),n]}registerProvider(e,t){return i.languages.registerDeclarationProvider(this._client.protocol2CodeConverter.asDocumentSelector(e),t)}}t.DeclarationFeature=s},7944:(e,t,n)=>{const i=n(9796),{MAX_LENGTH:r,MAX_SAFE_INTEGER:o}=n(9630),{safeRe:s,t:a}=n(6850),c=n(7095),{compareIdentifiers:u}=n(4031);class l{constructor(e,t){if(t=c(t),e instanceof l){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>r)throw new TypeError(`version is longer than ${r} characters`);i("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const n=e.trim().match(t.loose?s[a.LOOSE]:s[a.FULL]);if(!n)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+n[1],this.minor=+n[2],this.patch=+n[3],this.major>o||this.major<0)throw new TypeError("Invalid major version");if(this.minor>o||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>o||this.patch<0)throw new TypeError("Invalid patch version");n[4]?this.prerelease=n[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<o)return t}return e}):this.prerelease=[],this.build=n[5]?n[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(i("SemVer.compare",this.version,this.options,e),!(e instanceof l)){if("string"==typeof e&&e===this.version)return 0;e=new l(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof l||(e=new l(e,this.options)),u(this.major,e.major)||u(this.minor,e.minor)||u(this.patch,e.patch)}comparePre(e){if(e instanceof l||(e=new l(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const n=this.prerelease[t],r=e.prerelease[t];if(i("prerelease compare",t,n,r),void 0===n&&void 0===r)return 0;if(void 0===r)return 1;if(void 0===n)return-1;if(n!==r)return u(n,r)}while(++t)}compareBuild(e){e instanceof l||(e=new l(e,this.options));let t=0;do{const n=this.build[t],r=e.build[t];if(i("prerelease compare",t,n,r),void 0===n&&void 0===r)return 0;if(void 0===r)return 1;if(void 0===n)return-1;if(n!==r)return u(n,r)}while(++t)}inc(e,t,n){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,n);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,n);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,n),this.inc("pre",t,n);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,n),this.inc("pre",t,n);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{const e=Number(n)?1:0;if(!t&&!1===n)throw new Error("invalid increment argument: identifier is empty");if(0===this.prerelease.length)this.prerelease=[e];else{let i=this.prerelease.length;for(;--i>=0;)"number"==typeof this.prerelease[i]&&(this.prerelease[i]++,i=-2);if(-1===i){if(t===this.prerelease.join(".")&&!1===n)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let i=[t,e];!1===n&&(i=[t]),0===u(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=i):this.prerelease=i}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=l},7975:function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Semaphore=void 0;const r=i(n(9042));t.Semaphore=class{_capacity;_active;_waiting;constructor(e=1){if(e<=0)throw new Error("Capacity must be greater than 0");this._capacity=e,this._active=0,this._waiting=[]}lock(e){return new Promise((t,n)=>{this._waiting.push({thunk:e,resolve:t,reject:n}),this.runNext()})}get active(){return this._active}runNext(){0!==this._waiting.length&&this._active!==this._capacity&&(0,r.default)().timer.setImmediate(()=>this.doRunNext())}doRunNext(){if(0===this._waiting.length||this._active===this._capacity)return;const e=this._waiting.shift();if(this._active++,this._active>this._capacity)throw new Error("To many thunks active");try{const t=e.thunk();t instanceof Promise?t.then(t=>{this._active--,e.resolve(t),this.runNext()},t=>{this._active--,e.reject(t),this.runNext()}):(this._active--,e.resolve(t),this.runNext())}catch(t){this._active--,e.reject(t),this.runNext()}}}},7984:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentSymbolFeature=t.SupportedSymbolTags=t.SupportedSymbolKinds=void 0;const s=n(1398),a=n(3434),c=n(9810),u=o(n(8820));t.SupportedSymbolKinds=[a.SymbolKind.File,a.SymbolKind.Module,a.SymbolKind.Namespace,a.SymbolKind.Package,a.SymbolKind.Class,a.SymbolKind.Method,a.SymbolKind.Property,a.SymbolKind.Field,a.SymbolKind.Constructor,a.SymbolKind.Enum,a.SymbolKind.Interface,a.SymbolKind.Function,a.SymbolKind.Variable,a.SymbolKind.Constant,a.SymbolKind.String,a.SymbolKind.Number,a.SymbolKind.Boolean,a.SymbolKind.Array,a.SymbolKind.Object,a.SymbolKind.Key,a.SymbolKind.Null,a.SymbolKind.EnumMember,a.SymbolKind.Struct,a.SymbolKind.Event,a.SymbolKind.Operator,a.SymbolKind.TypeParameter],t.SupportedSymbolTags=[a.SymbolTag.Deprecated];class l extends c.TextDocumentLanguageFeature{constructor(e){super(e,a.DocumentSymbolRequest.type)}fillClientCapabilities(e){const n=(0,c.ensure)((0,c.ensure)(e,"textDocument"),"documentSymbol");n.dynamicRegistration=!0,n.symbolKind={valueSet:t.SupportedSymbolKinds},n.hierarchicalDocumentSymbolSupport=!0,n.tagSupport={valueSet:t.SupportedSymbolTags},n.labelSupport=!0}initialize(e,t){const n=this.getRegistrationOptions(t,e.documentSymbolProvider);n&&this.register({id:u.generateUuid(),registerOptions:n})}registerLanguageProvider(e){const t=e.documentSelector,n={provideDocumentSymbols:(e,t)=>{const n=this._client,i=async(e,t)=>{try{const i=await n.sendRequest(a.DocumentSymbolRequest.type,n.code2ProtocolConverter.asDocumentSymbolParams(e),t);if(t.isCancellationRequested||null==i)return null;if(0===i.length)return[];{const e=i[0];return a.DocumentSymbol.is(e)?await n.protocol2CodeConverter.asDocumentSymbols(i,t):await n.protocol2CodeConverter.asSymbolInformations(i,t)}}catch(e){return n.handleFailedRequest(a.DocumentSymbolRequest.type,t,e,null)}},r=n.middleware;return r.provideDocumentSymbols?r.provideDocumentSymbols(e,t,i):i(e,t)}},i=void 0!==e.label?{label:e.label}:void 0;return[s.languages.registerDocumentSymbolProvider(this._client.protocol2CodeConverter.asDocumentSelector(t),n,i),n]}}t.DocumentSymbolFeature=l},8045:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.Message=t.NotificationType9=t.NotificationType8=t.NotificationType7=t.NotificationType6=t.NotificationType5=t.NotificationType4=t.NotificationType3=t.NotificationType2=t.NotificationType1=t.NotificationType0=t.NotificationType=t.RequestType9=t.RequestType8=t.RequestType7=t.RequestType6=t.RequestType5=t.RequestType4=t.RequestType3=t.RequestType2=t.RequestType1=t.RequestType=t.RequestType0=t.AbstractMessageSignature=t.ParameterStructures=t.ResponseError=t.ErrorCodes=void 0;const s=o(n(6357));var a,c;!function(e){e.ParseError=-32700,e.InvalidRequest=-32600,e.MethodNotFound=-32601,e.InvalidParams=-32602,e.InternalError=-32603,e.jsonrpcReservedErrorRangeStart=-32099,e.serverErrorStart=-32099,e.MessageWriteError=-32099,e.MessageReadError=-32098,e.PendingResponseRejected=-32097,e.ConnectionInactive=-32096,e.ServerNotInitialized=-32002,e.UnknownErrorCode=-32001,e.jsonrpcReservedErrorRangeEnd=-32e3,e.serverErrorEnd=-32e3}(a||(t.ErrorCodes=a={}));class u extends Error{code;data;constructor(e,t,n){super(t),this.code=s.number(e)?e:a.UnknownErrorCode,this.data=n,Object.setPrototypeOf(this,u.prototype)}toJson(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data),e}}t.ResponseError=u;class l{kind;static auto=new l("auto");static byPosition=new l("byPosition");static byName=new l("byName");constructor(e){this.kind=e}static is(e){return e===l.auto||e===l.byName||e===l.byPosition}toString(){return this.kind}}t.ParameterStructures=l;class d{method;numberOfParams;constructor(e,t){this.method=e,this.numberOfParams=t}get parameterStructures(){return l.auto}}t.AbstractMessageSignature=d,t.RequestType0=class extends d{_;constructor(e){super(e,0)}},t.RequestType=class extends d{_parameterStructures;_;constructor(e,t=l.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.RequestType1=class extends d{_parameterStructures;_;constructor(e,t=l.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.RequestType2=class extends d{_;constructor(e){super(e,2)}},t.RequestType3=class extends d{_;constructor(e){super(e,3)}},t.RequestType4=class extends d{_;constructor(e){super(e,4)}},t.RequestType5=class extends d{_;constructor(e){super(e,5)}},t.RequestType6=class extends d{_;constructor(e){super(e,6)}},t.RequestType7=class extends d{_;constructor(e){super(e,7)}},t.RequestType8=class extends d{_;constructor(e){super(e,8)}},t.RequestType9=class extends d{_;constructor(e){super(e,9)}},t.NotificationType=class extends d{_parameterStructures;_;constructor(e,t=l.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.NotificationType0=class extends d{_;constructor(e){super(e,0)}},t.NotificationType1=class extends d{_parameterStructures;_;constructor(e,t=l.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.NotificationType2=class extends d{_;constructor(e){super(e,2)}},t.NotificationType3=class extends d{_;constructor(e){super(e,3)}},t.NotificationType4=class extends d{_;constructor(e){super(e,4)}},t.NotificationType5=class extends d{_;constructor(e){super(e,5)}},t.NotificationType6=class extends d{_;constructor(e){super(e,6)}},t.NotificationType7=class extends d{_;constructor(e){super(e,7)}},t.NotificationType8=class extends d{_;constructor(e){super(e,8)}},t.NotificationType9=class extends d{_;constructor(e){super(e,9)}},function(e){e.isRequest=function(e){const t=e;return t&&s.string(t.method)&&(s.string(t.id)||s.number(t.id))},e.isNotification=function(e){const t=e;return t&&s.string(t.method)&&void 0===e.id},e.isResponse=function(e){const t=e;return t&&(void 0!==t.result||!!t.error)&&(s.string(t.id)||s.number(t.id)||null===t.id)}}(c||(t.Message=c={}))},8382:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CallHierarchyFeature=void 0;const i=n(1398),r=n(3434),o=n(9810);class s{client;middleware;constructor(e){this.client=e,this.middleware=e.middleware}prepareCallHierarchy(e,t,n){const i=this.client,o=this.middleware,s=(e,t,n)=>{const o=i.code2ProtocolConverter.asTextDocumentPositionParams(e,t);return i.sendRequest(r.CallHierarchyPrepareRequest.type,o,n).then(e=>n.isCancellationRequested?null:i.protocol2CodeConverter.asCallHierarchyItems(e,n),e=>i.handleFailedRequest(r.CallHierarchyPrepareRequest.type,n,e,null))};return o.prepareCallHierarchy?o.prepareCallHierarchy(e,t,n,s):s(e,t,n)}provideCallHierarchyIncomingCalls(e,t){const n=this.client,i=this.middleware,o=(e,t)=>{const i={item:n.code2ProtocolConverter.asCallHierarchyItem(e)};return n.sendRequest(r.CallHierarchyIncomingCallsRequest.type,i,t).then(e=>t.isCancellationRequested?null:n.protocol2CodeConverter.asCallHierarchyIncomingCalls(e,t),e=>n.handleFailedRequest(r.CallHierarchyIncomingCallsRequest.type,t,e,null))};return i.provideCallHierarchyIncomingCalls?i.provideCallHierarchyIncomingCalls(e,t,o):o(e,t)}provideCallHierarchyOutgoingCalls(e,t){const n=this.client,i=this.middleware,o=(e,t)=>{const i={item:n.code2ProtocolConverter.asCallHierarchyItem(e)};return n.sendRequest(r.CallHierarchyOutgoingCallsRequest.type,i,t).then(e=>t.isCancellationRequested?null:n.protocol2CodeConverter.asCallHierarchyOutgoingCalls(e,t),e=>n.handleFailedRequest(r.CallHierarchyOutgoingCallsRequest.type,t,e,null))};return i.provideCallHierarchyOutgoingCalls?i.provideCallHierarchyOutgoingCalls(e,t,o):o(e,t)}}class a extends o.TextDocumentLanguageFeature{constructor(e){super(e,r.CallHierarchyPrepareRequest.type)}fillClientCapabilities(e){const t=e;(0,o.ensure)((0,o.ensure)(t,"textDocument"),"callHierarchy").dynamicRegistration=!0}initialize(e,t){const[n,i]=this.getRegistration(t,e.callHierarchyProvider);n&&i&&this.register({id:n,registerOptions:i})}registerLanguageProvider(e){const t=this._client,n=new s(t);return[i.languages.registerCallHierarchyProvider(this._client.protocol2CodeConverter.asDocumentSelector(e.documentSelector),n),n]}}t.CallHierarchyFeature=a},8395:(e,t,n)=>{class i{constructor(e,t){if(t=o(t),e instanceof i)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new i(e.raw,t);if(e instanceof s)return this.raw=e.value,this.set=[[e]],this.format(),this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const e=this.set[0];if(this.set=this.set.filter(e=>!m(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1)for(const e of this.set)if(1===e.length&&v(e[0])){this.set=[e];break}}this.format()}format(){return this.range=this.set.map(e=>e.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange(e){const t=((this.options.includePrerelease&&f)|(this.options.loose&&g))+":"+e,n=r.get(t);if(n)return n;const i=this.options.loose,o=i?u[l.HYPHENRANGELOOSE]:u[l.HYPHENRANGE];e=e.replace(o,k(this.options.includePrerelease)),a("hyphen replace",e),e=e.replace(u[l.COMPARATORTRIM],d),a("comparator trim",e),e=e.replace(u[l.TILDETRIM],h),a("tilde trim",e),e=e.replace(u[l.CARETTRIM],p),a("caret trim",e);let c=e.split(" ").map(e=>b(e,this.options)).join(" ").split(/\s+/).map(e=>O(e,this.options));i&&(c=c.filter(e=>(a("loose invalid filter",e,this.options),!!e.match(u[l.COMPARATORLOOSE])))),a("range list",c);const v=new Map,y=c.map(e=>new s(e,this.options));for(const e of y){if(m(e))return[e];v.set(e.value,e)}v.size>1&&v.has("")&&v.delete("");const _=[...v.values()];return r.set(t,_),_}intersects(e,t){if(!(e instanceof i))throw new TypeError("a Range is required");return this.set.some(n=>y(n,t)&&e.set.some(e=>y(e,t)&&n.every(n=>e.every(e=>n.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(x(this.set[t],e,this.options))return!0;return!1}}e.exports=i;const r=new(n(5689))({max:1e3}),o=n(7095),s=n(9524),a=n(9796),c=n(7944),{safeRe:u,t:l,comparatorTrimReplace:d,tildeTrimReplace:h,caretTrimReplace:p}=n(6850),{FLAG_INCLUDE_PRERELEASE:f,FLAG_LOOSE:g}=n(9630),m=e=>"<0.0.0-0"===e.value,v=e=>""===e.value,y=(e,t)=>{let n=!0;const i=e.slice();let r=i.pop();for(;n&&i.length;)n=i.every(e=>r.intersects(e,t)),r=i.pop();return n},b=(e,t)=>(a("comp",e,t),e=S(e,t),a("caret",e),e=C(e,t),a("tildes",e),e=R(e,t),a("xrange",e),e=P(e,t),a("stars",e),e),_=e=>!e||"x"===e.toLowerCase()||"*"===e,C=(e,t)=>e.trim().split(/\s+/).map(e=>D(e,t)).join(" "),D=(e,t)=>{const n=t.loose?u[l.TILDELOOSE]:u[l.TILDE];return e.replace(n,(t,n,i,r,o)=>{let s;return a("tilde",e,t,n,i,r,o),_(n)?s="":_(i)?s=`>=${n}.0.0 <${+n+1}.0.0-0`:_(r)?s=`>=${n}.${i}.0 <${n}.${+i+1}.0-0`:o?(a("replaceTilde pr",o),s=`>=${n}.${i}.${r}-${o} <${n}.${+i+1}.0-0`):s=`>=${n}.${i}.${r} <${n}.${+i+1}.0-0`,a("tilde return",s),s})},S=(e,t)=>e.trim().split(/\s+/).map(e=>w(e,t)).join(" "),w=(e,t)=>{a("caret",e,t);const n=t.loose?u[l.CARETLOOSE]:u[l.CARET],i=t.includePrerelease?"-0":"";return e.replace(n,(t,n,r,o,s)=>{let c;return a("caret",e,t,n,r,o,s),_(n)?c="":_(r)?c=`>=${n}.0.0${i} <${+n+1}.0.0-0`:_(o)?c="0"===n?`>=${n}.${r}.0${i} <${n}.${+r+1}.0-0`:`>=${n}.${r}.0${i} <${+n+1}.0.0-0`:s?(a("replaceCaret pr",s),c="0"===n?"0"===r?`>=${n}.${r}.${o}-${s} <${n}.${r}.${+o+1}-0`:`>=${n}.${r}.${o}-${s} <${n}.${+r+1}.0-0`:`>=${n}.${r}.${o}-${s} <${+n+1}.0.0-0`):(a("no pr"),c="0"===n?"0"===r?`>=${n}.${r}.${o}${i} <${n}.${r}.${+o+1}-0`:`>=${n}.${r}.${o}${i} <${n}.${+r+1}.0-0`:`>=${n}.${r}.${o} <${+n+1}.0.0-0`),a("caret return",c),c})},R=(e,t)=>(a("replaceXRanges",e,t),e.split(/\s+/).map(e=>T(e,t)).join(" ")),T=(e,t)=>{e=e.trim();const n=t.loose?u[l.XRANGELOOSE]:u[l.XRANGE];return e.replace(n,(n,i,r,o,s,c)=>{a("xRange",e,n,i,r,o,s,c);const u=_(r),l=u||_(o),d=l||_(s),h=d;return"="===i&&h&&(i=""),c=t.includePrerelease?"-0":"",u?n=">"===i||"<"===i?"<0.0.0-0":"*":i&&h?(l&&(o=0),s=0,">"===i?(i=">=",l?(r=+r+1,o=0,s=0):(o=+o+1,s=0)):"<="===i&&(i="<",l?r=+r+1:o=+o+1),"<"===i&&(c="-0"),n=`${i+r}.${o}.${s}${c}`):l?n=`>=${r}.0.0${c} <${+r+1}.0.0-0`:d&&(n=`>=${r}.${o}.0${c} <${r}.${+o+1}.0-0`),a("xRange return",n),n})},P=(e,t)=>(a("replaceStars",e,t),e.trim().replace(u[l.STAR],"")),O=(e,t)=>(a("replaceGTE0",e,t),e.trim().replace(u[t.includePrerelease?l.GTE0PRE:l.GTE0],"")),k=e=>(t,n,i,r,o,s,a,c,u,l,d,h,p)=>`${n=_(i)?"":_(r)?`>=${i}.0.0${e?"-0":""}`:_(o)?`>=${i}.${r}.0${e?"-0":""}`:s?`>=${n}`:`>=${n}${e?"-0":""}`} ${c=_(u)?"":_(l)?`<${+u+1}.0.0-0`:_(d)?`<${u}.${+l+1}.0-0`:h?`<=${u}.${l}.${d}-${h}`:e?`<${u}.${l}.${+d+1}-0`:`<=${c}`}`.trim(),x=(e,t,n)=>{for(let n=0;n<e.length;n++)if(!e[n].test(t))return!1;if(t.prerelease.length&&!n.includePrerelease){for(let n=0;n<e.length;n++)if(a(e[n].semver),e[n].semver!==s.ANY&&e[n].semver.prerelease.length>0){const i=e[n].semver;if(i.major===t.major&&i.minor===t.minor&&i.patch===t.patch)return!0}return!1}return!0}},8509:e=>{"use strict";function t(e,t,r){e instanceof RegExp&&(e=n(e,r)),t instanceof RegExp&&(t=n(t,r));var o=i(e,t,r);return o&&{start:o[0],end:o[1],pre:r.slice(0,o[0]),body:r.slice(o[0]+e.length,o[1]),post:r.slice(o[1]+t.length)}}function n(e,t){var n=t.match(e);return n?n[0]:null}function i(e,t,n){var i,r,o,s,a,c=n.indexOf(e),u=n.indexOf(t,c+1),l=c;if(c>=0&&u>0){if(e===t)return[c,u];for(i=[],o=n.length;l>=0&&!a;)l==c?(i.push(l),c=n.indexOf(e,l+1)):1==i.length?a=[i.pop(),u]:((r=i.pop())<o&&(o=r,s=u),u=n.indexOf(t,l+1)),l=c<u&&c>=0?c:u;i.length&&(a=[o,s])}return a}e.exports=t,t.range=i},8575:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlineCompletionRequest=void 0;const i=n(7096);var r;!function(e){e.method="textDocument/inlineCompletion",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.InlineCompletionRequest=r={}))},8820:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.generateUuid=t.parse=t.isUUID=t.v4=t.empty=void 0;class n{_value;constructor(e){this._value=e}asHex(){return this._value}equals(e){return this.asHex()===e.asHex()}}class i extends n{static _chars=["0","1","2","3","4","5","6","6","7","8","9","a","b","c","d","e","f"];static _timeHighBits=["8","9","a","b"];static _oneOf(e){return e[Math.floor(e.length*Math.random())]}static _randomHex(){return i._oneOf(i._chars)}constructor(){super([i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),"-",i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),"-","4",i._randomHex(),i._randomHex(),i._randomHex(),"-",i._oneOf(i._timeHighBits),i._randomHex(),i._randomHex(),i._randomHex(),"-",i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex()].join(""))}}function r(){return new i}t.empty=new n("00000000-0000-0000-0000-000000000000"),t.v4=r;const o=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;function s(e){return o.test(e)}t.isUUID=s,t.parse=function(e){if(!s(e))throw new Error("invalid uuid");return new n(e)},t.generateUuid=function(){return r().asHex()}},8958:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FoldingRangeRefreshRequest=t.FoldingRangeRequest=void 0;const i=n(7096);var r,o;!function(e){e.method="textDocument/foldingRange",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.FoldingRangeRequest=r={})),function(e){e.method="workspace/foldingRange/refresh",e.messageDirection=i.MessageDirection.serverToClient,e.type=new i.ProtocolRequestType0(e.method)}(o||(t.FoldingRangeRefreshRequest=o={}))},9023:e=>{"use strict";e.exports=require("util")},9042:(e,t)=>{"use strict";let n;function i(){if(void 0===n)throw new Error("No runtime abstraction layer installed");return n}Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.install=function(e){if(void 0===e)throw new Error("No runtime abstraction layer provided");n=e}}(i||(i={})),t.default=i},9248:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ShowDocumentRequest=void 0;const i=n(7096);var r;!function(e){e.method="window/showDocument",e.messageDirection=i.MessageDirection.serverToClient,e.type=new i.ProtocolRequestType(e.method)}(r||(t.ShowDocumentRequest=r={}))},9278:e=>{"use strict";e.exports=require("net")},9439:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t},s=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.SettingMonitor=t.LanguageClient=t.TransportKind=void 0;const a=o(n(5317)),c=o(n(9896)),u=o(n(6928)),l=n(1398),d=o(n(1027)),h=n(710),p=n(1141),f=n(3286),g=n(5860),m=n(1722);s(n(3286),t),s(n(2685),t);const v="^1.91.0";var y,b,_,C,D,S;!function(e){e[e.stdio=0]="stdio",e[e.ipc=1]="ipc",e[e.pipe=2]="pipe",e[e.socket=3]="socket"}(y||(t.TransportKind=y={})),function(e){e.isSocket=function(e){const t=e;return t&&t.kind===y.socket&&d.number(t.port)}}(b||(b={})),function(e){e.is=function(e){return d.string(e.command)}}(_||(_={})),function(e){e.is=function(e){return d.string(e.module)}}(C||(C={})),function(e){e.is=function(e){const t=e;return t&&void 0!==t.writer&&void 0!==t.reader}}(D||(D={})),function(e){e.is=function(e){const t=e;return t&&void 0!==t.process&&"boolean"==typeof t.detached}}(S||(S={}));class w extends h.BaseLanguageClient{_serverOptions;_forceDebug;_serverProcess;_isDetached;_isInDebugMode;constructor(e,t,n,i,r){let o,s,a,c,u;d.string(t)?(o=e,s=t,a=n,c=i,u=!!r):(o=e.toLowerCase(),s=e,a=t,c=n,u=i),void 0===u&&(u=!1),super(o,s,c),this._serverOptions=a,this._forceDebug=u,this._isInDebugMode=u;try{this.checkVersion()}catch(e){throw d.string(e.message)&&this.outputChannel.appendLine(e.message),e}}checkVersion(){const e=g(l.version);if(!e)throw new Error(`No valid VS Code version detected. Version string is: ${l.version}`);if(e.prerelease&&e.prerelease.length>0&&(e.prerelease=[]),!m(e,v))throw new Error(`The language client requires VS Code version ${v} but received version ${l.version}`)}get isInDebugMode(){return this._isInDebugMode}async restart(){await this.stop(),this.isInDebugMode?(await new Promise(e=>setTimeout(e,1e3)),await this.start()):await this.start()}shutdown(e,t=2e3){return super.shutdown(e,t).finally(()=>{if(this._serverProcess){const e=this._serverProcess;this._serverProcess=void 0,void 0!==this._isDetached&&this._isDetached||this.checkProcessDied(e),this._isDetached=void 0}})}checkProcessDied(e){e&&void 0!==e.pid&&setTimeout(()=>{try{void 0!==e.pid&&(process.kill(e.pid,0),(0,p.terminate)(e))}catch(e){}},2e3)}handleConnectionClosed(){return this._serverProcess=void 0,super.handleConnectionClosed()}fillInitializeParams(e){super.fillInitializeParams(e),null===e.processId&&(e.processId=process.pid)}createMessageTransports(e){function t(e,t){if(!e&&!t)return;const n=Object.create(null);return Object.keys(process.env).forEach(e=>n[e]=process.env[e]),t&&(n.ELECTRON_RUN_AS_NODE="1",n.ELECTRON_NO_ASAR="1"),e&&Object.keys(e).forEach(t=>n[t]=e[t]),n}const n=["--debug=","--debug-brk=","--inspect=","--inspect-brk="],i=["--debug","--debug-brk","--inspect","--inspect-brk"];function r(e){if(null===e.stdin||null===e.stdout||null===e.stderr)throw new Error("Process created without stdio streams")}const o=this._serverOptions;if(d.func(o))return o().then(t=>{if(h.MessageTransports.is(t))return this._isDetached=!!t.detached,t;if(D.is(t))return this._isDetached=!!t.detached,{reader:new f.StreamMessageReader(t.reader),writer:new f.StreamMessageWriter(t.writer)};{let n;return S.is(t)?(n=t.process,this._isDetached=t.detached):(n=t,this._isDetached=!1),n.stderr.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),{reader:new f.StreamMessageReader(n.stdout),writer:new f.StreamMessageWriter(n.stdin)}}});let s;const c=o;return c.run||c.debug?this._forceDebug||function(){const e=process.execArgv;return!!e&&e.some(e=>n.some(t=>e.startsWith(t))||i.some(t=>e===t))}()?(s=c.debug,this._isInDebugMode=!0):(s=c.run,this._isInDebugMode=!1):s=o,this._getServerWorkingDir(s.options).then(n=>{if(C.is(s)&&s.module){const i=s,o=i.transport||y.stdio;if(!i.runtime){let s;return new Promise((c,u)=>{const l=(i.args&&i.args.slice())??[];o===y.ipc?l.push("--node-ipc"):o===y.stdio?l.push("--stdio"):o===y.pipe?(s=(0,f.generateRandomPipeName)(),l.push(`--pipe=${s}`)):b.isSocket(o)&&l.push(`--socket=${o.port}`),l.push(`--clientProcessId=${process.pid.toString()}`);const h=i.options??Object.create(null);if(h.env=t(h.env,!0),h.execArgv=h.execArgv||[],h.cwd=n,h.silent=!0,o===y.ipc||o===y.stdio){const t=a.fork(i.module,l||[],h);r(t),this._serverProcess=t,t.stderr.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),o===y.ipc?(t.stdout.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),c({reader:new f.IPCMessageReader(this._serverProcess),writer:new f.IPCMessageWriter(this._serverProcess)})):c({reader:new f.StreamMessageReader(t.stdout),writer:new f.StreamMessageWriter(t.stdin)})}else o===y.pipe?(0,f.createClientPipeTransport)(s).then(t=>{const n=a.fork(i.module,l||[],h);r(n),this._serverProcess=n,n.stderr.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),n.stdout.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),t.onConnected().then(e=>{c({reader:e[0],writer:e[1]})},u)},u):b.isSocket(o)&&(0,f.createClientSocketTransport)(o.port).then(t=>{const n=a.fork(i.module,l||[],h);r(n),this._serverProcess=n,n.stderr.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),n.stdout.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),t.onConnected().then(e=>{c({reader:e[0],writer:e[1]})},u)},u)})}{const r=[],s=i.options??Object.create(null);s.execArgv&&s.execArgv.forEach(e=>r.push(e)),r.push(i.module),i.args&&i.args.forEach(e=>r.push(e));const c=Object.create(null);c.cwd=n,c.env=t(s.env,!1);const u=this._getRuntimePath(i.runtime,n);let l;if(o===y.ipc?(c.stdio=[null,null,null,"ipc"],r.push("--node-ipc")):o===y.stdio?r.push("--stdio"):o===y.pipe?(l=(0,f.generateRandomPipeName)(),r.push(`--pipe=${l}`)):b.isSocket(o)&&r.push(`--socket=${o.port}`),r.push(`--clientProcessId=${process.pid.toString()}`),o===y.ipc||o===y.stdio){const t=a.spawn(u,r,c);return t&&t.pid?(this._serverProcess=t,t.stderr.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),o===y.ipc?(t.stdout.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),Promise.resolve({reader:new f.IPCMessageReader(t),writer:new f.IPCMessageWriter(t)})):Promise.resolve({reader:new f.StreamMessageReader(t.stdout),writer:new f.StreamMessageWriter(t.stdin)})):R(t,`Launching server using runtime ${u} failed.`)}if(o===y.pipe)return(0,f.createClientPipeTransport)(l).then(t=>{const n=a.spawn(u,r,c);return n&&n.pid?(this._serverProcess=n,n.stderr.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),n.stdout.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),t.onConnected().then(e=>({reader:e[0],writer:e[1]}))):R(n,`Launching server using runtime ${u} failed.`)});if(b.isSocket(o))return(0,f.createClientSocketTransport)(o.port).then(t=>{const n=a.spawn(u,r,c);return n&&n.pid?(this._serverProcess=n,n.stderr.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),n.stdout.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),t.onConnected().then(e=>({reader:e[0],writer:e[1]}))):R(n,`Launching server using runtime ${u} failed.`)})}}else if(_.is(s)&&s.command){const t=s,i=void 0!==s.args?s.args.slice(0):[];let r;const o=s.transport;if(o===y.stdio)i.push("--stdio");else if(o===y.pipe)r=(0,f.generateRandomPipeName)(),i.push(`--pipe=${r}`);else if(b.isSocket(o))i.push(`--socket=${o.port}`);else if(o===y.ipc)throw new Error("Transport kind ipc is not support for command executable");const c=Object.assign({},t.options);if(c.cwd=c.cwd||n,void 0===o||o===y.stdio){const n=a.spawn(t.command,i,c);return n&&n.pid?(n.stderr.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),this._serverProcess=n,this._isDetached=!!c.detached,Promise.resolve({reader:new f.StreamMessageReader(n.stdout),writer:new f.StreamMessageWriter(n.stdin)})):R(n,`Launching server using command ${t.command} failed.`)}if(o===y.pipe)return(0,f.createClientPipeTransport)(r).then(n=>{const r=a.spawn(t.command,i,c);return r&&r.pid?(this._serverProcess=r,this._isDetached=!!c.detached,r.stderr.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),r.stdout.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),n.onConnected().then(e=>({reader:e[0],writer:e[1]}))):R(r,`Launching server using command ${t.command} failed.`)});if(b.isSocket(o))return(0,f.createClientSocketTransport)(o.port).then(n=>{const r=a.spawn(t.command,i,c);return r&&r.pid?(this._serverProcess=r,this._isDetached=!!c.detached,r.stderr.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),r.stdout.on("data",t=>this.outputChannel.append(d.string(t)?t:t.toString(e))),n.onConnected().then(e=>({reader:e[0],writer:e[1]}))):R(r,`Launching server using command ${t.command} failed.`)})}return Promise.reject(new Error("Unsupported server configuration "+JSON.stringify(o,null,4)))}).finally(()=>{void 0!==this._serverProcess&&this._serverProcess.on("exit",(e,t)=>{0===e?this.info("Server process exited successfully",void 0,!1):null!==e&&this.error(`Server process exited with code ${e}.`,void 0,!1),null!==t&&this.error(`Server process exited with signal ${t}.`,void 0,!1)})})}_getRuntimePath(e,t){if(u.isAbsolute(e))return e;const n=this._mainGetRootPath();if(void 0!==n){const t=u.join(n,e);if(c.existsSync(t))return t}if(void 0!==t){const n=u.join(t,e);if(c.existsSync(n))return n}return e}_mainGetRootPath(){const e=l.workspace.workspaceFolders;if(!e||0===e.length)return;const t=e[0];return"file"===t.uri.scheme?t.uri.fsPath:void 0}_getServerWorkingDir(e){let t=e&&e.cwd;return t||(t=this.clientOptions.workspaceFolder?this.clientOptions.workspaceFolder.uri.fsPath:this._mainGetRootPath()),t?new Promise(e=>{c.lstat(t,(n,i)=>{e(!n&&i.isDirectory()?t:void 0)})}):Promise.resolve(void 0)}}function R(e,t){return null===e?Promise.reject(t):new Promise((n,i)=>{e.on("error",e=>{i(`${t} ${e}`)}),setImmediate(()=>i(t))})}t.LanguageClient=w,t.SettingMonitor=class{_client;_setting;_listeners;constructor(e,t){this._client=e,this._setting=t,this._listeners=[]}start(){return l.workspace.onDidChangeConfiguration(this.onDidChangeConfiguration,this,this._listeners),this.onDidChangeConfiguration(),new l.Disposable(()=>{this._client.needsStop()&&this._client.stop()})}onDidChangeConfiguration(){const e=this._setting.indexOf("."),t=e>=0?this._setting.substr(0,e):this._setting,n=e>=0?this._setting.substr(e+1):void 0,i=n?l.workspace.getConfiguration(t).get(n,!1):l.workspace.getConfiguration(t);i&&this._client.needsStart()?this._client.start().catch(e=>this._client.error("Start failed after configuration change",e,"force")):!i&&this._client.needsStop()&&this._client.stop().catch(e=>this._client.error("Stop failed after configuration change",e,"force"))}}},9488:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ConfigurationRequest=void 0;const i=n(7096);var r;!function(e){e.method="workspace/configuration",e.messageDirection=i.MessageDirection.serverToClient,e.type=new i.ProtocolRequestType(e.method)}(r||(t.ConfigurationRequest=r={}))},9504:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.DidSaveTextDocumentFeature=t.WillSaveWaitUntilFeature=t.WillSaveFeature=t.DidChangeTextDocumentFeature=t.DidCloseTextDocumentFeature=t.DidOpenTextDocumentFeature=void 0;const s=n(1398),a=n(3434),c=n(9810),u=o(n(8820));class l extends c.TextDocumentEventFeature{_syncedDocuments;_pendingOpenNotifications;_delayOpen;_pendingOpenListeners;constructor(e,t){super(e,s.workspace.onDidOpenTextDocument,a.DidOpenTextDocumentNotification.type,()=>e.middleware.didOpen,t=>e.code2ProtocolConverter.asOpenTextDocumentParams(t),e=>e,c.TextDocumentEventFeature.textDocumentFilter),this._syncedDocuments=t,this._pendingOpenNotifications=new Map,this._delayOpen=e.clientOptions.textSynchronization?.delayOpenNotifications??!1}async callback(e){return this._delayOpen?this.matches(e)?this._client.tabsModel.isVisible(e)?super.callback(e):void this._pendingOpenNotifications.set(e.uri.toString(),e):void 0:super.callback(e)}get openDocuments(){return this._syncedDocuments.values()}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"textDocument"),"synchronization").dynamicRegistration=!0}initialize(e,t){const n=e.resolvedTextDocumentSync;t&&n&&n.openClose&&this.register({id:u.generateUuid(),registerOptions:{documentSelector:t}})}get registrationType(){return a.DidOpenTextDocumentNotification.type}register(e){if(super.register(e),!e.registerOptions.documentSelector)return;const t=this._client.protocol2CodeConverter.asDocumentSelector(e.registerOptions.documentSelector);if(s.workspace.textDocuments.forEach(e=>{const n=e.uri.toString();if(!this._syncedDocuments.has(n)&&s.languages.match(t,e)>0&&!this._client.hasDedicatedTextSynchronizationFeature(e))if(this._client.tabsModel.isVisible(e)){const t=this._client.middleware,i=e=>this._client.sendNotification(this._type,this._createParams(e));(t.didOpen?t.didOpen(e,i):i(e)).catch(e=>{this._client.error(`Sending document notification ${this._type.method} failed`,e)}),this._syncedDocuments.set(n,e)}else this._pendingOpenNotifications.set(n,e)}),this._delayOpen&&void 0===this._pendingOpenListeners){this._pendingOpenListeners=[];const e=this._client.tabsModel;this._pendingOpenListeners.push(e.onClose(e=>{for(const t of e)this._pendingOpenNotifications.delete(t.toString())})),this._pendingOpenListeners.push(e.onOpen(e=>{for(const t of e){const e=this._pendingOpenNotifications.get(t.toString());void 0!==e&&(super.callback(e).catch(e=>{this._client.error(`Sending document notification ${this._type.method} failed`,e)}),this._pendingOpenNotifications.delete(t.toString()))}})),this._pendingOpenListeners.push(s.workspace.onDidCloseTextDocument(e=>{this._pendingOpenNotifications.delete(e.uri.toString())}))}}async sendPendingOpenNotifications(e){const t=Array.from(this._pendingOpenNotifications.values());this._pendingOpenNotifications.clear();for(const n of t)void 0!==e&&n.uri.toString()===e||await super.callback(n)}getTextDocument(e){return e}notificationSent(e,t,n){this._syncedDocuments.set(e.uri.toString(),e),super.notificationSent(e,t,n)}clear(){if(this._pendingOpenNotifications.clear(),void 0!==this._pendingOpenListeners){for(const e of this._pendingOpenListeners)e.dispose();this._pendingOpenListeners=void 0}super.clear()}}t.DidOpenTextDocumentFeature=l;class d extends c.TextDocumentEventFeature{_syncedDocuments;_pendingTextDocumentChanges;constructor(e,t,n){super(e,s.workspace.onDidCloseTextDocument,a.DidCloseTextDocumentNotification.type,()=>e.middleware.didClose,t=>e.code2ProtocolConverter.asCloseTextDocumentParams(t),e=>e,c.TextDocumentEventFeature.textDocumentFilter),this._syncedDocuments=t,this._pendingTextDocumentChanges=n}get registrationType(){return a.DidCloseTextDocumentNotification.type}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"textDocument"),"synchronization").dynamicRegistration=!0}initialize(e,t){const n=e.resolvedTextDocumentSync;t&&n&&n.openClose&&this.register({id:u.generateUuid(),registerOptions:{documentSelector:t}})}async callback(e){await super.callback(e),this._pendingTextDocumentChanges.delete(e.uri.toString())}getTextDocument(e){return e}notificationSent(e,t,n){this._syncedDocuments.delete(e.uri.toString()),super.notificationSent(e,t,n)}unregister(e){const t=this._selectors.get(e);if(void 0===t)return;super.unregister(e);const n=this._selectors.values();this._syncedDocuments.forEach(e=>{if(s.languages.match(t,e)>0&&!this._selectorFilter(n,e)&&!this._client.hasDedicatedTextSynchronizationFeature(e)){const t=this._client.middleware,n=e=>this._client.sendNotification(this._type,this._createParams(e));this._syncedDocuments.delete(e.uri.toString()),(t.didClose?t.didClose(e,n):n(e)).catch(e=>{this._client.error(`Sending document notification ${this._type.method} failed`,e)})}})}}t.DidCloseTextDocumentFeature=d;class h extends c.DynamicDocumentFeature{_listener;_changeData;_onNotificationSent;_onPendingChangeAdded;_pendingTextDocumentChanges;_syncKind;constructor(e,t){super(e),this._changeData=new Map,this._onNotificationSent=new s.EventEmitter,this._onPendingChangeAdded=new s.EventEmitter,this._pendingTextDocumentChanges=t,this._syncKind=a.TextDocumentSyncKind.None}get onNotificationSent(){return this._onNotificationSent.event}get onPendingChangeAdded(){return this._onPendingChangeAdded.event}get syncKind(){return this._syncKind}get registrationType(){return a.DidChangeTextDocumentNotification.type}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"textDocument"),"synchronization").dynamicRegistration=!0}initialize(e,t){const n=e.resolvedTextDocumentSync;t&&n&&void 0!==n.change&&n.change!==a.TextDocumentSyncKind.None&&this.register({id:u.generateUuid(),registerOptions:Object.assign({},{documentSelector:t},{syncKind:n.change})})}register(e){e.registerOptions.documentSelector&&(this._listener||(this._listener=s.workspace.onDidChangeTextDocument(this.callback,this)),this._changeData.set(e.id,{syncKind:e.registerOptions.syncKind,documentSelector:this._client.protocol2CodeConverter.asDocumentSelector(e.registerOptions.documentSelector)}),this.updateSyncKind(e.registerOptions.syncKind))}*getDocumentSelectors(){for(const e of this._changeData.values())yield e.documentSelector}async callback(e){if(0===e.contentChanges.length)return;const t=e.document.uri,n=e.document.version,i=[];for(const r of this._changeData.values())if(s.languages.match(r.documentSelector,e.document)>0&&!this._client.hasDedicatedTextSynchronizationFeature(e.document)){const o=this._client.middleware;if(r.syncKind===a.TextDocumentSyncKind.Incremental){const r=async e=>{const i=this._client.code2ProtocolConverter.asChangeTextDocumentParams(e,t,n);await this._client.sendNotification(a.DidChangeTextDocumentNotification.type,i),this.notificationSent(e.document,a.DidChangeTextDocumentNotification.type,i)};i.push(o.didChange?o.didChange(e,e=>r(e)):r(e))}else if(r.syncKind===a.TextDocumentSyncKind.Full){const t=async e=>{const t=e.document.uri.toString();this._pendingTextDocumentChanges.set(t,e.document),this._onPendingChangeAdded.fire()};i.push(o.didChange?o.didChange(e,e=>t(e)):t(e))}}return Promise.all(i).then(void 0,e=>{throw this._client.error(`Sending document notification ${a.DidChangeTextDocumentNotification.type.method} failed`,e),e})}notificationSent(e,t,n){this._onNotificationSent.fire({textDocument:e,type:t,params:n})}unregister(e){if(this._changeData.delete(e),0===this._changeData.size)this._listener&&(this._listener.dispose(),this._listener=void 0),this._syncKind=a.TextDocumentSyncKind.None;else{this._syncKind=a.TextDocumentSyncKind.None;for(const e of this._changeData.values())if(this.updateSyncKind(e.syncKind),this._syncKind===a.TextDocumentSyncKind.Full)break}}clear(){this._pendingTextDocumentChanges.clear(),this._changeData.clear(),this._syncKind=a.TextDocumentSyncKind.None,this._listener&&(this._listener.dispose(),this._listener=void 0)}getPendingDocumentChanges(e){if(0===this._pendingTextDocumentChanges.size)return[];let t;if(0===e.size)t=Array.from(this._pendingTextDocumentChanges.values()),this._pendingTextDocumentChanges.clear();else{t=[];for(const n of this._pendingTextDocumentChanges)e.has(n[0])||(t.push(n[1]),this._pendingTextDocumentChanges.delete(n[0]))}return t}getProvider(e){for(const t of this._changeData.values())if(s.languages.match(t.documentSelector,e)>0)return{send:e=>this.callback(e)}}updateSyncKind(e){if(this._syncKind!==a.TextDocumentSyncKind.Full)switch(e){case a.TextDocumentSyncKind.Full:this._syncKind=e;break;case a.TextDocumentSyncKind.Incremental:this._syncKind===a.TextDocumentSyncKind.None&&(this._syncKind=a.TextDocumentSyncKind.Incremental)}}}t.DidChangeTextDocumentFeature=h;class p extends c.TextDocumentEventFeature{constructor(e){super(e,s.workspace.onWillSaveTextDocument,a.WillSaveTextDocumentNotification.type,()=>e.middleware.willSave,t=>e.code2ProtocolConverter.asWillSaveTextDocumentParams(t),e=>e.document,(e,t)=>c.TextDocumentEventFeature.textDocumentFilter(e,t.document))}get registrationType(){return a.WillSaveTextDocumentNotification.type}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"textDocument"),"synchronization").willSave=!0}initialize(e,t){const n=e.resolvedTextDocumentSync;t&&n&&n.willSave&&this.register({id:u.generateUuid(),registerOptions:{documentSelector:t}})}getTextDocument(e){return e.document}}t.WillSaveFeature=p;class f extends c.DynamicDocumentFeature{_listener;_selectors;constructor(e){super(e),this._selectors=new Map}getDocumentSelectors(){return this._selectors.values()}get registrationType(){return a.WillSaveTextDocumentWaitUntilRequest.type}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"textDocument"),"synchronization").willSaveWaitUntil=!0}initialize(e,t){const n=e.resolvedTextDocumentSync;t&&n&&n.willSaveWaitUntil&&this.register({id:u.generateUuid(),registerOptions:{documentSelector:t}})}register(e){e.registerOptions.documentSelector&&(this._listener||(this._listener=s.workspace.onWillSaveTextDocument(this.callback,this)),this._selectors.set(e.id,this._client.protocol2CodeConverter.asDocumentSelector(e.registerOptions.documentSelector)))}callback(e){if(c.TextDocumentEventFeature.textDocumentFilter(this._selectors.values(),e.document)&&!this._client.hasDedicatedTextSynchronizationFeature(e.document)){const t=this._client.middleware,n=e=>this._client.sendRequest(a.WillSaveTextDocumentWaitUntilRequest.type,this._client.code2ProtocolConverter.asWillSaveTextDocumentParams(e)).then(async e=>{const t=await this._client.protocol2CodeConverter.asTextEdits(e);return void 0===t?[]:t});e.waitUntil(t.willSaveWaitUntil?t.willSaveWaitUntil(e,n):n(e))}}unregister(e){this._selectors.delete(e),0===this._selectors.size&&this._listener&&(this._listener.dispose(),this._listener=void 0)}clear(){this._selectors.clear(),this._listener&&(this._listener.dispose(),this._listener=void 0)}}t.WillSaveWaitUntilFeature=f;class g extends c.TextDocumentEventFeature{_includeText;constructor(e){super(e,s.workspace.onDidSaveTextDocument,a.DidSaveTextDocumentNotification.type,()=>e.middleware.didSave,t=>e.code2ProtocolConverter.asSaveTextDocumentParams(t,this._includeText),e=>e,c.TextDocumentEventFeature.textDocumentFilter),this._includeText=!1}get registrationType(){return a.DidSaveTextDocumentNotification.type}fillClientCapabilities(e){(0,c.ensure)((0,c.ensure)(e,"textDocument"),"synchronization").didSave=!0}initialize(e,t){const n=e.resolvedTextDocumentSync;if(t&&n&&n.save){const e="boolean"==typeof n.save?{includeText:!1}:{includeText:!!n.save.includeText};this.register({id:u.generateUuid(),registerOptions:Object.assign({},{documentSelector:t},e)})}}register(e){this._includeText=!!e.registerOptions.includeText,super.register(e)}getTextDocument(e){return e}}t.DidSaveTextDocumentFeature=g},9524:(e,t,n)=>{const i=Symbol("SemVer ANY");class r{static get ANY(){return i}constructor(e,t){if(t=o(t),e instanceof r){if(e.loose===!!t.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),u("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===i?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(e){const t=this.options.loose?s[a.COMPARATORLOOSE]:s[a.COMPARATOR],n=e.match(t);if(!n)throw new TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==n[1]?n[1]:"","="===this.operator&&(this.operator=""),n[2]?this.semver=new l(n[2],this.options.loose):this.semver=i}toString(){return this.value}test(e){if(u("Comparator.test",e,this.options.loose),this.semver===i||e===i)return!0;if("string"==typeof e)try{e=new l(e,this.options)}catch(e){return!1}return c(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof r))throw new TypeError("a Comparator is required");return""===this.operator?""===this.value||new d(e.value,t).test(this.value):""===e.operator?""===e.value||new d(this.value,t).test(e.semver):!((t=o(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))||(!this.operator.startsWith(">")||!e.operator.startsWith(">"))&&(!this.operator.startsWith("<")||!e.operator.startsWith("<"))&&(this.semver.version!==e.semver.version||!this.operator.includes("=")||!e.operator.includes("="))&&!(c(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<"))&&!(c(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))}}e.exports=r;const o=n(7095),{safeRe:s,t:a}=n(6850),c=n(3811),u=n(9796),l=n(7944),d=n(8395)},9624:(e,t,n)=>{"use strict";n.r(t),n.d(t,{AST:()=>_,GLOBSTAR:()=>z,Minimatch:()=>Z,braceExpand:()=>X,defaults:()=>G,escape:()=>C,filter:()=>B,makeRe:()=>J,match:()=>Q,minimatch:()=>D,sep:()=>K,unescape:()=>u});var i=n(2164);const r=e=>{if("string"!=typeof e)throw new TypeError("invalid pattern");if(e.length>65536)throw new TypeError("pattern is too long")},o={"[:alnum:]":["\\p{L}\\p{Nl}\\p{Nd}",!0],"[:alpha:]":["\\p{L}\\p{Nl}",!0],"[:ascii:]":["\\x00-\\x7f",!1],"[:blank:]":["\\p{Zs}\\t",!0],"[:cntrl:]":["\\p{Cc}",!0],"[:digit:]":["\\p{Nd}",!0],"[:graph:]":["\\p{Z}\\p{C}",!0,!0],"[:lower:]":["\\p{Ll}",!0],"[:print:]":["\\p{C}",!0],"[:punct:]":["\\p{P}",!0],"[:space:]":["\\p{Z}\\t\\r\\n\\v\\f",!0],"[:upper:]":["\\p{Lu}",!0],"[:word:]":["\\p{L}\\p{Nl}\\p{Nd}\\p{Pc}",!0],"[:xdigit:]":["A-Fa-f0-9",!1]},s=e=>e.replace(/[[\]\\-]/g,"\\$&"),a=e=>e.join(""),c=(e,t)=>{const n=t;if("["!==e.charAt(n))throw new Error("not in a brace expression");const i=[],r=[];let c=n+1,u=!1,l=!1,d=!1,h=!1,p=n,f="";e:for(;c<e.length;){const t=e.charAt(c);if("!"!==t&&"^"!==t||c!==n+1){if("]"===t&&u&&!d){p=c+1;break}if(u=!0,"\\"!==t||d){if("["===t&&!d)for(const[t,[s,a,u]]of Object.entries(o))if(e.startsWith(t,c)){if(f)return["$.",!1,e.length-n,!0];c+=t.length,u?r.push(s):i.push(s),l=l||a;continue e}d=!1,f?(t>f?i.push(s(f)+"-"+s(t)):t===f&&i.push(s(t)),f="",c++):e.startsWith("-]",c+1)?(i.push(s(t+"-")),c+=2):e.startsWith("-",c+1)?(f=t,c+=2):(i.push(s(t)),c++)}else d=!0,c++}else h=!0,c++}if(p<c)return["",!1,0,!1];if(!i.length&&!r.length)return["$.",!1,e.length-n,!0];if(0===r.length&&1===i.length&&/^\\?.$/.test(i[0])&&!h){return[(g=2===i[0].length?i[0].slice(-1):i[0],g.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")),!1,p-n,!1]}var g;const m="["+(h?"^":"")+a(i)+"]",v="["+(h?"":"^")+a(r)+"]";return[i.length&&r.length?"("+m+"|"+v+")":i.length?m:v,l,p-n,!0]},u=(e,{windowsPathsNoEscape:t=!1}={})=>t?e.replace(/\[([^\/\\])\]/g,"$1"):e.replace(/((?!\\).|^)\[([^\/\\])\]/g,"$1$2").replace(/\\([^\/])/g,"$1"),l=new Set(["!","?","+","*","@"]),d=e=>l.has(e),h="(?!\\.)",p=new Set(["[","."]),f=new Set(["..","."]),g=new Set("().*{}+?[]^$\\!"),m=e=>e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),v="[^/]",y=v+"*?",b=v+"+?";class _{type;#e;#t;#n=!1;#i=[];#r;#o;#s;#a=!1;#c;#u;#l=!1;constructor(e,t,n={}){this.type=e,e&&(this.#t=!0),this.#r=t,this.#e=this.#r?this.#r.#e:this,this.#c=this.#e===this?n:this.#e.#c,this.#s=this.#e===this?[]:this.#e.#s,"!"!==e||this.#e.#a||this.#s.push(this),this.#o=this.#r?this.#r.#i.length:0}get hasMagic(){if(void 0!==this.#t)return this.#t;for(const e of this.#i)if("string"!=typeof e&&(e.type||e.hasMagic))return this.#t=!0;return this.#t}toString(){return void 0!==this.#u?this.#u:this.type?this.#u=this.type+"("+this.#i.map(e=>String(e)).join("|")+")":this.#u=this.#i.map(e=>String(e)).join("")}#d(){if(this!==this.#e)throw new Error("should only call on root");if(this.#a)return this;let e;for(this.toString(),this.#a=!0;e=this.#s.pop();){if("!"!==e.type)continue;let t=e,n=t.#r;for(;n;){for(let i=t.#o+1;!n.type&&i<n.#i.length;i++)for(const t of e.#i){if("string"==typeof t)throw new Error("string part in extglob AST??");t.copyIn(n.#i[i])}t=n,n=t.#r}}return this}push(...e){for(const t of e)if(""!==t){if("string"!=typeof t&&!(t instanceof _&&t.#r===this))throw new Error("invalid part: "+t);this.#i.push(t)}}toJSON(){const e=null===this.type?this.#i.slice().map(e=>"string"==typeof e?e:e.toJSON()):[this.type,...this.#i.map(e=>e.toJSON())];return this.isStart()&&!this.type&&e.unshift([]),this.isEnd()&&(this===this.#e||this.#e.#a&&"!"===this.#r?.type)&&e.push({}),e}isStart(){if(this.#e===this)return!0;if(!this.#r?.isStart())return!1;if(0===this.#o)return!0;const e=this.#r;for(let t=0;t<this.#o;t++){const n=e.#i[t];if(!(n instanceof _&&"!"===n.type))return!1}return!0}isEnd(){if(this.#e===this)return!0;if("!"===this.#r?.type)return!0;if(!this.#r?.isEnd())return!1;if(!this.type)return this.#r?.isEnd();const e=this.#r?this.#r.#i.length:0;return this.#o===e-1}copyIn(e){"string"==typeof e?this.push(e):this.push(e.clone(this))}clone(e){const t=new _(this.type,e);for(const e of this.#i)t.copyIn(e);return t}static#h(e,t,n,i){let r=!1,o=!1,s=-1,a=!1;if(null===t.type){let c=n,u="";for(;c<e.length;){const n=e.charAt(c++);if(r||"\\"===n)r=!r,u+=n;else if(o)c===s+1?"^"!==n&&"!"!==n||(a=!0):"]"!==n||c===s+2&&a||(o=!1),u+=n;else if("["!==n){if(!i.noext&&d(n)&&"("===e.charAt(c)){t.push(u),u="";const r=new _(n,t);c=_.#h(e,r,c,i),t.push(r);continue}u+=n}else o=!0,s=c,a=!1,u+=n}return t.push(u),c}let c=n+1,u=new _(null,t);const l=[];let h="";for(;c<e.length;){const n=e.charAt(c++);if(r||"\\"===n)r=!r,h+=n;else if(o)c===s+1?"^"!==n&&"!"!==n||(a=!0):"]"!==n||c===s+2&&a||(o=!1),h+=n;else if("["!==n){if(d(n)&&"("===e.charAt(c)){u.push(h),h="";const t=new _(n,u);u.push(t),c=_.#h(e,t,c,i);continue}if("|"!==n){if(")"===n)return""===h&&0===t.#i.length&&(t.#l=!0),u.push(h),h="",t.push(...l,u),c;h+=n}else u.push(h),h="",l.push(u),u=new _(null,t)}else o=!0,s=c,a=!1,h+=n}return t.type=null,t.#t=void 0,t.#i=[e.substring(n-1)],c}static fromGlob(e,t={}){const n=new _(null,void 0,t);return _.#h(e,n,0,t),n}toMMPattern(){if(this!==this.#e)return this.#e.toMMPattern();const e=this.toString(),[t,n,i,r]=this.toRegExpSource();if(!(i||this.#t||this.#c.nocase&&!this.#c.nocaseMagicOnly&&e.toUpperCase()!==e.toLowerCase()))return n;const o=(this.#c.nocase?"i":"")+(r?"u":"");return Object.assign(new RegExp(`^${t}$`,o),{_src:t,_glob:e})}toRegExpSource(e){const t=e??!!this.#c.dot;if(this.#e===this&&this.#d(),!this.type){const n=this.isStart()&&this.isEnd(),i=this.#i.map(t=>{const[i,r,o,s]="string"==typeof t?_.#p(t,this.#t,n):t.toRegExpSource(e);return this.#t=this.#t||o,this.#n=this.#n||s,i}).join("");let r="";if(this.isStart()&&"string"==typeof this.#i[0]&&(1!==this.#i.length||!f.has(this.#i[0]))){const n=p,o=t&&n.has(i.charAt(0))||i.startsWith("\\.")&&n.has(i.charAt(2))||i.startsWith("\\.\\.")&&n.has(i.charAt(4)),s=!t&&!e&&n.has(i.charAt(0));r=o?"(?!(?:^|/)\\.\\.?(?:$|/))":s?h:""}let o="";return this.isEnd()&&this.#e.#a&&"!"===this.#r?.type&&(o="(?:$|\\/)"),[r+i+o,u(i),this.#t=!!this.#t,this.#n]}const n="*"===this.type||"+"===this.type,i="!"===this.type?"(?:(?!(?:":"(?:";let r=this.#f(t);if(this.isStart()&&this.isEnd()&&!r&&"!"!==this.type){const e=this.toString();return this.#i=[e],this.type=null,this.#t=void 0,[e,u(this.toString()),!1,!1]}let o=!n||e||t?"":this.#f(!0);o===r&&(o=""),o&&(r=`(?:${r})(?:${o})*?`);let s="";return s="!"===this.type&&this.#l?(this.isStart()&&!t?h:"")+b:i+r+("!"===this.type?"))"+(!this.isStart()||t||e?"":h)+y+")":"@"===this.type?")":"?"===this.type?")?":"+"===this.type&&o?")":"*"===this.type&&o?")?":`)${this.type}`),[s,u(r),this.#t=!!this.#t,this.#n]}#f(e){return this.#i.map(t=>{if("string"==typeof t)throw new Error("string type in extglob ast??");const[n,i,r,o]=t.toRegExpSource(e);return this.#n=this.#n||o,n}).filter(e=>!(this.isStart()&&this.isEnd()&&!e)).join("|")}static#p(e,t,n=!1){let i=!1,r="",o=!1;for(let s=0;s<e.length;s++){const a=e.charAt(s);if(i)i=!1,r+=(g.has(a)?"\\":"")+a;else if("\\"!==a){if("["===a){const[n,i,a,u]=c(e,s);if(a){r+=n,o=o||i,s+=a-1,t=t||u;continue}}"*"!==a?"?"!==a?r+=m(a):(r+=v,t=!0):(r+=n&&"*"===e?b:y,t=!0)}else s===e.length-1?r+="\\\\":i=!0}return[r,u(e),!!t,o]}}const C=(e,{windowsPathsNoEscape:t=!1}={})=>t?e.replace(/[?*()[\]]/g,"[$&]"):e.replace(/[?*()[\]\\]/g,"\\$&"),D=(e,t,n={})=>(r(t),!(!n.nocomment&&"#"===t.charAt(0))&&new Z(t,n).match(e)),S=/^\*+([^+@!?\*\[\(]*)$/,w=e=>t=>!t.startsWith(".")&&t.endsWith(e),R=e=>t=>t.endsWith(e),T=e=>(e=e.toLowerCase(),t=>!t.startsWith(".")&&t.toLowerCase().endsWith(e)),P=e=>(e=e.toLowerCase(),t=>t.toLowerCase().endsWith(e)),O=/^\*+\.\*+$/,k=e=>!e.startsWith(".")&&e.includes("."),x=e=>"."!==e&&".."!==e&&e.includes("."),E=/^\.\*+$/,M=e=>"."!==e&&".."!==e&&e.startsWith("."),F=/^\*+$/,q=e=>0!==e.length&&!e.startsWith("."),I=e=>0!==e.length&&"."!==e&&".."!==e,N=/^\?+([^+@!?\*\[\(]*)?$/,j=([e,t=""])=>{const n=H([e]);return t?(t=t.toLowerCase(),e=>n(e)&&e.toLowerCase().endsWith(t)):n},L=([e,t=""])=>{const n=W([e]);return t?(t=t.toLowerCase(),e=>n(e)&&e.toLowerCase().endsWith(t)):n},A=([e,t=""])=>{const n=W([e]);return t?e=>n(e)&&e.endsWith(t):n},$=([e,t=""])=>{const n=H([e]);return t?e=>n(e)&&e.endsWith(t):n},H=([e])=>{const t=e.length;return e=>e.length===t&&!e.startsWith(".")},W=([e])=>{const t=e.length;return e=>e.length===t&&"."!==e&&".."!==e},U="object"==typeof process&&process?"object"==typeof process.env&&process.env&&process.env.__MINIMATCH_TESTING_PLATFORM__||process.platform:"posix",K="win32"===U?"\\":"/";D.sep=K;const z=Symbol("globstar **");D.GLOBSTAR=z;const B=(e,t={})=>n=>D(n,e,t);D.filter=B;const V=(e,t={})=>Object.assign({},e,t),G=e=>{if(!e||"object"!=typeof e||!Object.keys(e).length)return D;const t=D;return Object.assign((n,i,r={})=>t(n,i,V(e,r)),{Minimatch:class extends t.Minimatch{constructor(t,n={}){super(t,V(e,n))}static defaults(n){return t.defaults(V(e,n)).Minimatch}},AST:class extends t.AST{constructor(t,n,i={}){super(t,n,V(e,i))}static fromGlob(n,i={}){return t.AST.fromGlob(n,V(e,i))}},unescape:(n,i={})=>t.unescape(n,V(e,i)),escape:(n,i={})=>t.escape(n,V(e,i)),filter:(n,i={})=>t.filter(n,V(e,i)),defaults:n=>t.defaults(V(e,n)),makeRe:(n,i={})=>t.makeRe(n,V(e,i)),braceExpand:(n,i={})=>t.braceExpand(n,V(e,i)),match:(n,i,r={})=>t.match(n,i,V(e,r)),sep:t.sep,GLOBSTAR:z})};D.defaults=G;const X=(e,t={})=>(r(e),t.nobrace||!/\{(?:(?!\{).)*\}/.test(e)?[e]:i(e));D.braceExpand=X;const J=(e,t={})=>new Z(e,t).makeRe();D.makeRe=J;const Q=(e,t,n={})=>{const i=new Z(t,n);return e=e.filter(e=>i.match(e)),i.options.nonull&&!e.length&&e.push(t),e};D.match=Q;const Y=/[?*]|[+@!]\(.*?\)|\[|\]/;class Z{options;set;pattern;windowsPathsNoEscape;nonegate;negate;comment;empty;preserveMultipleSlashes;partial;globSet;globParts;nocase;isWindows;platform;windowsNoMagicRoot;regexp;constructor(e,t={}){r(e),t=t||{},this.options=t,this.pattern=e,this.platform=t.platform||U,this.isWindows="win32"===this.platform,this.windowsPathsNoEscape=!!t.windowsPathsNoEscape||!1===t.allowWindowsEscape,this.windowsPathsNoEscape&&(this.pattern=this.pattern.replace(/\\/g,"/")),this.preserveMultipleSlashes=!!t.preserveMultipleSlashes,this.regexp=null,this.negate=!1,this.nonegate=!!t.nonegate,this.comment=!1,this.empty=!1,this.partial=!!t.partial,this.nocase=!!this.options.nocase,this.windowsNoMagicRoot=void 0!==t.windowsNoMagicRoot?t.windowsNoMagicRoot:!(!this.isWindows||!this.nocase),this.globSet=[],this.globParts=[],this.set=[],this.make()}hasMagic(){if(this.options.magicalBraces&&this.set.length>1)return!0;for(const e of this.set)for(const t of e)if("string"!=typeof t)return!0;return!1}debug(...e){}make(){const e=this.pattern,t=this.options;if(!t.nocomment&&"#"===e.charAt(0))return void(this.comment=!0);if(!e)return void(this.empty=!0);this.parseNegate(),this.globSet=[...new Set(this.braceExpand())],t.debug&&(this.debug=(...e)=>console.error(...e)),this.debug(this.pattern,this.globSet);const n=this.globSet.map(e=>this.slashSplit(e));this.globParts=this.preprocess(n),this.debug(this.pattern,this.globParts);let i=this.globParts.map((e,t,n)=>{if(this.isWindows&&this.windowsNoMagicRoot){const t=!(""!==e[0]||""!==e[1]||"?"!==e[2]&&Y.test(e[2])||Y.test(e[3])),n=/^[a-z]:/i.test(e[0]);if(t)return[...e.slice(0,4),...e.slice(4).map(e=>this.parse(e))];if(n)return[e[0],...e.slice(1).map(e=>this.parse(e))]}return e.map(e=>this.parse(e))});if(this.debug(this.pattern,i),this.set=i.filter(e=>-1===e.indexOf(!1)),this.isWindows)for(let e=0;e<this.set.length;e++){const t=this.set[e];""===t[0]&&""===t[1]&&"?"===this.globParts[e][2]&&"string"==typeof t[3]&&/^[a-z]:$/i.test(t[3])&&(t[2]="?")}this.debug(this.pattern,this.set)}preprocess(e){if(this.options.noglobstar)for(let t=0;t<e.length;t++)for(let n=0;n<e[t].length;n++)"**"===e[t][n]&&(e[t][n]="*");const{optimizationLevel:t=1}=this.options;return t>=2?(e=this.firstPhasePreProcess(e),e=this.secondPhasePreProcess(e)):e=t>=1?this.levelOneOptimize(e):this.adjascentGlobstarOptimize(e),e}adjascentGlobstarOptimize(e){return e.map(e=>{let t=-1;for(;-1!==(t=e.indexOf("**",t+1));){let n=t;for(;"**"===e[n+1];)n++;n!==t&&e.splice(t,n-t)}return e})}levelOneOptimize(e){return e.map(e=>0===(e=e.reduce((e,t)=>{const n=e[e.length-1];return"**"===t&&"**"===n?e:".."===t&&n&&".."!==n&&"."!==n&&"**"!==n?(e.pop(),e):(e.push(t),e)},[])).length?[""]:e)}levelTwoFileOptimize(e){Array.isArray(e)||(e=this.slashSplit(e));let t=!1;do{if(t=!1,!this.preserveMultipleSlashes){for(let n=1;n<e.length-1;n++){const i=e[n];1===n&&""===i&&""===e[0]||"."!==i&&""!==i||(t=!0,e.splice(n,1),n--)}"."!==e[0]||2!==e.length||"."!==e[1]&&""!==e[1]||(t=!0,e.pop())}let n=0;for(;-1!==(n=e.indexOf("..",n+1));){const i=e[n-1];i&&"."!==i&&".."!==i&&"**"!==i&&(t=!0,e.splice(n-1,2),n-=2)}}while(t);return 0===e.length?[""]:e}firstPhasePreProcess(e){let t=!1;do{t=!1;for(let n of e){let i=-1;for(;-1!==(i=n.indexOf("**",i+1));){let r=i;for(;"**"===n[r+1];)r++;r>i&&n.splice(i+1,r-i);let o=n[i+1];const s=n[i+2],a=n[i+3];if(".."!==o)continue;if(!s||"."===s||".."===s||!a||"."===a||".."===a)continue;t=!0,n.splice(i,1);const c=n.slice(0);c[i]="**",e.push(c),i--}if(!this.preserveMultipleSlashes){for(let e=1;e<n.length-1;e++){const i=n[e];1===e&&""===i&&""===n[0]||"."!==i&&""!==i||(t=!0,n.splice(e,1),e--)}"."!==n[0]||2!==n.length||"."!==n[1]&&""!==n[1]||(t=!0,n.pop())}let r=0;for(;-1!==(r=n.indexOf("..",r+1));){const e=n[r-1];if(e&&"."!==e&&".."!==e&&"**"!==e){t=!0;const e=1===r&&"**"===n[r+1]?["."]:[];n.splice(r-1,2,...e),0===n.length&&n.push(""),r-=2}}}}while(t);return e}secondPhasePreProcess(e){for(let t=0;t<e.length-1;t++)for(let n=t+1;n<e.length;n++){const i=this.partsMatch(e[t],e[n],!this.preserveMultipleSlashes);i&&(e[t]=i,e[n]=[])}return e.filter(e=>e.length)}partsMatch(e,t,n=!1){let i=0,r=0,o=[],s="";for(;i<e.length&&r<t.length;)if(e[i]===t[r])o.push("b"===s?t[r]:e[i]),i++,r++;else if(n&&"**"===e[i]&&t[r]===e[i+1])o.push(e[i]),i++;else if(n&&"**"===t[r]&&e[i]===t[r+1])o.push(t[r]),r++;else if("*"!==e[i]||!t[r]||!this.options.dot&&t[r].startsWith(".")||"**"===t[r]){if("*"!==t[r]||!e[i]||!this.options.dot&&e[i].startsWith(".")||"**"===e[i])return!1;if("a"===s)return!1;s="b",o.push(t[r]),i++,r++}else{if("b"===s)return!1;s="a",o.push(e[i]),i++,r++}return e.length===t.length&&o}parseNegate(){if(this.nonegate)return;const e=this.pattern;let t=!1,n=0;for(let i=0;i<e.length&&"!"===e.charAt(i);i++)t=!t,n++;n&&(this.pattern=e.slice(n)),this.negate=t}matchOne(e,t,n=!1){const i=this.options;if(this.isWindows){const n="string"==typeof e[0]&&/^[a-z]:$/i.test(e[0]),i=!n&&""===e[0]&&""===e[1]&&"?"===e[2]&&/^[a-z]:$/i.test(e[3]),r="string"==typeof t[0]&&/^[a-z]:$/i.test(t[0]),o=i?3:n?0:void 0,s=!r&&""===t[0]&&""===t[1]&&"?"===t[2]&&"string"==typeof t[3]&&/^[a-z]:$/i.test(t[3])?3:r?0:void 0;if("number"==typeof o&&"number"==typeof s){const[n,i]=[e[o],t[s]];n.toLowerCase()===i.toLowerCase()&&(t[s]=n,s>o?t=t.slice(s):o>s&&(e=e.slice(o)))}}const{optimizationLevel:r=1}=this.options;r>=2&&(e=this.levelTwoFileOptimize(e)),this.debug("matchOne",this,{file:e,pattern:t}),this.debug("matchOne",e.length,t.length);for(var o=0,s=0,a=e.length,c=t.length;o<a&&s<c;o++,s++){this.debug("matchOne loop");var u=t[s],l=e[o];if(this.debug(t,u,l),!1===u)return!1;if(u===z){this.debug("GLOBSTAR",[t,u,l]);var d=o,h=s+1;if(h===c){for(this.debug("** at the end");o<a;o++)if("."===e[o]||".."===e[o]||!i.dot&&"."===e[o].charAt(0))return!1;return!0}for(;d<a;){var p=e[d];if(this.debug("\nglobstar while",e,d,t,h,p),this.matchOne(e.slice(d),t.slice(h),n))return this.debug("globstar found match!",d,a,p),!0;if("."===p||".."===p||!i.dot&&"."===p.charAt(0)){this.debug("dot detected!",e,d,t,h);break}this.debug("globstar swallow a segment, and continue"),d++}return!(!n||(this.debug("\n>>> no match, partial?",e,d,t,h),d!==a))}let r;if("string"==typeof u?(r=l===u,this.debug("string match",u,l,r)):(r=u.test(l),this.debug("pattern match",u,l,r)),!r)return!1}if(o===a&&s===c)return!0;if(o===a)return n;if(s===c)return o===a-1&&""===e[o];throw new Error("wtf?")}braceExpand(){return X(this.pattern,this.options)}parse(e){r(e);const t=this.options;if("**"===e)return z;if(""===e)return"";let n,i=null;(n=e.match(F))?i=t.dot?I:q:(n=e.match(S))?i=(t.nocase?t.dot?P:T:t.dot?R:w)(n[1]):(n=e.match(N))?i=(t.nocase?t.dot?L:j:t.dot?A:$)(n):(n=e.match(O))?i=t.dot?x:k:(n=e.match(E))&&(i=M);const o=_.fromGlob(e,this.options).toMMPattern();return i?Object.assign(o,{test:i}):o}makeRe(){if(this.regexp||!1===this.regexp)return this.regexp;const e=this.set;if(!e.length)return this.regexp=!1,this.regexp;const t=this.options,n=t.noglobstar?"[^/]*?":t.dot?"(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?":"(?:(?!(?:\\/|^)\\.).)*?",i=new Set(t.nocase?["i"]:[]);let r=e.map(e=>{const t=e.map(e=>{if(e instanceof RegExp)for(const t of e.flags.split(""))i.add(t);return"string"==typeof e?e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"):e===z?z:e._src});return t.forEach((e,i)=>{const r=t[i+1],o=t[i-1];e===z&&o!==z&&(void 0===o?void 0!==r&&r!==z?t[i+1]="(?:\\/|"+n+"\\/)?"+r:t[i]=n:void 0===r?t[i-1]=o+"(?:\\/|"+n+")?":r!==z&&(t[i-1]=o+"(?:\\/|\\/"+n+"\\/)"+r,t[i+1]=z))}),t.filter(e=>e!==z).join("/")}).join("|");const[o,s]=e.length>1?["(?:",")"]:["",""];r="^"+o+r+s+"$",this.negate&&(r="^(?!"+r+").+$");try{this.regexp=new RegExp(r,[...i].join(""))}catch(e){this.regexp=!1}return this.regexp}slashSplit(e){return this.preserveMultipleSlashes?e.split("/"):this.isWindows&&/^\/\/[^\/]+/.test(e)?["",...e.split(/\/+/)]:e.split(/\/+/)}match(e,t=this.partial){if(this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return""===e;if("/"===e&&t)return!0;const n=this.options;this.isWindows&&(e=e.split("\\").join("/"));const i=this.slashSplit(e);this.debug(this.pattern,"split",i);const r=this.set;this.debug(this.pattern,"set",r);let o=i[i.length-1];if(!o)for(let e=i.length-2;!o&&e>=0;e--)o=i[e];for(let e=0;e<r.length;e++){const s=r[e];let a=i;if(n.matchBase&&1===s.length&&(a=[o]),this.matchOne(a,s,t))return!!n.flipNegate||!this.negate}return!n.flipNegate&&this.negate}static defaults(e){return D.defaults(e).Minimatch}}D.AST=_,D.Minimatch=Z,D.escape=C,D.unescape=u},9629:function(e,t,n){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(i=function(e){return i=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},i(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=i(e),s=0;s<n.length;s++)"default"!==n[s]&&r(t,e,n[s]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.UriList=void 0;const a=s(n(1398));class c{static from(e){return new c((t=function(e){return function(e){return e.split("\r\n")}(e).filter(e=>!e.startsWith("#")).map(e=>e.trim())}(e).map(e=>{try{return{uri:a.Uri.parse(e),str:e}}catch{return}}),t.filter(e=>!!e)));var t}constructor(e){this.entries=e}}t.UriList=c},9630:e=>{const t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},9691:(e,t,n)=>{"use strict";function i(e){var t=this;if(t instanceof i||(t=new i),t.tail=null,t.head=null,t.length=0,e&&"function"==typeof e.forEach)e.forEach(function(e){t.push(e)});else if(arguments.length>0)for(var n=0,r=arguments.length;n<r;n++)t.push(arguments[n]);return t}function r(e,t,n){var i=t===e.head?new a(n,null,t,e):new a(n,t,t.next,e);return null===i.next&&(e.tail=i),null===i.prev&&(e.head=i),e.length++,i}function o(e,t){e.tail=new a(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++}function s(e,t){e.head=new a(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++}function a(e,t,n,i){if(!(this instanceof a))return new a(e,t,n,i);this.list=i,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,n?(n.prev=this,this.next=n):this.next=null}e.exports=i,i.Node=a,i.create=i,i.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,n=e.prev;return t&&(t.prev=n),n&&(n.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=n),e.list.length--,e.next=null,e.prev=null,e.list=null,t},i.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}},i.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}},i.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)o(this,arguments[e]);return this.length},i.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)s(this,arguments[e]);return this.length},i.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}},i.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}},i.prototype.forEach=function(e,t){t=t||this;for(var n=this.head,i=0;null!==n;i++)e.call(t,n.value,i,this),n=n.next},i.prototype.forEachReverse=function(e,t){t=t||this;for(var n=this.tail,i=this.length-1;null!==n;i--)e.call(t,n.value,i,this),n=n.prev},i.prototype.get=function(e){for(var t=0,n=this.head;null!==n&&t<e;t++)n=n.next;if(t===e&&null!==n)return n.value},i.prototype.getReverse=function(e){for(var t=0,n=this.tail;null!==n&&t<e;t++)n=n.prev;if(t===e&&null!==n)return n.value},i.prototype.map=function(e,t){t=t||this;for(var n=new i,r=this.head;null!==r;)n.push(e.call(t,r.value,this)),r=r.next;return n},i.prototype.mapReverse=function(e,t){t=t||this;for(var n=new i,r=this.tail;null!==r;)n.push(e.call(t,r.value,this)),r=r.prev;return n},i.prototype.reduce=function(e,t){var n,i=this.head;if(arguments.length>1)n=t;else{if(!this.head)throw new TypeError("Reduce of empty list with no initial value");i=this.head.next,n=this.head.value}for(var r=0;null!==i;r++)n=e(n,i.value,r),i=i.next;return n},i.prototype.reduceReverse=function(e,t){var n,i=this.tail;if(arguments.length>1)n=t;else{if(!this.tail)throw new TypeError("Reduce of empty list with no initial value");i=this.tail.prev,n=this.tail.value}for(var r=this.length-1;null!==i;r--)n=e(n,i.value,r),i=i.prev;return n},i.prototype.toArray=function(){for(var e=new Array(this.length),t=0,n=this.head;null!==n;t++)e[t]=n.value,n=n.next;return e},i.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,n=this.tail;null!==n;t++)e[t]=n.value,n=n.prev;return e},i.prototype.slice=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var n=new i;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var r=0,o=this.head;null!==o&&r<e;r++)o=o.next;for(;null!==o&&r<t;r++,o=o.next)n.push(o.value);return n},i.prototype.sliceReverse=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var n=new i;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var r=this.length,o=this.tail;null!==o&&r>t;r--)o=o.prev;for(;null!==o&&r>e;r--,o=o.prev)n.push(o.value);return n},i.prototype.splice=function(e,t,...n){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var i=0,o=this.head;null!==o&&i<e;i++)o=o.next;var s=[];for(i=0;o&&i<t;i++)s.push(o.value),o=this.removeNode(o);for(null===o&&(o=this.tail),o!==this.head&&o!==this.tail&&(o=o.prev),i=0;i<n.length;i++)o=r(this,o,n[i]);return s},i.prototype.reverse=function(){for(var e=this.head,t=this.tail,n=e;null!==n;n=n.prev){var i=n.prev;n.prev=n.next,n.next=i}return this.head=t,this.tail=e,this};try{n(7103)(i)}catch(e){}},9744:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LinkedEditingRangeRequest=void 0;const i=n(7096);var r;!function(e){e.method="textDocument/linkedEditingRange",e.messageDirection=i.MessageDirection.clientToServer,e.type=new i.ProtocolRequestType(e.method)}(r||(t.LinkedEditingRangeRequest=r={}))},9765:function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ProgressType=t.ProgressToken=t.createMessageConnection=t.NullLogger=t.ConnectionOptions=t.ConnectionStrategy=t.AbstractMessageBuffer=t.WriteableStreamMessageWriter=t.AbstractMessageWriter=t.MessageWriter=t.ReadableStreamMessageReader=t.AbstractMessageReader=t.MessageReader=t.SharedArrayReceiverStrategy=t.SharedArraySenderStrategy=t.CancellationToken=t.CancellationTokenSource=t.Emitter=t.Event=t.Disposable=t.LRUCache=t.Touch=t.LinkedMap=t.ParameterStructures=t.NotificationType9=t.NotificationType8=t.NotificationType7=t.NotificationType6=t.NotificationType5=t.NotificationType4=t.NotificationType3=t.NotificationType2=t.NotificationType1=t.NotificationType0=t.NotificationType=t.ErrorCodes=t.ResponseError=t.RequestType9=t.RequestType8=t.RequestType7=t.RequestType6=t.RequestType5=t.RequestType4=t.RequestType3=t.RequestType2=t.RequestType1=t.RequestType0=t.RequestType=t.Message=t.RAL=void 0,t.MessageStrategy=t.CancellationStrategy=t.CancellationSenderStrategy=t.RequestCancellationReceiverStrategy=t.IdCancellationReceiverStrategy=t.CancellationReceiverStrategy=t.ConnectionError=t.ConnectionErrors=t.LogTraceNotification=t.SetTraceNotification=t.TraceFormat=t.TraceValues=t.TraceValue=t.Trace=void 0;const r=n(8045);Object.defineProperty(t,"Message",{enumerable:!0,get:function(){return r.Message}}),Object.defineProperty(t,"RequestType",{enumerable:!0,get:function(){return r.RequestType}}),Object.defineProperty(t,"RequestType0",{enumerable:!0,get:function(){return r.RequestType0}}),Object.defineProperty(t,"RequestType1",{enumerable:!0,get:function(){return r.RequestType1}}),Object.defineProperty(t,"RequestType2",{enumerable:!0,get:function(){return r.RequestType2}}),Object.defineProperty(t,"RequestType3",{enumerable:!0,get:function(){return r.RequestType3}}),Object.defineProperty(t,"RequestType4",{enumerable:!0,get:function(){return r.RequestType4}}),Object.defineProperty(t,"RequestType5",{enumerable:!0,get:function(){return r.RequestType5}}),Object.defineProperty(t,"RequestType6",{enumerable:!0,get:function(){return r.RequestType6}}),Object.defineProperty(t,"RequestType7",{enumerable:!0,get:function(){return r.RequestType7}}),Object.defineProperty(t,"RequestType8",{enumerable:!0,get:function(){return r.RequestType8}}),Object.defineProperty(t,"RequestType9",{enumerable:!0,get:function(){return r.RequestType9}}),Object.defineProperty(t,"ResponseError",{enumerable:!0,get:function(){return r.ResponseError}}),Object.defineProperty(t,"ErrorCodes",{enumerable:!0,get:function(){return r.ErrorCodes}}),Object.defineProperty(t,"NotificationType",{enumerable:!0,get:function(){return r.NotificationType}}),Object.defineProperty(t,"NotificationType0",{enumerable:!0,get:function(){return r.NotificationType0}}),Object.defineProperty(t,"NotificationType1",{enumerable:!0,get:function(){return r.NotificationType1}}),Object.defineProperty(t,"NotificationType2",{enumerable:!0,get:function(){return r.NotificationType2}}),Object.defineProperty(t,"NotificationType3",{enumerable:!0,get:function(){return r.NotificationType3}}),Object.defineProperty(t,"NotificationType4",{enumerable:!0,get:function(){return r.NotificationType4}}),Object.defineProperty(t,"NotificationType5",{enumerable:!0,get:function(){return r.NotificationType5}}),Object.defineProperty(t,"NotificationType6",{enumerable:!0,get:function(){return r.NotificationType6}}),Object.defineProperty(t,"NotificationType7",{enumerable:!0,get:function(){return r.NotificationType7}}),Object.defineProperty(t,"NotificationType8",{enumerable:!0,get:function(){return r.NotificationType8}}),Object.defineProperty(t,"NotificationType9",{enumerable:!0,get:function(){return r.NotificationType9}}),Object.defineProperty(t,"ParameterStructures",{enumerable:!0,get:function(){return r.ParameterStructures}});const o=n(5140);Object.defineProperty(t,"LinkedMap",{enumerable:!0,get:function(){return o.LinkedMap}}),Object.defineProperty(t,"LRUCache",{enumerable:!0,get:function(){return o.LRUCache}}),Object.defineProperty(t,"Touch",{enumerable:!0,get:function(){return o.Touch}});const s=n(3167);Object.defineProperty(t,"Disposable",{enumerable:!0,get:function(){return s.Disposable}});const a=n(6712);Object.defineProperty(t,"Event",{enumerable:!0,get:function(){return a.Event}}),Object.defineProperty(t,"Emitter",{enumerable:!0,get:function(){return a.Emitter}});const c=n(4782);Object.defineProperty(t,"CancellationTokenSource",{enumerable:!0,get:function(){return c.CancellationTokenSource}}),Object.defineProperty(t,"CancellationToken",{enumerable:!0,get:function(){return c.CancellationToken}});const u=n(5584);Object.defineProperty(t,"SharedArraySenderStrategy",{enumerable:!0,get:function(){return u.SharedArraySenderStrategy}}),Object.defineProperty(t,"SharedArrayReceiverStrategy",{enumerable:!0,get:function(){return u.SharedArrayReceiverStrategy}});const l=n(1977);Object.defineProperty(t,"MessageReader",{enumerable:!0,get:function(){return l.MessageReader}}),Object.defineProperty(t,"AbstractMessageReader",{enumerable:!0,get:function(){return l.AbstractMessageReader}}),Object.defineProperty(t,"ReadableStreamMessageReader",{enumerable:!0,get:function(){return l.ReadableStreamMessageReader}});const d=n(2549);Object.defineProperty(t,"MessageWriter",{enumerable:!0,get:function(){return d.MessageWriter}}),Object.defineProperty(t,"AbstractMessageWriter",{enumerable:!0,get:function(){return d.AbstractMessageWriter}}),Object.defineProperty(t,"WriteableStreamMessageWriter",{enumerable:!0,get:function(){return d.WriteableStreamMessageWriter}});const h=n(7408);Object.defineProperty(t,"AbstractMessageBuffer",{enumerable:!0,get:function(){return h.AbstractMessageBuffer}});const p=n(7365);Object.defineProperty(t,"ConnectionStrategy",{enumerable:!0,get:function(){return p.ConnectionStrategy}}),Object.defineProperty(t,"ConnectionOptions",{enumerable:!0,get:function(){return p.ConnectionOptions}}),Object.defineProperty(t,"NullLogger",{enumerable:!0,get:function(){return p.NullLogger}}),Object.defineProperty(t,"createMessageConnection",{enumerable:!0,get:function(){return p.createMessageConnection}}),Object.defineProperty(t,"ProgressToken",{enumerable:!0,get:function(){return p.ProgressToken}}),Object.defineProperty(t,"ProgressType",{enumerable:!0,get:function(){return p.ProgressType}}),Object.defineProperty(t,"Trace",{enumerable:!0,get:function(){return p.Trace}}),Object.defineProperty(t,"TraceValue",{enumerable:!0,get:function(){return p.TraceValue}}),Object.defineProperty(t,"TraceFormat",{enumerable:!0,get:function(){return p.TraceFormat}}),Object.defineProperty(t,"SetTraceNotification",{enumerable:!0,get:function(){return p.SetTraceNotification}}),Object.defineProperty(t,"LogTraceNotification",{enumerable:!0,get:function(){return p.LogTraceNotification}}),Object.defineProperty(t,"ConnectionErrors",{enumerable:!0,get:function(){return p.ConnectionErrors}}),Object.defineProperty(t,"ConnectionError",{enumerable:!0,get:function(){return p.ConnectionError}}),Object.defineProperty(t,"CancellationReceiverStrategy",{enumerable:!0,get:function(){return p.CancellationReceiverStrategy}}),Object.defineProperty(t,"IdCancellationReceiverStrategy",{enumerable:!0,get:function(){return p.IdCancellationReceiverStrategy}}),Object.defineProperty(t,"RequestCancellationReceiverStrategy",{enumerable:!0,get:function(){return p.RequestCancellationReceiverStrategy}}),Object.defineProperty(t,"CancellationSenderStrategy",{enumerable:!0,get:function(){return p.CancellationSenderStrategy}}),Object.defineProperty(t,"CancellationStrategy",{enumerable:!0,get:function(){return p.CancellationStrategy}}),Object.defineProperty(t,"MessageStrategy",{enumerable:!0,get:function(){return p.MessageStrategy}}),Object.defineProperty(t,"TraceValues",{enumerable:!0,get:function(){return p.TraceValues}});const f=i(n(9042));t.RAL=f.default},9796:e=>{const t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},9810:function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,i,r)}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.WorkspaceFeature=t.TextDocumentLanguageFeature=t.TextDocumentEventFeature=t.DynamicDocumentFeature=t.DynamicFeature=t.StaticFeature=t.ensure=t.LSPCancellationError=void 0;const s=n(1398),a=n(3434),c=o(n(1027)),u=o(n(8820));class l extends s.CancellationError{data;constructor(e){super(),this.data=e}}var d,h;t.LSPCancellationError=l,t.ensure=function(e,t){return void 0===e[t]&&(e[t]={}),e[t]},function(e){e.is=function(e){const t=e;return null!=t&&c.func(t.fillClientCapabilities)&&c.func(t.initialize)&&c.func(t.getState)&&c.func(t.clear)&&(void 0===t.fillInitializeParams||c.func(t.fillInitializeParams))}}(d||(t.StaticFeature=d={})),function(e){e.is=function(e){const t=e;return null!=t&&c.func(t.fillClientCapabilities)&&c.func(t.initialize)&&c.func(t.getState)&&c.func(t.clear)&&(void 0===t.fillInitializeParams||c.func(t.fillInitializeParams))&&c.func(t.register)&&c.func(t.unregister)&&void 0!==t.registrationType}}(h||(t.DynamicFeature=h={}));class p{_client;constructor(e){this._client=e}getState(){const e=this.getDocumentSelectors();let t=0;for(const n of e){t++;for(const e of s.workspace.textDocuments)if(s.languages.match(n,e)>0)return{kind:"document",id:this.registrationType.method,registrations:!0,matches:!0}}const n=t>0;return{kind:"document",id:this.registrationType.method,registrations:n,matches:!1}}}t.DynamicDocumentFeature=p,t.TextDocumentEventFeature=class extends p{_event;_type;_middleware;_createParams;_textDocument;_selectorFilter;_listener;_selectors;_onNotificationSent;static textDocumentFilter(e,t){for(const n of e)if(s.languages.match(n,t)>0)return!0;return!1}constructor(e,t,n,i,r,o,a){super(e),this._event=t,this._type=n,this._middleware=i,this._createParams=r,this._textDocument=o,this._selectorFilter=a,this._selectors=new Map,this._onNotificationSent=new s.EventEmitter}getStateInfo(){return[this._selectors.values(),!1]}getDocumentSelectors(){return this._selectors.values()}register(e){e.registerOptions.documentSelector&&(this._listener||(this._listener=this._event(e=>{this.callback(e).catch(e=>{this._client.error(`Sending document notification ${this._type.method} failed.`,e)})})),this._selectors.set(e.id,this._client.protocol2CodeConverter.asDocumentSelector(e.registerOptions.documentSelector)))}async callback(e){const t=async e=>{const t=this._createParams(e);await this._client.sendNotification(this._type,t),this.notificationSent(this.getTextDocument(e),this._type,t)};if(this.matches(e)){const n=this._middleware();return n?n(e,e=>t(e)):t(e)}}matches(e){return!this._client.hasDedicatedTextSynchronizationFeature(this._textDocument(e))&&(!this._selectorFilter||this._selectorFilter(this._selectors.values(),e))}get onNotificationSent(){return this._onNotificationSent.event}notificationSent(e,t,n){this._onNotificationSent.fire({textDocument:e,type:t,params:n})}unregister(e){this._selectors.delete(e),0===this._selectors.size&&this._listener&&(this._listener.dispose(),this._listener=void 0)}clear(){this._selectors.clear(),this._onNotificationSent.dispose(),this._onNotificationSent=new s.EventEmitter,this._listener&&(this._listener.dispose(),this._listener=void 0)}getProvider(e){for(const t of this._selectors.values())if(s.languages.match(t,e)>0)return{send:e=>this.callback(e)}}},t.TextDocumentLanguageFeature=class extends p{_registrationType;_registrations;constructor(e,t){super(e),this._registrationType=t,this._registrations=new Map}*getDocumentSelectors(){for(const e of this._registrations.values()){const t=e.data.registerOptions.documentSelector;null!==t&&(yield this._client.protocol2CodeConverter.asDocumentSelector(t))}}get registrationType(){return this._registrationType}register(e){if(!e.registerOptions.documentSelector)return;const t=this.registerLanguageProvider(e.registerOptions,e.id);this._registrations.set(e.id,{disposable:t[0],data:e,provider:t[1]})}unregister(e){const t=this._registrations.get(e);void 0!==t&&(this._registrations.delete(e),t.disposable.dispose())}clear(){this._registrations.forEach(e=>{e.disposable.dispose()}),this._registrations.clear()}getRegistration(e,t){if(!t)return[void 0,void 0];if(a.TextDocumentRegistrationOptions.is(t)){const n=a.StaticRegistrationOptions.hasId(t)?t.id:u.generateUuid(),i=t.documentSelector??e;if(i)return[n,Object.assign({},t,{documentSelector:i})]}else if(c.boolean(t)&&!0===t||a.WorkDoneProgressOptions.is(t)){if(!e)return[void 0,void 0];const n=c.boolean(t)&&!0===t?{documentSelector:e}:Object.assign({},t,{documentSelector:e});return[u.generateUuid(),n]}return[void 0,void 0]}getRegistrationOptions(e,t){if(e&&t)return c.boolean(t)&&!0===t?{documentSelector:e}:Object.assign({},t,{documentSelector:e})}getProvider(e){for(const t of this._registrations.values()){const n=t.data.registerOptions.documentSelector;if(null!==n&&s.languages.match(this._client.protocol2CodeConverter.asDocumentSelector(n),e)>0)return t.provider}}getAllProviders(){const e=[];for(const t of this._registrations.values())e.push(t.provider);return e}},t.WorkspaceFeature=class{_client;_registrationType;_registrations;constructor(e,t){this._client=e,this._registrationType=t,this._registrations=new Map}getState(){const e=this._registrations.size>0;return{kind:"workspace",id:this._registrationType.method,registrations:e}}get registrationType(){return this._registrationType}register(e){const t=this.registerLanguageProvider(e.registerOptions);this._registrations.set(e.id,{disposable:t[0],provider:t[1]})}unregister(e){const t=this._registrations.get(e);void 0!==t&&(this._registrations.delete(e),t.disposable.dispose())}clear(){this._registrations.forEach(e=>{e.disposable.dispose()}),this._registrations.clear()}getProviders(){const e=[];for(const t of this._registrations.values())e.push(t.provider);return e}}},9896:e=>{"use strict";e.exports=require("fs")}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var o=t[i]={exports:{}};return e[i].call(o.exports,o,o.exports,n),o.exports}n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};(()=>{"use strict";var e=i;Object.defineProperty(e,"__esModule",{value:!0}),e.activate=async function(e){const n=`./server/${-1!==(r.extensions.getExtension("vscode.css-language-features")?.packageJSON?.main||"").indexOf("/dist/")?"dist":"out"}/node/cssServerMain`,i=e.asAbsolutePath(n),l={execArgv:["--nolazy","--inspect="+(7e3+Math.round(999*Math.random()))]},d={run:{module:i,transport:o.TransportKind.ipc},debug:{module:i,transport:o.TransportKind.ipc,options:l}};process.env.VSCODE_L10N_BUNDLE_LOCATION=r.l10n.uri?.toString()??"",u=await(0,s.startClient)(e,(e,t,n)=>new o.LanguageClient(e,t,d,n),{fs:(0,a.getNodeFSRequestService)(),TextDecoder:t.TextDecoder}),e.subscriptions.push((0,c.registerDropOrPasteResourceSupport)({language:"css",scheme:"*"}))},e.deactivate=async function(){u&&(await u.stop(),u=void 0)};const t=n(9023),r=n(1398),o=n(9439),s=n(4523),a=n(613),c=n(4656);let u})();var r=exports;for(var o in i)r[o]=i[o];i.__esModule&&Object.defineProperty(r,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/4cefd8e163dc8255316b7bc1c43103ac9eaeba8e/extensions/css-language-features/client/dist/node/cssClientMain.js.map