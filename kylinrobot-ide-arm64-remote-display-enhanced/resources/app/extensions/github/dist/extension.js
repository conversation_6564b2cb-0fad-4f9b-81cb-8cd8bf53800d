/*! For license information please see extension.js.LICENSE.txt */
(()=>{var e={87:(e,t,r)=>{"use strict";r.d(t,{Dt:()=>s,Y8:()=>c,bb:()=>a,vv:()=>u});var n=r(269),o=r(5130),i="";function s(e,t){return void 0===t&&(t=!1),null==e?t:"true"===e.toString()[o.OL]()}function a(e){(isNaN(e)||e<0)&&(e=0),e=Math.round(e);var t=i+e%1e3,r=i+Math.floor(e/1e3)%60,n=i+Math.floor(e/6e4)%60,s=i+Math.floor(e/36e5)%24,a=Math.floor(e/864e5);return t=1===t[o.oI]?"00"+t:2===t[o.oI]?"0"+t:t,r=r[o.oI]<2?"0"+r:r,n=n[o.oI]<2?"0"+n:n,s=s[o.oI]<2?"0"+s:s,(a>0?a+".":i)+s+":"+n+":"+r+"."+t}function c(e,t){var r=null;return(0,n.Iuo)(e,function(e){if(e.identifier===t)return r=e,-1}),r}function u(e,t,r,o,i){return!i&&(0,n.KgX)(e)&&("Script error."===e||"Script error"===e)}},269:(e,t,r)=>{"use strict";function n(e,t){return e||t}function o(e,t){return e[t]}r.d(t,{$8:()=>le,$PY:()=>N,$XS:()=>B,AHH:()=>pr,Cv9:()=>Yt,DA8:()=>bt,EHq:()=>Nt,Edw:()=>G,EtT:()=>$,FJj:()=>Jt,GuU:()=>Ve,Gvm:()=>j,HzD:()=>tr,Iuo:()=>wt,JKf:()=>ct,KTd:()=>Rt,KVm:()=>St,KgX:()=>x,KhI:()=>Z,Lln:()=>ut,Lmq:()=>q,Lok:()=>dt,N6t:()=>ye,Nq2:()=>Ct,O9V:()=>L,P0f:()=>Ue,QdQ:()=>$t,R3R:()=>Zt,SZ2:()=>D,Tnt:()=>H,UUD:()=>Qt,UxO:()=>te,Vdv:()=>et,W$7:()=>Pt,WSA:()=>Te,Wtk:()=>Qe,Y0g:()=>gt,YEm:()=>Ye,Yny:()=>Tt,ZHX:()=>ve,ZWZ:()=>xe,aqQ:()=>Ut,b07:()=>U,bJ7:()=>z,cGk:()=>he,cyL:()=>M,dRz:()=>ur,eCG:()=>ht,f0d:()=>Ft,fn0:()=>_e,gBW:()=>R,hKY:()=>Fe,hXl:()=>F,isD:()=>re,jjc:()=>mt,jsL:()=>be,kgX:()=>W,mS$:()=>Ze,mmD:()=>ue,nRs:()=>Be,oJg:()=>ae,rDm:()=>_t,raO:()=>ne,sSX:()=>At,tGl:()=>rr,twz:()=>ot,v0u:()=>K,vE3:()=>me,vF1:()=>oe,vKV:()=>lr,w3n:()=>nt,w9M:()=>rt,woc:()=>V,xZI:()=>yt,zS2:()=>Je,zav:()=>Q,zkX:()=>tt,zkd:()=>pe,zwS:()=>Wt,zzB:()=>X});var i,s=void 0,a=null,c="",u="function",l="object",p="prototype",d="__proto__",f="undefined",g="constructor",m="Symbol",h="_polyfill",v="length",y="name",b="call",E="toString",T=n(Object),w=o(T,p),_=n(String),P=o(_,p),S=n(Math),k=n(Array),O=o(k,p),C=o(O,"slice");function R(e,t){try{return{v:e.apply(this,t)}}catch(e){return{e}}}function A(e){return function(t){return typeof t===e}}function I(e){var t="[object "+e+"]";return function(e){return!(!e||D(e)!==t)}}function D(e){return w[E].call(e)}function G(e,t){return typeof e===t}function U(e){return typeof e===f||e===f}function F(e){return e===a||U(e)}function L(e){return!!e||e!==s}var x=A("string"),H=A(u);function j(e){return!(!e&&F(e)||!e||typeof e!==l)}var M=o(k,"isArray"),N=I("Date"),$=A("number"),q=A("boolean"),z=I("Error");function B(e){return!!(e&&e.then&&H(e.then))}function V(e){return!e||!X(e)}function X(e){return!(!e||(t=function(){return!(e&&0+e)},r=!e,n=R(t),n.e?r:n.v));var t,r,n}var W=o(T,"getOwnPropertyDescriptor");function K(e,t){return!!e&&w.hasOwnProperty[b](e,t)}var Z=n(o(T,"hasOwn"),J);function J(e,t){return K(e,t)||!!W(e,t)}function Q(e,t,r){if(e&&j(e))for(var n in e)if(Z(e,n)&&-1===t[b](r||e,n,e[n]))break}var Y={e:"enumerable",c:"configurable",v:"value",w:"writable",g:"get",s:"set"};function ee(e){var t={};if(t[Y.c]=!0,t[Y.e]=!0,e.l){t.get=function(){return e.l.v};var r=W(e.l,"v");r&&r.set&&(t.set=function(t){e.l.v=t})}return Q(e,function(e,r){t[Y[e]]=L(r)?r:t[Y[e]]}),t}var te=o(T,"defineProperty"),re=o(T,"defineProperties");function ne(e,t,r,n,o,i){var s={e:i,c:o};return r&&(s.g=r),n&&(s.s=n),te(e,t,ee(s))}function oe(e,t,r){return te(e,t,ee(r))}function ie(e,t,r,n,o){var i={};return Q(e,function(e,n){se(i,e,t?n:e,o),se(i,n,r?n:e,o)}),n?n(i):i}function se(e,t,r,n){te(e,t,{value:r,enumerable:!0,writable:!!n})}var ae=n(_),ce="[object Error]";function ue(e,t){var r=c,n=w[E][b](e);n===ce&&(e={stack:ae(e.stack),message:ae(e.message),name:ae(e.name)});try{r=((r=JSON.stringify(e,a,t?"number"==typeof t?t:4:s))?r.replace(/"(\w+)"\s*:\s{0,1}/g,"$1: "):a)||ae(e)}catch(e){r=" - "+ue(e,t)}return n+": "+r}function le(e){throw new Error(e)}function pe(e){throw new TypeError(e)}var de=o(T,"freeze");function fe(e){return e}function ge(e){return e[d]||a}var me=o(T,"assign"),he=o(T,"keys");function ve(e){return de&&Q(e,function(e,t){(M(t)||j(t))&&ve(t)}),ye(e)}var ye=n(de,fe),be=n(o(T,"seal"),fe),Ee=n(o(T,"getPrototypeOf"),ge);function Te(e){return ie(e,1,0,ye)}function we(e){return ie(e,0,0,ye)}function _e(e){return function(e){var t={};return Q(e,function(e,r){se(t,e,r[1]),se(t,r[0],r[1])}),ye(t)}(e)}var Pe,Se=we({asyncIterator:0,hasInstance:1,isConcatSpreadable:2,iterator:3,match:4,matchAll:5,replace:6,search:7,species:8,split:9,toPrimitive:10,toStringTag:11,unscopables:12}),ke="__tsUtils$gblCfg";function Oe(){var e;return typeof globalThis!==f&&(e=globalThis),e||typeof self===f||(e=self),e||typeof window===f||(e=window),e||typeof global===f||(e=global),e}function Ce(){if(!Pe){var e=R(Oe).v||{};Pe=e[ke]=e[ke]||{}}return Pe}var Re=Ae;function Ae(e,t,r){var n=t?t[e]:a;return function(t){var o=(t?t[e]:a)||n;if(o||r){var i=arguments;return(o||r).apply(t,o?C[b](i,1):i)}pe('"'+ae(e)+'" not defined for '+ue(t))}}function Ie(e){return function(t){return t[e]}}var De=o(S,"max"),Ge=Re("slice",P),Ue=Re("substring",P),Fe=Ae("substr",P,Le);function Le(e,t,r){return F(e)&&pe("Invalid "+ue(e)),r<0?c:((t=t||0)<0&&(t=De(t+e[v],0)),U(r)?Ge(e,t):Ge(e,t,t+r))}function xe(e,t){return Ue(e,0,t)}var He,je,Me,Ne="_urid";function $e(e){var t={description:ae(e),toString:function(){return m+"("+e+")"}};return t[h]=!0,t}function qe(e){var t=function(){if(!He){var e=Ce();He=e.gblSym=e.gblSym||{k:{},s:{}}}return He}();if(!Z(t.k,e)){var r=$e(e),n=he(t.s).length;r[Ne]=function(){return n+"_"+r[E]()},t.k[e]=r,t.s[r[Ne]()]=ae(e)}return t.k[e]}function ze(){Me=Ce()}function Be(e){var t={};return!Me&&ze(),t.b=Me.lzy,te(t,"v",{configurable:!0,get:function(){var r=e();return Me.lzy||te(t,"v",{value:r}),t.b=Me.lzy,r}}),t}function Ve(e){return te({toJSON:function(){return e}},"v",{value:e})}var Xe,We="window";function Ke(e,t){var r;return function(){return!Me&&ze(),r&&!Me.lzy||(r=Ve(R(e,t).v)),r.v}}function Ze(e){return!Me&&ze(),Xe&&!1!==e&&!Me.lzy||(Xe=Ve(R(Oe).v||a)),Xe.v}function Je(e,t){var r;if((r=Xe&&!1!==t?Xe.v:Ze(t))&&r[e])return r[e];if(e===We)try{return window}catch(e){}return a}function Qe(){return!!Ye()}var Ye=Ke(Je,["document"]);function et(){return!!tt()}var tt=Ke(Je,[We]);function rt(){return!!nt()}var nt=Ke(Je,["navigator"]);function ot(){return!!ct()}var it,st,at,ct=Ke(Je,["history"]),ut=Ke(function(){return!!R(function(){return process&&(process.versions||{}).node}).v});function lt(){return it=Ve(R(Je,[m]).v)}function pt(e){var t=(Me.lzy?0:it)||lt();return t.v?t.v[e]:s}function dt(){return!!ft()}function ft(){return!Me&&ze(),((Me.lzy?0:it)||lt()).v}function gt(e,t){var r=Se[e];!Me&&ze();var n=(Me.lzy?0:it)||lt();return n.v?n.v[r||e]:t?s:function(e){var t;!je&&(je={});var r=Se[e];return r&&(t=je[r]=je[r]||$e(m+"."+r)),t}(e)}function mt(e,t){!Me&&ze();var r=(Me.lzy?0:it)||lt();return r.v?r.v(e):t?a:$e(e)}function ht(e){return!Me&&ze(),((st=(Me.lzy?0:st)||Ve(R(pt,["for"]).v)).v||qe)(e)}function vt(e){return!!e&&H(e.next)}function yt(e){return!function(e){return e===a||!L(e)}(e)&&H(e[gt(3)])}function bt(e,t,r){if(e&&(vt(e)||(!at&&(at=Ve(gt(3))),e=e[at.v]?e[at.v]():a),vt(e))){var n=s,o=s;try{for(var i=0;!(o=e.next()).done&&-1!==t[b](r||e,o.value,i,e);)i++}catch(t){n={e:t},e.throw&&(o=a,e.throw(n))}finally{try{o&&!o.done&&e.return&&e.return(o)}finally{if(n)throw n.e}}}}function Et(e,t,r){return e.apply(t,r)}function Tt(e,t){return!U(t)&&e&&(M(t)?Et(e.push,e,t):vt(t)||yt(t)?bt(t,function(t){e.push(t)}):e.push(t)),e}function wt(e,t,r){if(e)for(var n=e[v]>>>0,o=0;o<n&&(!(o in e)||-1!==t[b](r||e,e[o],o,e));o++);}var _t=Re("indexOf",O),Pt=Re("map",O);function St(e,t,r){return((e?e.slice:a)||C).apply(e,C[b](arguments,1))}function kt(e,t,r){return-1!==_t(e,t,r)}var Ot,Ct=Ae("includes",O,kt),Rt=Re("reduce",O),At=n(o(T,"create"),It);function It(e){if(!e)return{};var t=typeof e;function r(){}return t!==l&&t!==u&&pe("Prototype must be an Object or function: "+ue(e)),r[p]=e,new r}function Dt(e,t){return(T.setPrototypeOf||function(e,t){var r;!Ot&&(Ot=Ve(((r={})[d]=[],r instanceof Array))),Ot.v?e[d]=t:Q(t,function(t,r){return e[t]=r})})(e,t)}function Gt(e,t){t&&(e[y]=t)}function Ut(e,t,r){var n=r||Error,o=n[p][y],i=Error.captureStackTrace;return function(e,t,r){function n(){this[g]=t,R(oe,[this,y,{v:e,c:!0,e:!1}])}return R(oe,[t,y,{v:e,c:!0,e:!1}]),(t=Dt(t,r))[p]=r===a?At(r):(n[p]=r[p],new n),t}(e,function(){var r=this,s=arguments;try{R(Gt,[n,e]);var a=Et(n,r,C[b](s))||r;if(a!==r){var c=Ee(r);c!==Ee(a)&&Dt(a,c)}return i&&i(a,r[g]),t&&t(a,s),a}finally{R(Gt,[n,o])}},n)}function Ft(){return(Date.now||Lt)()}function Lt(){return(new Date).getTime()}function xt(e){return function(t){return F(t)&&pe("strTrim called ["+ue(t)+"]"),t&&t.replace&&(t=t.replace(e,c)),t}}var Ht,jt,Mt,Nt=Ae("trim",P,xt(/^\s+|(?=\s)\s+$/g));function $t(e){if(!e||typeof e!==l)return!1;Mt||(Mt=!et()||tt());var t=!1;if(e!==Mt){jt||(Ht=Function[p][E],jt=Ht[b](T));try{var r=Ee(e);(t=!r)||(K(r,g)&&(r=r[g]),t=!(!r||typeof r!==u||Ht[b](r)!==jt))}catch(e){}}return t}function qt(e){return e.value&&Xt(e),!0}var zt=[function(e){var t=e.value;if(M(t)){var r=e.result=[];return r.length=t.length,e.copyTo(r,t),!0}return!1},Xt,function(e){return e.type===u},function(e){var t=e.value;return!!N(t)&&(e.result=new Date(t.getTime()),!0)}];function Bt(e,t,r,n){var o=r.handler,s=r.path?n?r.path.concat(n):r.path:[],c={handler:r.handler,src:r.src,path:s},u=typeof t,p=!1,d=t===a;d||(t&&u===l?p=$t(t):d=function(e){return!i&&(i=["string","number","boolean",f,"symbol","bigint"]),!(e===l||-1===i.indexOf(e))}(u));var g={type:u,isPrim:d,isPlain:p,value:t,result:t,path:s,origin:r.src,copy:function(t,n){return Bt(e,t,n?c:r,n)},copyTo:function(t,r){return Vt(e,t,r,c)}};return g.isPrim?o&&o[b](r,g)?g.result:t:function(e,t){var n;return wt(e,function(e){if(e.k===t)return n=e,-1}),n||(n={k:t,v:t},e.push(n),function(e){oe(g,"result",{g:function(){return e.v},s:function(t){e.v=t}});for(var t=0,n=o;!(n||(t<zt.length?zt[t++]:qt))[b](r,g);)n=a}(n)),n.v}(e,t)}function Vt(e,t,r,n){if(!F(r))for(var o in r)t[o]=Bt(e,r[o],n,o);return t}function Xt(e){var t=e.value;if(t&&e.isPlain){var r=e.result={};return e.copyTo(r,t),!0}return!1}function Wt(e,t,r,n,o,i,s){return function(e,t){return wt(t,function(t){!function(e,t){Vt([],e,t,{handler:void 0,src:t,path:[]})}(e,t)}),e}(Bt([],a=e,{handler:undefined,src:a})||{},C[b](arguments));var a}var Kt,Zt=Ie(v);function Jt(){return!Me&&ze(),Kt&&!Me.lzy||(Kt=Ve(R(Je,["performance"]).v)),Kt.v}function Qt(){var e=Jt();return e&&e.now?e.now():Ft()}dt();var Yt=Ae("endsWith",P,er);function er(e,t,r){x(e)||pe("'"+ue(e)+"' is not a string");var n=x(t)?t:ae(t),o=!U(r)&&r<e[v]?r:e[v];return Ue(e,o-n[v],o)===n}var tr=Re("indexOf",P),rr=Ae("startsWith",P,nr);function nr(e,t,r){x(e)||pe("'"+ue(e)+"' is not a string");var n=x(t)?t:ae(t),o=r>0?r:0;return Ue(e,o,o+n[v])===n}var or="ref",ir="unref",sr="hasRef",ar="enabled";function cr(e,t,r){var n=M(t),o=n?t.length:0,i=(o>0?t[0]:n?s:t)||setTimeout,c=(o>1?t[1]:s)||clearTimeout,u=r[0];r[0]=function(){l.dn(),Et(u,s,C[b](arguments))};var l=function(e,t,r){var n,o=!0,i=e?t(a):a;function s(){return o=!1,i&&i[ir]&&i[ir](),n}function c(){i&&r(i),i=a}function u(){return i=t(i),o||s(),n}return(n={cancel:c,refresh:u})[sr]=function(){return i&&i[sr]?i[sr]():o},n[or]=function(){return o=!0,i&&i[or]&&i[or](),n},n[ir]=s,{h:n=te(n,ar,{get:function(){return!!i},set:function(e){!e&&i&&c(),e&&!i&&u()}}),dn:function(){i=a}}}(e,function(e){if(e){if(e.refresh)return e.refresh(),e;Et(c,s,[e])}return Et(i,s,r)},function(e){Et(c,s,[e])});return l.h}function ur(e,t){return cr(!0,s,C[b](arguments))}function lr(e,t,r){return cr(!0,e,C[b](arguments,1))}function pr(e,t){return cr(!1,s,C[b](arguments))}},380:(e,t,r)=>{"use strict";r.d(t,{Cr:()=>u,Xc:()=>p,pI:()=>l,u7:()=>d});var n=r(269),o=r(6182),i=r(4276),s=r(6492),a=r(1864),c=(0,i.T)("plugin");function u(e){return c.get(e,"state",{},!0)}function l(e,t){for(var r,i=[],a=null,c=e[o.uR]();c;){var l=c[o.AP]();if(l){a&&a[o.YH]&&l[s.qT]&&a[o.YH](l);var p=!!(r=u(l))[o.tZ];l[o.tZ]&&(p=l[o.tZ]()),p||i[o.y5](l),a=l,c=c[o.uR]()}}(0,n.Iuo)(i,function(n){var i=e[s.eT]();n[o.mE](e.getCfg(),i,t,e[o.uR]()),r=u(n),n[s.eT]||r[s.eT]||(r[s.eT]=i),r[o.tZ]=!0,delete r[o.Ik]})}function p(e){return e.sort(function(e,t){var r=0;if(t){var n=t[s.qT];e[s.qT]?r=n?e[s.Vo]-t[s.Vo]:1:n&&(r=-1)}else r=e?1:-1;return r})}function d(e){var t={};return{getName:function(){return t[o.RS]},setName:function(r){e&&e.setName(r),t[o.RS]=r},getTraceId:function(){return t[o.P5]},setTraceId:function(r){e&&e.setTraceId(r),(0,a.hX)(r)&&(t[o.P5]=r)},getSpanId:function(){return t[o.wi]},setSpanId:function(r){e&&e.setSpanId(r),(0,a.wN)(r)&&(t[o.wi]=r)},getTraceFlags:function(){return t[o.Rr]},setTraceFlags:function(r){e&&e.setTraceFlags(r),t[o.Rr]=r}}}},659:(e,t,r)=>{"use strict";r.d(t,{Im:()=>s,qU:()=>c,vz:()=>u});var n=r(269),o=r(5664),i=(((0,n.mS$)()||{}).Symbol,((0,n.mS$)()||{}).Reflect,"hasOwnProperty"),s=n.vE3||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var s in t=arguments[r])o.Wy[i].call(t,s)&&(e[s]=t[s]);return e},a=function(e,t){return a=o.s6.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t[i](r)&&(e[r]=t[r])},a(e,t)};function c(e,t){function r(){this.constructor=e}typeof t!==o.hW&&null!==t&&(0,n.zkd)("Class extends value "+String(t)+" is not a constructor or null"),a(e,t),e[o.vR]=null===t?(0,n.sSX)(t):(r[o.vR]=t[o.vR],new r)}function u(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e}},740:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AnalyticsPluginIdentifier:()=>M,BreezeChannelIdentifier:()=>j,ConfigurationManager:()=>S,ConnectionStringParser:()=>d.F,ContextTagKeys:()=>k.o,CtxTagKeys:()=>A.O,DEFAULT_BREEZE_ENDPOINT:()=>g._G,DEFAULT_BREEZE_PATH:()=>g.wc,Data:()=>_.B,DisabledPropertyName:()=>g.xF,DistributedTracingModes:()=>I.uG,Envelope:()=>m.L,Event:()=>h.J,EventPersistence:()=>I.iD,Exception:()=>v.WJ,Extensions:()=>A.F,HttpMethod:()=>g.ym,Metric:()=>y.J,PageView:()=>b.h,PageViewPerformance:()=>w.H,ProcessLegacy:()=>g.jp,PropertiesPluginIdentifier:()=>H,RemoteDependencyData:()=>E.A,RequestHeaders:()=>f.a,SampleRate:()=>g.tU,SeverityLevel:()=>P.O,TelemetryItemCreator:()=>R,ThrottleMgr:()=>p,Trace:()=>T.C,correlationIdCanIncludeCorrelationHeader:()=>n.Rs,correlationIdGetCorrelationContext:()=>n.pg,correlationIdGetCorrelationContextValue:()=>n.mD,correlationIdGetPrefix:()=>n.mp,correlationIdSetPrefix:()=>n.Wt,createDistributedTraceContextFromTrace:()=>n.Ft,createDomEvent:()=>F,createOfflineListener:()=>x.G,createTelemetryItem:()=>C,createTraceParent:()=>U.wk,dataSanitizeException:()=>O.Vt,dataSanitizeId:()=>O.HQ,dataSanitizeInput:()=>O._T,dataSanitizeKey:()=>O.lq,dataSanitizeKeyAndAddUniqueness:()=>O.zx,dataSanitizeMeasurements:()=>O.Vj,dataSanitizeMessage:()=>O.Vk,dataSanitizeProperties:()=>O.xP,dataSanitizeString:()=>O.Rr,dataSanitizeUrl:()=>O.pJ,dateTimeUtilsDuration:()=>n.jj,dateTimeUtilsNow:()=>n.lt,dsPadNumber:()=>O.qW,findAllScripts:()=>U.V5,findW3cTraceParent:()=>U.ef,formatTraceParent:()=>U.L0,getExtensionByName:()=>D.Y8,isBeaconApiSupported:()=>G.Uf,isCrossOriginError:()=>D.vv,isInternalApplicationInsightsEndpoint:()=>n.Qu,isSampledFlag:()=>U.N7,isValidSpanId:()=>U.wN,isValidTraceId:()=>U.hX,isValidTraceParent:()=>U.mJ,msToTimeSpan:()=>D.bb,parseConnectionString:()=>d.H,parseTraceParent:()=>U.ZI,strNotSpecified:()=>g.R2,stringToBoolOrDefault:()=>D.Dt,urlGetAbsoluteUrl:()=>L.wX,urlGetCompleteUrl:()=>L.k6,urlGetPathName:()=>L.Gz,urlParseFullHost:()=>L.M0,urlParseHost:()=>L.PS,urlParseUrl:()=>L.cM,utlCanUseLocalStorage:()=>u.BW,utlCanUseSessionStorage:()=>u.AN,utlDisableStorage:()=>u.Nu,utlEnableStorage:()=>u.iw,utlGetLocalStorage:()=>u.Se,utlGetSessionStorage:()=>u.vH,utlGetSessionStorageKeys:()=>u.T9,utlRemoveSessionStorage:()=>u.v7,utlRemoveStorage:()=>u.tm,utlSetLocalStorage:()=>u._M,utlSetSessionStorage:()=>u.Dt,utlSetStoragePrefix:()=>u.vh});var n=r(2318),o=r(269),i=r(3775),s=r(3673),a=r(9749),c=r(6535),u=r(4658),l=r(5130),p=function(e,t){var r,n,p,d,f,g,m,h=this,v=!1,y=!1;function b(e,t,o,i){if(v){var s=function(e){try{var t=E(e);return(0,c.Z1)(1e6)<=t.limit.samplingRate}catch(e){}return!1}(e);if(!s)return;var a=E(e),u=C(e),p=w(a,r,u),d=!1,g=0,m=R(e);try{p&&!m?(g=Math.min(a.limit.maxSendNumber,u[l.F2]+1),u[l.F2]=0,d=!0,f[e]=!0,u[l.Jm]=new Date):(f[e]=p,u[l.F2]+=1);var h=_(e);S(n,h,u);for(var y=0;y<g;y++)O(e,n,t,o)}catch(e){}return{isThrottled:d,throttleNum:g}}return i&&A(e)[l.y5]({msgID:e,message:t,severity:o}),null}function E(e){return p[e]||p[109]}function T(e,t){var r,n,i,s,a;try{var c=t||{},u={};u[l.Cx]=!!c[l.Cx];var d=c[l.zw]||{};y=(null==d?void 0:d.daysOfMonth)&&(null==d?void 0:d.daysOfMonth[l.oI])>0,u[l.zw]=(s=null===(i=(i=d)||{})||void 0===i?void 0:i.monthInterval,a=null==i?void 0:i.dayInterval,(0,o.hXl)(s)&&(0,o.hXl)(a)&&(i.monthInterval=3,y||(i[l.i9]=[28],y=!0)),i={monthInterval:null==i?void 0:i.monthInterval,dayInterval:null==i?void 0:i.dayInterval,daysOfMonth:null==i?void 0:i.daysOfMonth});var f={samplingRate:(null===(r=c.limit)||void 0===r?void 0:r.samplingRate)||100,maxSendNumber:(null===(n=c.limit)||void 0===n?void 0:n.maxSendNumber)||1};u.limit=f,p[e]=u}catch(e){}}function w(e,t,r){if(e&&!e[l.Cx]&&t&&(0,s.Gh)(r)){var n=P(),i=r[l.C9],a=e[l.zw],c=1;if(null==a?void 0:a.monthInterval){var u=12*(n.getUTCFullYear()-i.getUTCFullYear())+n.getUTCMonth()-i.getUTCMonth();c=k(a.monthInterval,0,u)}var p=1;if(y)p=(0,o.rDm)(a[l.i9],n[l.$e]());else if(null==a?void 0:a.dayInterval){var d=Math.floor((n.getTime()-i.getTime())/864e5);p=k(a.dayInterval,0,d)}return c>=0&&p>=0}return!1}function _(e,t){var r=(0,s.Gh)(t)?t:"";return e?"appInsightsThrottle"+r+"-"+e:null}function P(e){try{if(!e)return new Date;var t=new Date(e);if(!isNaN(t.getDate()))return t}catch(e){}return null}function S(e,t,r){try{return(0,u._M)(e,t,(0,o.EHq)(JSON[l.Jj](r)))}catch(e){}return!1}function k(e,t,r){return e<=0?1:r>=t&&(r-t)%e==0?Math.floor((r-t)/e)+1:-1}function O(e,t,r,n){(0,i.ZP)(t,n||1,e,r)}function C(e){try{var t=d[e];if(!t){var r=_(e,g);t=function(e,t,r){try{var n={date:P(),count:0};if(e){var o=JSON.parse(e);return{date:P(o[l.C9])||n[l.C9],count:o[l.F2]||n[l.F2],preTriggerDate:o.preTriggerDate?P(o[l.Jm]):void 0}}return S(t,r,n),n}catch(e){}return null}((0,u.Se)(n,r),n,r),d[e]=t}return d[e]}catch(e){}return null}function R(e){var t=f[e];if((0,o.hXl)(t)){t=!1;var r=C(e);r&&(t=function(e){try{if(e){var t=new Date;return e.getUTCFullYear()===t.getUTCFullYear()&&e.getUTCMonth()===t.getUTCMonth()&&e[l.$e]()===t[l.$e]()}}catch(e){}return!1}(r[l.Jm])),f[e]=t}return f[e]}function A(e){return m=m||{},(0,o.hXl)(m[e])&&(m[e]=[]),m[e]}n=(0,i.y0)(e),f={},d={},m={},p={},T(109),g=(0,s.Gh)(t)?t:"",e.addUnloadHook((0,a.a)(e.config,function(e){var t=e.cfg;r=(0,u.BW)();var n=t.throttleMgrCfg||{};(0,o.zav)(n,function(e,t){T(parseInt(e),t)})})),h._getDbgPlgTargets=function(){return[m]},h.getConfig=function(){return p},h.canThrottle=function(e){var t=C(e);return w(E(e),r,t)},h.isTriggered=function(e){return R(e)},h.isReady=function(){return v},h.flush=function(e){try{var t=A(e);if(t&&t[l.oI]>0){var r=t.slice(0);return m[e]=[],(0,o.Iuo)(r,function(e){b(e.msgID,e[l.pM],e.severity,!1)}),!0}}catch(e){}return!1},h.flushAll=function(){try{if(m){var e=!0;return(0,o.zav)(m,function(t){var r=h.flush(parseInt(t));e=e&&r}),e}}catch(e){}return!1},h.onReadyState=function(e,t){return void 0===t&&(t=!0),(v=!!(0,o.hXl)(e)||e)&&t?h.flushAll():null},h.sendMessage=function(e,t,r){return b(e,t,r,!0)}},d=r(4484),f=r(2910),g=r(5025),m=r(1062),h=r(3072),v=r(5397),y=r(5014),b=r(1448),E=r(1365),T=r(2445),w=r(4164),_=r(7358),P=r(9762),S=function(){function e(){}return e.getConfig=function(e,t,r,n){var i;return void 0===n&&(n=!1),i=r&&e[l.up]&&e[l.up][r]&&!(0,o.hXl)(e[l.up][r][t])?e[l.up][r][t]:e[t],(0,o.hXl)(i)?n:i},e}(),k=r(8596),O=r(7975);function C(e,t,r,n,i,a){var c;r=(0,O.Rr)(n,r)||g.R2,((0,o.hXl)(e)||(0,o.hXl)(t)||(0,o.hXl)(r))&&(0,o.$8)("Input doesn't contain all required fields");var u="";e[g.ks]&&(u=e[g.ks],delete e[g.ks]);var p=((c={})[l.RS]=r,c.time=(0,s._u)(new Date),c.iKey=u,c.ext=a||{},c.tags=[],c.data={},c.baseType=t,c.baseData=e,c);return(0,o.hXl)(i)||(0,o.zav)(i,function(e,t){p.data[e]=t}),p}var R=function(){function e(){}return e.create=C,e}(),A=r(1575),I=r(7374),D=r(87),G=r(7292),U=r(1864);function F(e){var t=null;if((0,o.Tnt)(Event))t=new Event(e);else{var r=(0,o.YEm)();r&&r.createEvent&&(t=r.createEvent("Event")).initEvent(e,!0,!0)}return t}var L=r(9354),x=r(5571),H="AppInsightsPropertiesPlugin",j="AppInsightsChannelPlugin",M="ApplicationInsightsAnalytics"},803:(e,t,r)=>{e.exports=r(7469)},836:(e,t,r)=>{"use strict";r.d(t,{P:()=>s});var n=r(269),o=r(6182),i=r(3775);function s(){var e=[];return{add:function(t){t&&e[o.y5](t)},run:function(t,r){(0,n.Iuo)(e,function(e){try{e(t,r)}catch(e){(0,i.ZP)(t[o.e4](),2,73,"Unexpected error calling unload handler - "+(0,n.mmD)(e))}}),e=[]}}}},856:(e,t,r)=>{"use strict";r.d(t,{v:()=>g});var n=r(8279),o=r(8205),i=r(269),s=r(6182),a=r(7847),c=r(3775),u=r(7292),l=r(3673),p="",d="&NoResponseBody=true",f="POST",g=function(){function e(){var t,r,g,m,h,v,y,b,E,T,w,_,P,S,k=0;(0,n.A)(e,this,function(e,n){var O=!0;function C(e,t){(0,c.ZP)(g,2,26,"Failed to send telemetry.",{message:e}),A(t,400,{})}function R(e){C("No endpoint url is provided for the batch",e)}function A(e,t,r,n){try{e&&e(t,r,n)}catch(e){}}function I(e,t){var r=(0,i.w3n)(),n=e[s.Vq];if(!n)return R(t),!0;n=e[s.Vq]+(P?d:p);var o=e[s.Cd],a=m?o:new Blob([o],{type:"text/plain;charset=UTF-8"});return r.sendBeacon(n,a)}function D(e,t,r){var n=e[s.Cd];try{if(n)if(I(e,t))A(t,200,{},p);else{var o=h&&h.beaconOnRetry;o&&(0,i.Tnt)(o)?o(e,t,I):(b&&b[s.L](e,t,!0),(0,c.ZP)(g,2,40,". Failed to send telemetry with Beacon API, retried with normal sender."))}}catch(e){m&&(0,c.OG)(g,"Failed to send telemetry using sendBeacon API. Ex:"+(0,i.mmD)(e)),A(t,m?0:400,{},p)}}function G(e,r,n){var a,c,u,d=e[s.c1]||{};!n&&t&&(a=(0,o.Qo)(function(e,t){c=e,u=t})),m&&n&&e.disableXhrSync&&(n=!1);var g=e[s.Vq];if(!g)return R(r),void(c&&c(!1));var v=(0,l.H$)(f,g,O,!0,n,e[s.do]);function y(t){var n=h&&h.xhrOnComplete;if(n&&(0,i.Tnt)(n))n(t,r,e);else{var o=(0,l.Lo)(t);A(r,t[s.cV],(0,l.w3)(t,m),o)}}return m||v[s.yy]("Content-type","application/json"),(0,i.Iuo)((0,i.cGk)(d),function(e){v[s.yy](e,d[e])}),v.onreadystatechange=function(){m||(y(v),4===v.readyState&&c&&c(!0))},v.onload=function(){m&&y(v)},v.onerror=function(e){A(r,m?v[s.cV]:400,(0,l.w3)(v,m),m?p:(0,l.r4)(v)),u&&u(e)},v.ontimeout=function(){A(r,m?v[s.cV]:500,(0,l.w3)(v,m),m?p:(0,l.r4)(v)),c&&c(!1)},v.send(e[s.Cd]),a}function U(e,r,n){var c,u,l,g,v=e[s.Vq],b=e[s.Cd],E=m?b:new Blob([b],{type:"application/json"}),T=new Headers,w=b[s.oI],_=!1,C=!1,I=e[s.c1]||{},D=((c={method:f,body:E})[a.x]=!0,c);e.headers&&(0,i.cGk)(e.headers)[s.oI]>0&&((0,i.Iuo)((0,i.cGk)(I),function(e){T.append(e,I[e])}),D[s.c1]=T),y?D.credentials=y:O&&m&&(D.credentials="include"),n&&(D.keepalive=!0,k+=w,m?2===e._sendReason&&(_=!0,P&&(v+=d)):_=!0);var G=new Request(v,D);try{G[a.x]=!0}catch(e){}if(!n&&t&&(u=(0,o.Qo)(function(e,t){l=e,g=t})),!v)return R(r),void(l&&l(!1));function U(e){A(r,m?0:400,{},m?p:e)}function F(e,t,n){var o=e[s.cV],a=h.fetchOnComplete;a&&(0,i.Tnt)(a)?a(e,r,n||p,t):A(r,o,{},n||p)}try{(0,o.Dv)(fetch(m?v:G,m?D:null),function(t){if(n&&(k-=w,w=0),!C)if(C=!0,t.rejected)U(t.reason&&t.reason[s.pM]),g&&g(t.reason);else{var r=t[s.pF];try{m||r.ok?m&&!r.body?(F(r,null,p),l&&l(!0)):(0,o.Dv)(r.text(),function(t){F(r,e,t[s.pF]),l&&l(!0)}):(U(r.statusText),l&&l(!1))}catch(e){U((0,i.mmD)(e)),g&&g(e)}}})}catch(e){C||(U((0,i.mmD)(e)),g&&g(e))}return _&&!C&&(C=!0,A(r,200,{}),l&&l(!0)),m&&!C&&e[s.do]>0&&S&&S.set(function(){C||(C=!0,A(r,500,{}),l&&l(!0))},e[s.do]),u}function F(e,t,r){var n=(0,i.zkX)(),o=new XDomainRequest,a=e[s.Cd];o.onload=function(){var r=(0,l.Lo)(o),n=h&&h.xdrOnComplete;n&&(0,i.Tnt)(n)?n(o,t,e):A(t,200,{},r)},o.onerror=function(){A(t,400,{},m?p:(0,l.HU)(o))},o.ontimeout=function(){A(t,500,{})},o.onprogress=function(){};var u=n&&n.location&&n.location[s.Qg]||"",d=e[s.Vq];if(d){if(!m&&0!==d.lastIndexOf(u,0)){var v="Cannot send XDomain request. The endpoint URL protocol doesn't match the hosting page protocol.";return(0,c.ZP)(g,2,40,". "+v),void C(v,t)}var y=m?d:d[s.W7](/^(https?:)/,"");o.open(f,y),e[s.do]&&(o[s.do]=e[s.do]),o.send(a),m&&r?S&&S.set(function(){o.send(a)},0):o.send(a)}else R(t)}function L(){k=0,r=!1,t=!1,g=null,m=null,h=null,v=null,y=null,b=null,E=!1,T=!1,w=!1,_=!1,P=!1,S=null}L(),e[s.mE]=function(t,n){g=n,r&&(0,c.ZP)(g,1,28,"Sender is already initialized"),e.SetConfig(t),r=!0},e._getDbgPlgTargets=function(){return[r,m,v,t]},e.SetConfig=function(e){try{if(h=e.senderOnCompleteCallBack||{},v=!!e.disableCredentials,y=e.fetchCredentials,m=!!e.isOneDs,t=!!e.enableSendPromise,E=!!e.disableXhr,T=!!e.disableBeacon,w=!!e.disableBeaconSync,S=e.timeWrapper,P=!!e.addNoResponse,_=!!e.disableFetchKeepAlive,b={sendPOST:G},m||(O=!1),v){var r=(0,u.g$)();r&&r.protocol&&"file:"===r.protocol[s.OL]()&&(O=!1)}return!0}catch(e){}return!1},e.getSyncFetchPayload=function(){return k},e.getSenderInst=function(e,t){return e&&e[s.oI]?function(e,t){for(var r,n=0,o=null,i=0;null==o&&i<e[s.oI];)n=e[i],E||1!==n?2!==n||!(0,u.R7)(t)||t&&_?3!==n||!(0,u.Uf)()||(t?w:T)||(o=D):o=U:(0,u.PV)()?o=F:(0,u.xk)()&&(o=G),i++;return o?((r={_transport:n,_isSync:t})[s.L]=o,r):null}(e,t):null},e.getFallbackInst=function(){return b},e[s.tn]=function(e,t){L()}})}return e.__ieDyn=1,e}()},857:e=>{"use strict";e.exports=require("os")},937:(e,t,r)=>{"use strict";r.d(t,{S:()=>o,_0:()=>s,hj:()=>i,m5:()=>n});var n="",o="https://browser.events.data.microsoft.com/OneCollector/1.0/",i="version",s="properties"},956:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ActiveStatus:()=>C.f,AppInsightsCore:()=>g,BaseTelemetryPlugin:()=>S.s,DiagnosticLogger:()=>c.wq,EventLatency:()=>v,EventPersistence:()=>b,EventPropertyType:()=>y,EventsDiscardedReason:()=>O.x,FullVersionString:()=>p.xE,InternalAppInsightsCore:()=>u._,LoggingSeverity:()=>_,MinChannelPriorty:()=>w,NotificationManager:()=>P.h,PerfEvent:()=>s.Q6,PerfManager:()=>s.NS,ProcessTelemetryContext:()=>k.W0,SenderPostManager:()=>M.v,TraceLevel:()=>E,Undefined:()=>U.bA,ValueKind:()=>h,ValueSanitizer:()=>T,Version:()=>p.Rx,_InternalLogMessage:()=>c.WD,__getRegisteredEvents:()=>R.El,_appendHeader:()=>A.LU,_getAllResponseHeaders:()=>A.w3,_logInternalMessage:()=>c.Oc,_testHookMaxUnloadHooksCb:()=>$.d,_throwInternal:()=>c.ZP,_warnToConsole:()=>c.OG,addEventHandler:()=>R.So,addEventListeners:()=>R.lQ,addPageHideEventListener:()=>R.Fc,addPageShowEventListener:()=>R.oS,addPageUnloadEventListener:()=>R.ee,areCookiesSupported:()=>F.gi,arrForEach:()=>i.Iuo,arrIndexOf:()=>i.rDm,arrMap:()=>i.W$7,arrReduce:()=>i.KTd,attachEvent:()=>R.Q3,blockDynamicConversion:()=>j.V9,convertAllHeadersToMap:()=>A.IL,cookieAvailable:()=>F.gi,createCookieMgr:()=>F.xN,createDynamicConfig:()=>a.e,createEnumStyle:()=>m.H,createGuid:()=>p.gj,createProcessTelemetryContext:()=>k.i8,createTraceParent:()=>H.wk,createUniqueNamespace:()=>L.Z,createUnloadHandlerContainer:()=>x.P,dateNow:()=>i.f0d,detachEvent:()=>R.Ym,disallowsSameSiteNone:()=>F.It,doPerf:()=>s.r2,dumpObj:()=>i.mmD,eventOff:()=>R.ML,eventOn:()=>R.mB,extend:()=>p.X$,findW3cTraceParent:()=>H.ef,forceDynamicConversion:()=>j.Hf,formatErrorMessageXdr:()=>A.HU,formatErrorMessageXhr:()=>A.r4,formatTraceParent:()=>H.L0,generateW3CId:()=>I.cL,getCommonSchemaMetaData:()=>p.Go,getConsole:()=>G.U5,getCookieValue:()=>p.UM,getCrypto:()=>G.MY,getDocument:()=>i.YEm,getDynamicConfigHandler:()=>j.QA,getExceptionName:()=>A.lL,getFieldValueType:()=>p.cq,getGlobal:()=>i.mS$,getGlobalInst:()=>i.zS2,getHistory:()=>i.JKf,getIEVersion:()=>G.L0,getISOString:()=>A._u,getJSON:()=>G.hm,getLocation:()=>G.g$,getMsCrypto:()=>G.iN,getNavigator:()=>i.w3n,getPerformance:()=>i.FJj,getResponseText:()=>A.Lo,getSetValue:()=>A.c2,getTenantId:()=>p.EO,getTime:()=>p.WB,getWindow:()=>i.zkX,hasDocument:()=>i.Wtk,hasHistory:()=>i.twz,hasJSON:()=>G.Z,hasNavigator:()=>i.w9M,hasOwnProperty:()=>i.v0u,hasWindow:()=>i.Vdv,isArray:()=>i.cyL,isArrayValid:()=>p.wJ,isBeaconsSupported:()=>G.Uf,isBoolean:()=>i.Lmq,isChromium:()=>p.F2,isDate:()=>i.$PY,isDocumentObjectAvailable:()=>p.g8,isError:()=>i.bJ7,isFetchSupported:()=>G.R7,isFunction:()=>i.Tnt,isGreaterThanZero:()=>p.ei,isIE:()=>G.lT,isLatency:()=>p.Hh,isNotTruthy:()=>i.woc,isNullOrUndefined:()=>i.hXl,isNumber:()=>i.EtT,isObject:()=>i.Gvm,isReactNative:()=>G.lV,isSampledFlag:()=>H.N7,isString:()=>i.KgX,isTruthy:()=>i.zzB,isTypeof:()=>i.Edw,isUint8ArrayAvailable:()=>p.h3,isUndefined:()=>i.b07,isValidSpanId:()=>H.wN,isValidTraceId:()=>H.hX,isValidTraceParent:()=>H.mJ,isValueAssigned:()=>p.yD,isValueKind:()=>p.m0,isWindowObjectAvailable:()=>p.P$,isXhrSupported:()=>G.xk,mergeEvtNamespace:()=>R.Hm,newGuid:()=>I.aq,newId:()=>D.Si,normalizeJsName:()=>A.cH,objDefineAccessors:()=>i.raO,objForEachKey:()=>i.zav,objFreeze:()=>i.N6t,objKeys:()=>i.cGk,objSeal:()=>i.jsL,onConfigChange:()=>a.a,openXhr:()=>p.H$,optimizeObject:()=>A.hW,parseResponse:()=>N.x,parseTraceParent:()=>H.ZI,perfNow:()=>i.UUD,prependTransports:()=>A.jL,proxyAssign:()=>A.qz,proxyFunctionAs:()=>A.RF,proxyFunctions:()=>A.o$,random32:()=>D.VN,randomValue:()=>D.Z1,removeEventHandler:()=>R.zh,removeEventListeners:()=>R.Wg,removePageHideEventListener:()=>R.sq,removePageShowEventListener:()=>R.vF,removePageUnloadEventListener:()=>R.Ds,safeGetCookieMgr:()=>F.um,safeGetLogger:()=>c.y0,sanitizeProperty:()=>p.TC,setEnableEnvMocks:()=>G.cU,setProcessTelemetryTimings:()=>p.u9,setValue:()=>A.KY,strContains:()=>A.Ju,strEndsWith:()=>i.Cv9,strFunction:()=>U.hW,strObject:()=>U._1,strPrototype:()=>U.vR,strStartsWith:()=>i.tGl,strTrim:()=>i.EHq,strUndefined:()=>U.bA,throwError:()=>i.$8,toISOString:()=>A._u,useXDomainRequest:()=>G.PV});var n=r(659),o=r(8279),i=r(269),s=r(8156),a=r(9749),c=r(3775),u=r(2774),l=r(937),p=r(4822),d=r(1739),f=(0,i.ZHX)({endpointUrl:l.S,propertyStorageOverride:{isVal:function(e){return!e||e.getProperty&&e.setProperty||(0,i.$8)("Invalid property storage override passed."),!0}}}),g=function(e){function t(){var r=e.call(this)||this;return(0,o.A)(t,r,function(e,t){e[d.mE]=function(r,n,o,u){(0,s.r2)(e,function(){return"AppInsightsCore.initialize"},function(){try{t[d.mE]((0,a.e)(r,f,o||e[d.Uw],!1).cfg,n,o,u)}catch(t){var s=e[d.Uw],l=(0,i.mmD)(t);-1!==l[d.Sj]("channels")&&(l+="\n - Channels must be provided through config.channels only!"),(0,c.ZP)(s,1,514,"SDK Initialization Failed - no telemetry will be sent: "+l)}},function(){return{config:r,extensions:n,logger:o,notificationManager:u}})},e.track=function(r){(0,s.r2)(e,function(){return"AppInsightsCore.track"},function(){var n=r;if(n){n[d.dg]=n[d.dg]||{},n[d.dg].trackStart=(0,p.WB)(),(0,p.Hh)(n.latency)||(n.latency=1);var o=n.ext=n.ext||{};o.sdk=o.sdk||{},o.sdk.ver=p.xE;var i=n.baseData=n.baseData||{};i[l._0]=i[l._0]||{};var s=i[l._0];s[l.hj]=s[l.hj]||e.pluginVersionString||l.m5}t.track(n)},function(){return{item:r}},!r.sync)},e[d.h4]=function(e){return t[d.h4](e||"InternalLog")}}),r}return(0,n.qU)(t,e),t.__ieDyn=1,t}(u._),m=r(4282),h=(0,m.H)({NotSet:0,Pii_DistinguishedName:1,Pii_GenericData:2,Pii_IPV4Address:3,Pii_IPv6Address:4,Pii_MailSubject:5,Pii_PhoneNumber:6,Pii_QueryString:7,Pii_SipAddress:8,Pii_SmtpAddress:9,Pii_Identity:10,Pii_Uri:11,Pii_Fqdn:12,Pii_IPV4AddressLegacy:13,Pii_IPv6ScrubLastHextets:14,Pii_DropValue:15,CustomerContent_GenericContent:32}),v=(0,m.H)({Normal:1,CostDeferred:2,RealTime:3,Immediate:4}),y=(0,m.H)({Unspecified:0,String:1,Int32:2,UInt32:3,Int64:4,UInt64:5,Double:6,Bool:7,Guid:8,DateTime:9}),b=(0,m.H)({Normal:1,Critical:2}),E=(0,m.H)({NONE:0,ERROR:1,WARNING:2,INFORMATION:3}),T=function(){function e(e){var t=this,r={},n=[],o=[];function s(e,t){var s,a=r[e];if(a&&(s=a[t]),!s&&null!==s){if((0,i.KgX)(e)&&(0,i.KgX)(t))if(o[d.oI]>0){for(var c=0;c<o[d.oI];c++)if(o[c][d.hF](e,t)){s={canHandle:!0,fieldHandler:o[c]};break}}else 0===n[d.oI]&&(s={canHandle:!0});if(!s&&null!==s)for(s=null,c=0;c<n[d.oI];c++)if(n[c][d.hF](e,t)){s={canHandle:!0,handler:n[c],fieldHandler:null};break}a||(a=r[e]={}),a[t]=s}return s}function a(e,t,r,n,o,s){if(e.handler)return e.handler.property(t,r,o,s);if(!(0,i.hXl)(o[d.QV])){if(!(4096&~n&&(0,p.m0)(o[d.QV])))return null;o[d.pF]=o[d.pF].toString()}return u(e.fieldHandler,t,r,n,o)}function c(e,t,r){return(0,p.yD)(r)?{value:r}:null}function u(e,r,n,o,s){if(s&&e){var a=e.getSanitizer(r,n,o,s[d.QV],s.propertyType);if(a)if(4===o){var l={},f=s[d.pF];(0,i.zav)(f,function(t,o){var i=r+"."+n;if((0,p.yD)(o)){var s=c(0,0,o);(s=u(e,i,t,(0,p.cq)(o),s))&&(l[t]=s[d.pF])}}),s[d.pF]=l}else{var g={path:r,name:n,type:o,prop:s,sanitizer:t};s=a.call(t,g)}}return s}e&&o.push(e),t.clearCache=function(){r={}},t.addSanitizer=function(e){e&&((0,i.Nq2)(n,e)||n.push(e),r={})},t.addFieldSanitizer=function(e){e&&((0,i.Nq2)(o,e)||o.push(e),r={})},t[d.Rl]=function(e){if(e){var t=(0,i.rDm)(n,e);-1!==t&&(n.splice(t,1),r={}),(0,i.Iuo)(n,function(t){t&&t[d.Rl]&&t[d.Rl](e)})}},t[d.Mr]=function(e){if(e){var t=(0,i.rDm)(o,e);-1!==t&&(o.splice(t,1),r={}),(0,i.Iuo)(n,function(t){t&&t[d.Mr]&&t[d.Mr](e)})}},t.isEmpty=function(){return(0,i.R3R)(n)+(0,i.R3R)(o)===0},t[d.hF]=function(e,t){var r=s(e,t);return!!r&&r[d.nw]},t[d.pF]=function(e,t,r,n){var o=s(e,t);if(o&&o[d.nw]){if(!o||!o[d.nw])return null;if(o.handler)return o.handler[d.pF](e,t,r,n);if(!(0,i.KgX)(t)||(0,i.hXl)(r)||r===l.m5)return null;var u=null,f=(0,p.cq)(r);if(8192&~f)1!==f&&2!==f&&3!==f&&4096&~f?4===f&&(u=c(0,0,n?JSON.stringify(r):r)):u=c(0,0,r);else{var g=-8193&f;if(u=r,!(0,p.yD)(u[d.pF])||1!==g&&2!==g&&3!==g&&4096&~g)return null}if(u)return a(o,e,t,f,u,n)}return null},t.property=function(e,t,r,n){var o=s(e,t);if(!o||!o[d.nw])return null;if(!(0,i.KgX)(t)||(0,i.hXl)(r)||!(0,p.yD)(r[d.pF]))return null;var c=(0,p.cq)(r[d.pF]);return 0===c?null:a(o,e,t,c,r,n)}}return e.getFieldType=p.cq,e}(),w=100,_=(0,m.H)({DISABLED:0,CRITICAL:1,WARNING:2,DEBUG:3}),P=r(1356),S=r(8257),k=r(2317),O=r(3662),C=r(4875),R=r(6149),A=r(3673),I=r(9882),D=r(6535),G=r(7292),U=r(5664),F=r(5034),L=r(4276),x=r(836),H=r(1864),j=r(9147),M=r(856),N=r(1190),$=r(8969)},991:(e,t,r)=>{"use strict";r.d(t,{q:()=>c});var n=r(269),o=r(6182);function i(e){return e&&(0,n.Gvm)(e)&&(e.isVal||e.fb||(0,n.KhI)(e,"v")||(0,n.KhI)(e,"mrg")||(0,n.KhI)(e,"ref")||e.set)}function s(e,t,r){var i,s=r.dfVal||n.O9V;if(t&&r.fb){var a=r.fb;(0,n.cyL)(a)||(a=[a]);for(var c=0;c<a[o.oI];c++){var u=a[c],l=t[u];if(s(l)?i=l:e&&(s(l=e.cfg[u])&&(i=l),e.set(e.cfg,(0,n.oJg)(u),l)),s(i))break}}return!s(i)&&s(r.v)&&(i=r.v),i}function a(e,t,r){var c,u=r;return r&&i(r)&&(u=s(e,t,r)),u&&(i(u)&&(u=a(e,t,u)),(0,n.cyL)(u)?(c=[])[o.oI]=u[o.oI]:(0,n.QdQ)(u)&&(c={}),c&&((0,n.zav)(u,function(r,n){n&&i(n)&&(n=a(e,t,n)),c[r]=n}),u=c)),u}function c(e,t,r,u){var l,p,d,f,g,m,h,v,y=u;i(y)?(l=y.isVal,p=y.set,m=y[o.XW],h=y[o.JQ],f=y.mrg,!(g=y.ref)&&(0,n.b07)(g)&&(g=!!f),d=s(e,t,y)):d=u,h&&e[o.JQ](t,r);var b=!0,E=t[r];!E&&(0,n.hXl)(E)||(v=E,b=!1,l&&v!==d&&!l(v)&&(v=d,b=!0),p&&(b=(v=p(v,d,t))===d)),b?v=d?a(e,t,d):d:((0,n.QdQ)(v)||(0,n.cyL)(d))&&f&&d&&((0,n.QdQ)(d)||(0,n.cyL)(d))&&(0,n.zav)(d,function(t,r){c(e,v,t,r)}),e.set(t,r,v),g&&e.ref(t,r),m&&e[o.XW](t,r)}},1062:(e,t,r)=>{"use strict";r.d(t,{L:()=>a});var n=r(3673),o=r(5025),i=r(5130),s=r(7975),a=function(e,t,r){var a=this,c=this;c.ver=1,c.sampleRate=100,c.tags={},c[i.RS]=(0,s.Rr)(e,r)||o.R2,c.data=t,c.time=(0,n._u)(new Date),c.aiDataContract={time:1,iKey:1,name:1,sampleRate:function(){return 100===a.sampleRate?4:1},tags:1,data:1}}},1170:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=i(r(5692)),a=i(r(857)),c=i(r(1398)),u=r(5396),l=r(2468),p=r(6548),d=r(2396),f=r(8393);function g(){return{sendPOST:(e,t)=>{const r={method:"POST",headers:{...e.headers,"Content-Type":"application/json","Content-Length":Buffer.byteLength(e.data)}};try{const n=s.request(e.urlString,r,e=>{e.on("data",function(r){t(e.statusCode??200,e.headers,r.toString())}),e.on("error",function(){t(0,{})})});n.write(e.data,e=>{e&&t(0,{})}),n.end()}catch{t(0,{})}}}}class m extends p.BaseTelemetryReporter{constructor(e,t){let r=e=>(0,l.appInsightsClientFactory)(e,c.env.machineId,c.env.sessionId,g(),t);f.TelemetryUtil.shouldUseOneDataSystemSDK(e)&&(r=e=>(0,u.oneDataSystemClientFactory)(e,c,g()));const n={release:a.release(),platform:a.platform(),architecture:a.arch()},o=new d.BaseTelemetrySender(e,r);if(e&&0===e.indexOf("AIF-"))throw new Error("AIF keys are no longer supported. Please switch to 1DS keys for 1st party extensions");super(o,c,{additionalCommonProperties:f.TelemetryUtil.getAdditionalCommonProperties(n)})}}t.default=m},1190:(e,t,r)=>{"use strict";r.d(t,{x:()=>a});var n=r(269),o=r(6182),i=r(3775),s=r(7292);function a(e,t){try{if(e&&""!==e){var r=(0,s.hm)().parse(e);if(r&&r[o.cp]&&r[o.cp]>=r.itemsAccepted&&r.itemsReceived-r.itemsAccepted===r.errors[o.oI])return r}}catch(r){(0,i.ZP)(t,1,43,"Cannot parse the response. "+(r[o.RS]||(0,n.mmD)(r)),{response:e})}return null}},1356:(e,t,r)=>{"use strict";r.d(t,{h:()=>d});var n=r(8279),o=r(8205),i=r(269),s=r(9749),a=r(6182),c=r(6492),u={perfEvtsSendAll:!1};function l(e){e.h=null;var t=e.cb;e.cb=[],(0,i.Iuo)(t,function(e){(0,i.gBW)(e.fn,[e.arg])})}function p(e,t,r,n){(0,i.Iuo)(e,function(e){e&&e[t]&&(r?(r.cb[a.y5]({fn:n,arg:e}),r.h=r.h||(0,i.dRz)(l,0,r)):(0,i.gBW)(n,[e]))})}var d=function(){function e(t){var r,l;this.listeners=[];var d=[],f={h:null,cb:[]},g=(0,s.e)(t,u);l=g[a.x6](function(e){r=!!e.cfg.perfEvtsSendAll}),(0,n.A)(e,this,function(e){(0,i.vF1)(e,"listeners",{g:function(){return d}}),e[a.vR]=function(e){d[a.y5](e)},e[a.h3]=function(e){for(var t=(0,i.rDm)(d,e);t>-1;)d[a.Ic](t,1),t=(0,i.rDm)(d,e)},e[c.fc]=function(e){p(d,c.fc,f,function(t){t[c.fc](e)})},e[c.Yp]=function(e,t){p(d,c.Yp,f,function(r){r[c.Yp](e,t)})},e[c.dI]=function(e,t){p(d,c.dI,t?f:null,function(r){r[c.dI](e,t)})},e[c.l0]=function(e){e&&(!r&&e[a.Zu]()||p(d,c.l0,null,function(t){e[a.tI]?(0,i.dRz)(function(){return t[c.l0](e)},0):t[c.l0](e)}))},e[c.s4]=function(e){e&&e[a.oI]&&p(d,c.s4,f,function(t){t[c.s4](e)})},e[c.Vj]=function(e){e&&e[a.Cd]&&p(d,c.Vj,f,function(t){t[c.Vj](e)})},e[c.Ev]=function(e,t){if(e>0){var r=t||0;p(d,c.Ev,f,function(t){t[c.Ev](e,r)})}},e[a.M5]=function(e){var t,r=function(){l&&l.rm(),l=null,d=[],f.h&&f.h[a._w](),f.h=null,f.cb=[]};if(p(d,"unload",null,function(r){var n=r[a.M5](e);n&&(t||(t=[]),t[a.y5](n))}),t)return(0,o.Qo)(function(e){return(0,o.Dv)((0,o.Xf)(t),function(){r(),e()})});r()}})}return e.__ieDyn=1,e}()},1365:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(87),o=r(2318),i=r(5130),s=r(7975),a=function(){function e(e,t,r,a,c,u,l,p,d,f,g,m){void 0===d&&(d="Ajax"),this.aiDataContract={id:1,ver:1,name:0,resultCode:0,duration:0,success:0,data:0,target:0,type:0,properties:0,measurements:0,kind:0,value:0,count:0,min:0,max:0,stdDev:0,dependencyKind:0,dependencySource:0,commandName:0,dependencyTypeName:0};var h=this;h.ver=2,h.id=t,h[i.qd]=(0,n.bb)(c),h.success=u,h.resultCode=l+"",h.type=(0,s.Rr)(e,d);var v=(0,o._U)(e,r,p,a);h.data=(0,s.pJ)(e,a)||v.data,h.target=(0,s.Rr)(e,v.target),f&&(h.target="".concat(h.target," | ").concat(f)),h[i.RS]=(0,s.Rr)(e,v[i.RS]),h[i.$y]=(0,s.xP)(e,g),h[i.XA]=(0,s.Vj)(e,m)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.RemoteDependency",e.dataType="RemoteDependencyData",e}()},1398:e=>{"use strict";e.exports=require("vscode")},1448:(e,t,r)=>{"use strict";r.d(t,{h:()=>a});var n=r(5025),o=r(87),i=r(5130),s=r(7975),a=function(){function e(e,t,r,a,c,u,l){this.aiDataContract={ver:1,name:0,url:0,duration:0,properties:0,measurements:0,id:0};var p=this;p.ver=2,p.id=(0,s.HQ)(e,l),p.url=(0,s.pJ)(e,r),p[i.RS]=(0,s.Rr)(e,t)||n.R2,isNaN(a)||(p[i.qd]=(0,o.bb)(a)),p[i.$y]=(0,s.xP)(e,c),p[i.XA]=(0,s.Vj)(e,u)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Pageview",e.dataType="PageviewData",e}()},1558:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GitHubBranchProtectionProvider=t.GitHubBranchProtectionProviderManager=void 0;const n=r(1398),o=r(7171),i=r(7937);t.GitHubBranchProtectionProviderManager=class{set enabled(e){if(this._enabled!==e){if(e)for(const e of this.gitAPI.repositories)this.providerDisposables.add(this.gitAPI.registerBranchProtectionProvider(e.rootUri,new s(e,this.globalState,this.logger,this.telemetryReporter)));else this.providerDisposables.dispose();this._enabled=e}}constructor(e,t,r,o){this.gitAPI=e,this.globalState=t,this.logger=r,this.telemetryReporter=o,this.disposables=new i.DisposableStore,this.providerDisposables=new i.DisposableStore,this._enabled=!1,this.disposables.add(this.gitAPI.onDidOpenRepository(t=>{this._enabled&&this.providerDisposables.add(e.registerBranchProtectionProvider(t.rootUri,new s(t,this.globalState,this.logger,this.telemetryReporter)))})),this.disposables.add(n.workspace.onDidChangeConfiguration(e=>{e.affectsConfiguration("github.branchProtection")&&this.updateEnablement()})),this.updateEnablement()}updateEnablement(){const e=n.workspace.getConfiguration("github",null);this.enabled=!0===e.get("branchProtection",!0)}dispose(){this.enabled=!1,this.disposables.dispose()}};class s{constructor(e,t,r,o){this.repository=e,this.globalState=t,this.logger=r,this.telemetryReporter=o,this._onDidChangeBranchProtection=new n.EventEmitter,this.onDidChangeBranchProtection=this._onDidChangeBranchProtection.event,this.globalStateKey=`branchProtection:${this.repository.rootUri.toString()}`,this.branchProtection=this.globalState.get(this.globalStateKey,[]),e.status().then(()=>{n.authentication.onDidChangeSessions(e=>{"github"===e.provider.id&&this.updateRepositoryBranchProtection()}),this.updateRepositoryBranchProtection()})}provideBranchProtection(){return this.branchProtection}async getRepositoryDetails(e,t){const r=await(0,o.getOctokitGraphql)(),{repository:n}=await r("\n\tquery repositoryPermissions($owner: String!, $repo: String!) {\n\t\trepository(owner: $owner, name: $repo) {\n\t\t\tdefaultBranchRef {\n\t\t\t\tname\n\t\t\t},\n\t\t\tviewerPermission\n\t\t}\n\t}\n",{owner:e,repo:t});return n}async getRepositoryRulesets(e,t){const r=[];let n;const i=await(0,o.getOctokitGraphql)();for(;;){const{repository:o}=await i("\n\tquery repositoryRulesets($owner: String!, $repo: String!, $cursor: String, $limit: Int = 100) {\n\t\trepository(owner: $owner, name: $repo) {\n\t\t\trulesets(includeParents: true, first: $limit, after: $cursor) {\n\t\t\t\tnodes {\n\t\t\t\t\tname\n\t\t\t\t\tenforcement\n\t\t\t\t\trules(type: PULL_REQUEST) {\n\t\t\t\t\t\ttotalCount\n\t\t\t\t\t}\n\t\t\t\t\tconditions {\n\t\t\t\t\t\trefName {\n\t\t\t\t\t\t\tinclude\n\t\t\t\t\t\t\texclude\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\ttarget\n\t\t\t\t},\n\t\t\t\tpageInfo {\n\t\t\t\t\tendCursor,\n\t\t\t\t\thasNextPage\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n",{owner:e,repo:t,cursor:n});if(r.push(...(o.rulesets?.nodes??[]).filter(e=>e&&"BRANCH"===e.target&&"ACTIVE"===e.enforcement&&(e.rules?.totalCount??0)>0)),!o.rulesets?.pageInfo.hasNextPage)break;n=o.rulesets.pageInfo.endCursor}return r}async updateRepositoryBranchProtection(){const e=[];try{for(const t of this.repository.state.remotes){const r=(0,i.getRepositoryFromUrl)(t.pushUrl??t.fetchUrl??"");if(!r)continue;this.logger.trace(`[GitHubBranchProtectionProvider][updateRepositoryBranchProtection] Fetching repository details for "${r.owner}/${r.repo}".`);const n=await this.getRepositoryDetails(r.owner,r.repo);if("ADMIN"!==n.viewerPermission&&"MAINTAIN"!==n.viewerPermission&&"WRITE"!==n.viewerPermission){this.logger.trace(`[GitHubBranchProtectionProvider][updateRepositoryBranchProtection] Skipping branch protection for "${r.owner}/${r.repo}" due to missing repository write permission.`);continue}const o=[],s=await this.getRepositoryRulesets(r.owner,r.repo);for(const e of s)o.push({include:(e.conditions.refName?.include??[]).map(e=>this.parseRulesetRefName(n,e)),exclude:(e.conditions.refName?.exclude??[]).map(e=>this.parseRulesetRefName(n,e))});e.push({remote:t.name,rules:o})}this.branchProtection=e,this._onDidChangeBranchProtection.fire(this.repository.rootUri),await this.globalState.update(this.globalStateKey,e),this.logger.trace(`[GitHubBranchProtectionProvider][updateRepositoryBranchProtection] Branch protection for "${this.repository.rootUri.toString()}": ${JSON.stringify(e)}.`),this.telemetryReporter.sendTelemetryEvent("branchProtection",void 0,{rulesetCount:this.branchProtection.length})}catch(t){this.logger.warn(`[GitHubBranchProtectionProvider][updateRepositoryBranchProtection] Failed to update repository branch protection: ${t.message}`),t instanceof o.AuthenticationError&&0!==this.branchProtection.length&&(this.branchProtection=e,this._onDidChangeBranchProtection.fire(this.repository.rootUri),await this.globalState.update(this.globalStateKey,void 0))}}parseRulesetRefName(e,t){if(t.startsWith("refs/heads/"))return t.substring(11);switch(t){case"~ALL":return"**/*";case"~DEFAULT_BRANCH":return e.defaultBranchRef.name;default:return t}}}t.GitHubBranchProtectionProvider=s},1575:(e,t,r)=>{"use strict";r.d(t,{F:()=>o,O:()=>i});var n=r(8596),o={UserExt:"user",DeviceExt:"device",TraceExt:"trace",WebExt:"web",AppExt:"app",OSExt:"os",SessionExt:"ses",SDKExt:"sdk"},i=new n.o},1739:(e,t,r)=>{"use strict";r.d(t,{Jg:()=>p,Mr:()=>g,QV:()=>u,Rl:()=>f,Sj:()=>i,Uw:()=>o,dg:()=>s,h4:()=>a,hF:()=>d,mE:()=>n,nw:()=>m,oI:()=>l,pF:()=>c});var n="initialize",o="logger",i="indexOf",s="timings",a="pollInternalLogs",c="value",u="kind",l="length",p="processTelemetryStart",d="handleField",f="rmSanitizer",g="rmFieldSanitizer",m="canHandle"},1864:(e,t,r)=>{"use strict";r.d(t,{L0:()=>_,N7:()=>w,V5:()=>S,ZI:()=>y,ef:()=>P,hX:()=>b,mJ:()=>T,wN:()=>E,wk:()=>v});var n=r(269),o=r(6182),i=r(9882),s=r(7292),a=r(6492),c=/^([\da-f]{2})-([\da-f]{32})-([\da-f]{16})-([\da-f]{2})(-[^\s]{1,64})?$/i,u="00",l="ff",p="00000000000000000000000000000000",d="0000000000000000",f=1;function g(e,t,r){return!(!e||e[o.oI]!==t||e===r||!e.match(/^[\da-f]*$/i))}function m(e,t,r){return g(e,t)?e:r}function h(e){(isNaN(e)||e<0||e>255)&&(e=1);for(var t=e.toString(16);t[o.oI]<2;)t="0"+t;return t}function v(e,t,r,s){var a;return(a={})[o.s]=g(s,2,l)?s:u,a[o.P5]=b(e)?e:(0,i.cL)(),a[o.wi]=E(t)?t:(0,n.ZWZ)((0,i.cL)(),16),a.traceFlags=r>=0&&r<=255?r:1,a}function y(e,t){var r;if(!e)return null;if((0,n.cyL)(e)&&(e=e[0]||""),!e||!(0,n.KgX)(e)||e[o.oI]>8192)return null;if(-1!==e.indexOf(",")){var i=e[o.sY](",");e=i[t>0&&i[o.oI]>t?t:0]}var s=c.exec((0,n.EHq)(e));return s&&s[1]!==l&&s[2]!==p&&s[3]!==d?((r={version:(s[1]||a.m5)[o.OL](),traceId:(s[2]||a.m5)[o.OL](),spanId:(s[3]||a.m5)[o.OL]()})[o.Rr]=parseInt(s[4],16),r):null}function b(e){return g(e,32,p)}function E(e){return g(e,16,d)}function T(e){return!!(e&&g(e[o.s],2,l)&&g(e[o.P5],32,p)&&g(e[o.wi],16,d)&&g(h(e[o.Rr]),2))}function w(e){return!!T(e)&&(e[o.Rr]&f)===f}function _(e){if(e){var t=h(e[o.Rr]);g(t,2)||(t="01");var r=e[o.s]||u;return"00"!==r&&"ff"!==r&&(r=u),"".concat(r.toLowerCase(),"-").concat(m(e.traceId,32,p).toLowerCase(),"-").concat(m(e.spanId,16,d).toLowerCase(),"-").concat(t.toLowerCase())}return""}function P(e){var t="traceparent",r=y((0,s.$Z)(t),e);return r||(r=y((0,s.Iu)(t),e)),r}function S(e){var t=e.getElementsByTagName("script"),r=[];return(0,n.Iuo)(t,function(e){var t=e[o.NA]("src");if(t){var n=e[o.NA]("crossorigin"),i=!0===e.hasAttribute("async"),s=!0===e.hasAttribute("defer"),a=e[o.NA]("referrerpolicy"),c={url:t};n&&(c.crossOrigin=n),i&&(c.async=i),s&&(c.defer=s),a&&(c.referrerPolicy=a),r[o.y5](c)}}),r}},2317:(e,t,r)=>{"use strict";r.d(t,{PV:()=>E,W0:()=>T,i8:()=>v,nU:()=>b,tS:()=>y});var n=r(269),o=r(991),i=r(9749),s=r(6182),a=r(3775),c=r(3673),u=r(6492),l=r(8156),p=r(380),d="TelemetryPluginChain",f="_hasRun",g="_getTelCtx",m=0;function h(e,t,r,c){var l=null,p=[];t||(t=(0,i.e)({},null,r[s.Uw])),null!==c&&(l=c?function(e,t,r){for(;e;){if(e[s.AP]()===r)return e;e=e[s.uR]()}return E([r],t.config||{},t)}(e,r,c):e);var d={_next:function(){var e=l;if(l=e?e[s.uR]():null,!e){var t=p;t&&t[s.oI]>0&&((0,n.Iuo)(t,function(e){try{e.func.call(e.self,e.args)}catch(e){(0,a.ZP)(r[s.Uw],2,73,"Unexpected Exception during onComplete - "+(0,n.mmD)(e))}}),p=[])}return e},ctx:{core:function(){return r},diagLog:function(){return(0,a.y0)(r,t.cfg)},getCfg:function(){return t.cfg},getExtCfg:function(e,r){var i=f(e,!0);return r&&(0,n.zav)(r,function(e,r){if((0,n.hXl)(i[e])){var s=t.cfg[e];!s&&(0,n.hXl)(s)||(i[e]=s)}(0,o.q)(t,i,e,r)}),t[s.h0](i,r)},getConfig:function(e,r,o){void 0===o&&(o=!1);var i,s=f(e,!1),a=t.cfg;return!s||!s[r]&&(0,n.hXl)(s[r])?!a[r]&&(0,n.hXl)(a[r])||(i=a[r]):i=s[r],i||!(0,n.hXl)(i)?i:o},hasNext:function(){return!!l},getNext:function(){return l},setNext:function(e){l=e},iterate:function(e){for(var t;t=d._next();){var r=t[s.AP]();r&&e(r)}},onComplete:function(e,t){for(var r=[],o=2;o<arguments.length;o++)r[o-2]=arguments[o];e&&p[s.y5]({func:e,self:(0,n.b07)(t)?d.ctx:t,args:r})}}};function f(e,r){var n=null,o=t.cfg;if(o&&e){var i=o[u.Bw];!i&&r&&(i={}),o[u.Bw]=i,(i=t.ref(o,u.Bw))&&(!(n=i[e])&&r&&(n={}),i[e]=n,n=t.ref(i,e))}return n}return d}function v(e,t,r,o){var a=(0,i.e)(t),c=h(e,a,r,o),l=c.ctx;return l[s.$5]=function(e){var t=c._next();return t&&t[u.qT](e,l),!t},l[s.$o]=function(e,t){return void 0===e&&(e=null),(0,n.cyL)(e)&&(e=E(e,a.cfg,r,t)),v(e||l[s.uR](),a.cfg,r,t)},l}function y(e,t,r){var o=(0,i.e)(t.config),a=h(e,o,t,r),c=a.ctx;return c[s.$5]=function(e){var t=a._next();return t&&t[s.M5](c,e),!t},c[s.$o]=function(e,r){return void 0===e&&(e=null),(0,n.cyL)(e)&&(e=E(e,o.cfg,t,r)),y(e||c[s.uR](),t,r)},c}function b(e,t,r){var o=(0,i.e)(t.config),a=h(e,o,t,r).ctx;return a[s.$5]=function(e){return a.iterate(function(t){(0,n.Tnt)(t[s.HC])&&t[s.HC](a,e)})},a[s.$o]=function(e,r){return void 0===e&&(e=null),(0,n.cyL)(e)&&(e=E(e,o.cfg,t,r)),b(e||a[s.uR](),t,r)},a}function E(e,t,r,o){var i=null,c=!o;if((0,n.cyL)(e)&&e[s.oI]>0){var h=null;(0,n.Iuo)(e,function(e){if(c||o!==e||(c=!0),c&&e&&(0,n.Tnt)(e[u.qT])){var y=function(e,t,r){var o,i=null,c=(0,n.Tnt)(e[u.qT]),h=(0,n.Tnt)(e[s.YH]),y={getPlugin:function(){return e},getNext:function(){return i},processTelemetry:function(o,a){b(a=a||function(){var o;return e&&(0,n.Tnt)(e[g])&&(o=e[g]()),o||(o=v(y,t,r)),o}(),function(t){if(!e||!c)return!1;var r=(0,p.Cr)(e);return!r[s.Ik]&&!r[u.Hr]&&(h&&e[s.YH](i),e[u.qT](o,t),!0)},"processTelemetry",function(){return{item:o}},!o.sync)||a[s.$5](o)},unload:function(t,r){b(t,function(){var n=!1;if(e){var o=(0,p.Cr)(e),i=e[u.eT]||o[u.eT];!e||i&&i!==t.core()||o[s.Ik]||(o[u.eT]=null,o[s.Ik]=!0,o[s.tZ]=!1,e[s.Ik]&&!0===e[s.Ik](t,r)&&(n=!0))}return n},"unload",function(){},r[s.tI])||t[s.$5](r)},update:function(t,r){b(t,function(){var n=!1;if(e){var o=(0,p.Cr)(e),i=e[u.eT]||o[u.eT];!e||i&&i!==t.core()||o[s.Ik]||e[s.HC]&&!0===e[s.HC](t,r)&&(n=!0)}return n},"update",function(){},!1)||t[s.$5](r)},_id:o=e?e[s.Ju]+"-"+e[u.Vo]+"-"+m++:"Unknown-0-"+m++,_setNext:function(e){i=e}};function b(t,r,c,p,g){var m=!1,h=e?e[s.Ju]:d,v=t[f];return v||(v=t[f]={}),t.setNext(i),e&&(0,l.r2)(t[u.eT](),function(){return h+":"+c},function(){v[o]=!0;try{var e=i?i._id:u.m5;e&&(v[e]=!1),m=r(t)}catch(e){var l=!i||v[i._id];l&&(m=!0),i&&l||(0,a.ZP)(t[s.e4](),1,73,"Plugin ["+h+"] failed during "+c+" - "+(0,n.mmD)(e)+", run flags: "+(0,n.mmD)(v))}},p,g),m}return(0,n.N6t)(y)}(e,t,r);i||(i=y),h&&h._setNext(y),h=y}})}return o&&!i?E([o],t,r):i}var T=function(e,t,r,o){var i=v(e,t,r,o);(0,c.o$)(this,i,(0,n.cGk)(i))}},2318:(e,t,r)=>{"use strict";r.d(t,{Ft:()=>T,Qu:()=>d,Rs:()=>m,Wt:()=>f,_U:()=>y,jj:()=>E,lt:()=>b,mD:()=>v,mp:()=>g,pg:()=>h});var n=r(269),o=r(1864),i=r(5025),s=r(2910),a=r(7975),c=r(9354),u=r(5130),l=[i._G+i.wc,"https://breeze.aimon.applicationinsights.io"+i.wc,"https://dc-int.services.visualstudio.com"+i.wc],p="cid-v1:";function d(e){return-1!==(0,n.rDm)(l,e[u.OL]())}function f(e){p=e}function g(){return p}function m(e,t,r){if(!t||e&&e.disableCorrelationHeaders)return!1;if(e&&e[u.Ol])for(var o=0;o<e.correlationHeaderExcludePatterns[u.oI];o++)if(e[u.Ol][o].test(t))return!1;var i=(0,c.cM)(t).host[u.OL]();if(!i||-1===(0,n.HzD)(i,":443")&&-1===(0,n.HzD)(i,":80")||(i=((0,c.M0)(t,!0)||"")[u.OL]()),(!e||!e.enableCorsCorrelation)&&i&&i!==r)return!1;var s,a=e&&e.correlationHeaderDomains;if(a&&((0,n.Iuo)(a,function(e){var t=new RegExp(e.toLowerCase().replace(/\\/g,"\\\\").replace(/\./g,"\\.").replace(/\*/g,".*"));s=s||t.test(i)}),!s))return!1;var l=e&&e.correlationHeaderExcludedDomains;if(!l||0===l[u.oI])return!0;for(o=0;o<l[u.oI];o++)if(new RegExp(l[o].toLowerCase().replace(/\\/g,"\\\\").replace(/\./g,"\\.").replace(/\*/g,".*")).test(i))return!1;return i&&i[u.oI]>0}function h(e){if(e){var t=v(e,s.a[1]);if(t&&t!==p)return t}}function v(e,t){if(e)for(var r=e[u.sY](","),n=0;n<r[u.oI];++n){var o=r[n][u.sY]("=");if(2===o[u.oI]&&o[0]===t)return o[1]}}function y(e,t,r,n){var o,i=n,s=n;if(t&&t[u.oI]>0){var l=(0,c.cM)(t);if(o=l.host,!i)if(null!=l[u.Ue]){var p=0===l.pathname[u.oI]?"/":l[u.Ue];"/"!==p.charAt(0)&&(p="/"+p),s=l[u.Ue],i=(0,a.Rr)(e,r?r+" "+p:p)}else i=(0,a.Rr)(e,t)}else o=n,i=n;return{target:o,name:i,data:s}}function b(){var e=(0,n.FJj)();if(e&&e.now&&e.timing){var t=e.now()+e.timing.navigationStart;if(t>0)return t}return(0,n.f0d)()}function E(e,t){var r=null;return 0===e||0===t||(0,n.hXl)(e)||(0,n.hXl)(t)||(r=t-e),r}function T(e,t){var r=e||{};return{getName:function(){return r[u.RS]},setName:function(e){t&&t.setName(e),r[u.RS]=e},getTraceId:function(){return r.traceID},setTraceId:function(e){t&&t.setTraceId(e),(0,o.hX)(e)&&(r.traceID=e)},getSpanId:function(){return r.parentID},setSpanId:function(e){t&&t.setSpanId(e),(0,o.wN)(e)&&(r.parentID=e)},getTraceFlags:function(){return r.traceFlags},setTraceFlags:function(e){t&&t.setTraceFlags(e),r.traceFlags=e}}}},2384:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GithubRemoteSourcePublisher=void 0;const n=r(8566);t.GithubRemoteSourcePublisher=class{constructor(e){this.gitAPI=e,this.name="GitHub",this.icon="github"}publishRepository(e){return(0,n.publishRepository)(this.gitAPI,e)}}},2396:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.BaseTelemetrySender=void 0,function(e){e[e.NOT_INSTANTIATED=0]="NOT_INSTANTIATED",e[e.INSTANTIATING=1]="INSTANTIATING",e[e.INSTANTIATED=2]="INSTANTIATED"}(r||(r={})),t.BaseTelemetrySender=class{constructor(e,t){this._instantiationStatus=r.NOT_INSTANTIATED,this._eventQueue=[],this._exceptionQueue=[],this._clientFactory=t,this._key=e}sendEventData(e,t){this._telemetryClient?this._telemetryClient.logEvent(e,t):this._instantiationStatus!==r.INSTANTIATED&&this._eventQueue.push({eventName:e,data:t})}sendErrorData(e,t){if(!this._telemetryClient)return void(this._instantiationStatus!==r.INSTANTIATED&&this._exceptionQueue.push({exception:e,data:t}));const n={stack:e.stack,message:e.message,name:e.name};if(t){const e=t.properties||t;t.properties={...e,...n}}else t={properties:n};this._telemetryClient.logEvent("unhandlederror",t)}async flush(){return this._telemetryClient?.flush()}async dispose(){this._telemetryClient&&(await this._telemetryClient.dispose(),this._telemetryClient=void 0)}_flushQueues(){this._eventQueue.forEach(({eventName:e,data:t})=>this.sendEventData(e,t)),this._eventQueue=[],this._exceptionQueue.forEach(({exception:e,data:t})=>this.sendErrorData(e,t)),this._exceptionQueue=[]}instantiateSender(){this._instantiationStatus===r.NOT_INSTANTIATED&&(this._instantiationStatus=r.INSTANTIATING,this._clientFactory(this._key).then(e=>{this._telemetryClient=e,this._instantiationStatus=r.INSTANTIATED,this._flushQueues()}).catch(e=>{console.error(e),this._instantiationStatus=r.INSTANTIATED}))}}},2445:(e,t,r)=>{"use strict";r.d(t,{C:()=>s});var n=r(5025),o=r(5130),i=r(7975),s=function(){function e(e,t,r,s,a){this.aiDataContract={ver:1,message:1,severityLevel:0,properties:0};var c=this;c.ver=2,t=t||n.R2,c[o.pM]=(0,i.Vk)(e,t),c[o.$y]=(0,i.xP)(e,s),c[o.XA]=(0,i.Vj)(e,a),r&&(c[o.Ur]=r)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Message",e.dataType="MessageData",e}()},2468:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.appInsightsClientFactory=void 0;const s=r(740),a=r(8393);t.appInsightsClientFactory=async(e,t,n,o,c)=>{let u;try{const t=await Promise.resolve().then(()=>i(r(5927))),n={};if(o){const e={alwaysUseXhrOverride:!0,httpXHROverride:o};n[s.BreezeChannelIdentifier]=e}let a;e.startsWith("InstrumentationKey=")||(a=e);const c=a?{instrumentationKey:a}:{connectionString:e};u=new t.ApplicationInsights({...c,disableAjaxTracking:!0,disableExceptionTracking:!0,disableFetchTracking:!0,disableCorrelationHeaders:!0,disableCookiesUsage:!0,autoTrackPageVisitTime:!1,emitLineDelimitedJson:!1,disableInstrumentationKeyValidation:!0,extensionConfig:n})}catch(e){return Promise.reject(e)}return{logEvent:(e,r)=>{const o={...r?.properties,...r?.measurements};c?.length&&a.TelemetryUtil.applyReplacements(o,c),u?.track({name:e,data:o,baseType:"EventData",ext:{user:{id:t,authId:t},app:{sesId:n}},baseData:{name:e,properties:r?.properties,measurements:r?.measurements}})},flush:async()=>{u?.flush(!1)},dispose:async()=>new Promise(e=>{u?.unload(!0,()=>{e(),u=void 0},1e3)})}}},2475:(e,t,r)=>{"use strict";r.d(t,{DD:()=>c,Lx:()=>a,NU:()=>s});var n=r(269),o=r(6182);function i(e,t,r){return!e&&(0,n.hXl)(e)?t:(0,n.Lmq)(e)?e:"true"===(0,n.oJg)(e)[o.OL]()}function s(e){return{mrg:!0,v:e}}function a(e,t,r){return{fb:r,isVal:e,v:t}}function c(e,t){return{fb:t,set:i,v:!!e}}},2613:e=>{"use strict";e.exports=require("assert")},2676:function(e,t,r){"use strict";var n,o=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(n=function(e){return n=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t},n(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=n(e),s=0;s<r.length;s++)"default"!==r[s]&&o(t,e,r[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.isFileInRepo=u,t.getRepositoryForFile=l,t.rangeString=f,t.notebookCellRangeString=g,t.encodeURIComponentExceptSlashes=m,t.getLink=async function(e,t,r,n,o="permalink",i,s){n=n??"https://github.com";const u=function(e){let t;const{fileUri:r,lineNumber:n}=function(e){return e instanceof a.Uri?{fileUri:e,lineNumber:void 0}:void 0!==e&&"lineNumber"in e&&"uri"in e?{fileUri:e.uri,lineNumber:e.lineNumber}:{fileUri:void 0,lineNumber:void 0}}(e),o=r??a.window.activeTextEditor?.document.uri;if(o){if("vscode-notebook-cell"===o.scheme&&a.window.activeNotebookEditor?.notebook.uri.fsPath===o.fsPath){const e=a.window.activeNotebookEditor.notebook.getCells().find(e=>e.document.uri.fragment===o?.fragment),t=e?.index??a.window.activeNotebookEditor.selection.start,r=d(n);return{type:p.Notebook,uri:o,cellIndex:t,range:r}}return t=d(n),{type:p.File,uri:o,range:t}}if(a.window.activeNotebookEditor)return{type:p.Notebook,uri:a.window.activeNotebookEditor.notebook.uri,cellIndex:a.window.activeNotebookEditor.selection.start,range:void 0}}(i),v=u?.uri,y=e.repositories.find(e=>(0,c.repositoryHasGitHubRemote)(e)),b=(v?l(e,v):y)??y;if(!b)return;let E;if(r&&v&&await h(b,v),b.state.remotes.find(e=>{if(e.fetchUrl){const t=(0,c.getRepositoryFromUrl)(e.fetchUrl);if(t&&e.name===b.state.HEAD?.upstream?.remote)return void(E=t);t&&!E&&(E=t)}}),!E)return;const T=b.state.HEAD?`/blob/${"headlink"===o&&b.state.HEAD.name?m(b.state.HEAD.name):b.state.HEAD?.commit}`:"",w=`${n}/${E.owner}/${E.repo}${T}`;if(!v)return w;const _=m(v.path.substring(b.rootUri.path.length));return`${w}${u.type===p.File?t?`${_}${s?f(u.range):""}`:"":t?`${_}${s?g(u.cellIndex,u.range):""}`:""}`},t.getAvatarLink=function(e,t){return`https://avatars.githubusercontent.com/u/${e}?s=${t}`},t.getBranchLink=function(e,t,r="https://github.com"){const n=(0,c.getRepositoryFromUrl)(e);if(!n)throw new Error("Invalid repository URL provided");return t=m(t),`${r}/${n.owner}/${n.repo}/tree/${t}`},t.getCommitLink=function(e,t,r="https://github.com"){const n=(0,c.getRepositoryFromUrl)(e);if(!n)throw new Error("Invalid repository URL provided");return`${r}/${n.owner}/${n.repo}/commit/${t}`},t.getVscodeDevHost=function(){return`https://${a.env.appName.toLowerCase().includes("insiders")?"insiders.":""}vscode.dev/github`},t.ensurePublished=h;const a=s(r(1398)),c=r(7937);function u(e,t){return t.path.toLowerCase()===e.rootUri.path.toLowerCase()||t.path.toLowerCase().startsWith(e.rootUri.path.toLowerCase())&&t.path.substring(e.rootUri.path.length).startsWith("/")}function l(e,t){for(const r of e.repositories)if(u(r,t))return r}var p;function d(e){return void 0===e||a.window.activeTextEditor&&!a.window.activeTextEditor.selection.isEmpty&&a.window.activeTextEditor.selection.contains(new a.Position(e-1,0))?a.window.activeTextEditor?.selection:new a.Range(e-1,0,e-1,1)}function f(e){if(!e)return"";let t=`#L${e.start.line+1}`;return e.start.line!==e.end.line&&(t+=`-L${e.end.line+1}`),t}function g(e,t){if(void 0===e)return"";if(!t)return`#C${e+1}`;let r=`#C${e+1}:L${t.start.line+1}`;return t.start.line!==t.end.line&&(r+=`-L${t.end.line+1}`),r}function m(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}async function h(e,t){if(await e.status(),(0===e.state.HEAD?.type||2===e.state.HEAD?.type)&&!e?.state.HEAD?.upstream){const e=a.l10n.t("Publish Branch & Copy Link");if(await a.window.showInformationMessage(a.l10n.t("The current branch is not published to the remote. Would you like to publish your branch before copying a link?"),{modal:!0},e)!==e)throw new a.CancellationError;await a.commands.executeCommand("git.publish")}if(![...e.state.workingTreeChanges,...e.state.indexChanges].find(e=>e.uri.toString()===t.toString())||e.state.HEAD?.ahead||e.state.HEAD?.behind){if(e.state.HEAD?.ahead){const t=a.l10n.t("Push Commits & Copy Link");if(await a.window.showInformationMessage(a.l10n.t("The current branch has unpublished commits. Would you like to push your commits before copying a link?"),{modal:!0},t)!==t)throw new a.CancellationError;await e.push()}else if(e.state.HEAD?.behind){const t=a.l10n.t("Pull Changes & Copy Link");if(await a.window.showInformationMessage(a.l10n.t("The current branch is not up to date. Would you like to pull before copying a link?"),{modal:!0},t)!==t)throw new a.CancellationError;await e.pull()}}else{const e=a.l10n.t("Commit Changes"),t=a.l10n.t("Copy Anyway");if(await a.window.showWarningMessage(a.l10n.t("The current file has uncommitted changes. Please commit your changes before copying a link."),{modal:!0},e,t)!==t)throw a.commands.executeCommand("workbench.view.scm"),new a.CancellationError}await e.status()}!function(e){e[e.File=1]="File",e[e.Notebook=2]="Notebook"}(p||(p={}))},2774:(e,t,r)=>{"use strict";r.d(t,{_:()=>G});var n,o=r(659),i=r(8279),s=r(8205),a=r(269),c=r(9749),u=r(4875),l=r(6182),p=r(4013),d=r(7847),f=r(5034),g=r(4276),m=r(7867),h=r(3775),v=r(3673),y=r(6492),b=r(1356),E=r(8156),T=r(2317),w=r(380),_=function(e){function t(){var r,n,o=e.call(this)||this;function s(){r=0,n=[]}return o.identifier="TelemetryInitializerPlugin",o.priority=199,s(),(0,i.A)(t,o,function(e,t){e.addTelemetryInitializer=function(e){return function(e,t,r){var n={id:t,fn:r};return(0,a.Yny)(e,n),{remove:function(){(0,a.Iuo)(e,function(t,r){if(t.id===n.id)return e[l.Ic](r,1),-1})}}}(n,r++,e)},e[y.qT]=function(t,r){(function(e,t,r){for(var n=!1,o=e[l.oI],i=0;i<o;++i){var s=e[i];if(s)try{if(!1===s.fn[l.y9](null,[t])){n=!0;break}}catch(e){(0,h.ZP)(r,2,64,"Telemetry initializer failed: "+(0,v.lL)(e),{exception:(0,a.mmD)(e)},!0)}}return!n})(n,t,r?r[l.e4]():e[l.e4]())&&e[l.$5](t,r)},e[l.tn]=function(){s()}}),o}return(0,o.qU)(t,e),t.__ieDyn=1,t}(r(8257).s),P=r(836),S=r(8969),k="Plugins must provide initialize method",O="SDK is still unloading...",C=(0,a.ZHX)(((n={cookieCfg:{}})[y.jy]={rdOnly:!0,ref:!0,v:[]},n[y.LZ]={rdOnly:!0,ref:!0,v:[]},n[y.Bw]={ref:!0,v:{}},n[y.Yd]=y.HP,n.loggingLevelConsole=0,n.diagnosticLogInterval=y.HP,n));function R(e,t){return new E.NS(t)}function A(e,t){var r=!1;return(0,a.Iuo)(t,function(t){if(t===e)return r=!0,-1}),r}function I(e,t,r,n){r&&(0,a.zav)(r,function(r,o){n&&(0,a.QdQ)(o)&&(0,a.QdQ)(t[r])&&I(e,t[r],o,n),n&&(0,a.QdQ)(o)&&(0,a.QdQ)(t[r])?I(e,t[r],o,n):e.set(t,r,o)})}function D(e,t){var r=null,n=-1;return(0,a.Iuo)(e,function(e,o){if(e.w===t)return r=e,n=o,-1}),{i:n,l:r}}var G=function(){function e(){var t,r,n,G,U,F,L,x,H,j,M,N,$,q,z,B,V,X,W,K,Z,J,Q,Y,ee,te,re,ne,oe,ie,se,ae;(0,i.A)(e,this,function(e){function i(){oe=!0,(0,a.hXl)(Z)?(te=u.f[l.Yq],(0,h.ZP)(n,1,112,"ikey can't be resolved from promises")):te=u.f.ACTIVE,ce()}function ce(){r&&(e.releaseQueue(),e[l.h4]())}function ue(e){return ie&&ie[l.XM]||ae||(e||n&&n.queue[l.oI]>0)&&(se||(se=!0,we(t[l.x6](function(e){var t=e.cfg.diagnosticLogInterval;t&&t>0||(t=1e4);var r=!1;ie&&(r=ie[l.XM],ie[l._w]()),(ie=(0,a.AHH)(ve,t)).unref(),ie[l.XM]=r}))),ie[l.XM]=!0),ie}function le(){var e={};Y=[];var t=function(t){t&&(0,a.Iuo)(t,function(t){if(t[l.Ju]&&t[l.s]&&!e[t.identifier]){var r=t[l.Ju]+"="+t[l.s];Y[l.y5](r),e[t.identifier]=t}})};t(N),M&&(0,a.Iuo)(M,function(e){t(e)}),t(j)}function pe(){r=!1,(t=(0,c.e)({},C,e[l.Uw])).cfg[l.Bl]=1,(0,a.vF1)(e,"config",{g:function(){return t.cfg},s:function(t){e.updateCfg(t,!1)}}),(0,a.vF1)(e,"pluginVersionStringArr",{g:function(){return Y||le(),Y}}),(0,a.vF1)(e,"pluginVersionString",{g:function(){return ee||(Y||le(),ee=Y.join(";")),ee||y.m5}}),(0,a.vF1)(e,"logger",{g:function(){return n||(n=new h.wq(t.cfg),t[l.Uw]=n),n},s:function(e){t[l.Uw]=e,n!==e&&((0,p.K)(n,!1),n=e)}}),e[l.Uw]=new h.wq(t.cfg),Q=[];var o=e.config[y.jy]||[];o.splice(0,o[l.oI]),(0,a.Yny)(o,Q),q=new _,G=[],(0,p.K)(U,!1),U=null,F=null,L=null,(0,p.K)(x,!1),x=null,H=null,j=[],M=null,N=null,$=!1,z=null,B=(0,g.Z)("AIBaseCore",!0),V=(0,P.P)(),K=null,Z=null,X=(0,S.w)(),J=[],ee=null,Y=null,ae=!1,ie=null,se=!1,te=0,re=null,ne=null,oe=!1}function de(){var r=(0,T.i8)(me(),t.cfg,e);return r[l.by](ue),r}function fe(t){var r=function(e,t,r){var n,o=[],i=[],s={};return(0,a.Iuo)(r,function(r){((0,a.hXl)(r)||(0,a.hXl)(r[l.mE]))&&(0,a.$8)(k);var n=r[y.Vo],c=r[l.Ju];r&&n&&((0,a.hXl)(s[n])?s[n]=c:(0,h.OG)(e,"Two extensions have same priority #"+n+" - "+s[n]+", "+c)),!n||n<t?o[l.y5](r):i[l.y5](r)}),(n={})[y.eT]=o,n[y.LZ]=i,n}(e[l.Uw],d.i,j);H=null,ee=null,Y=null,N=(M||[])[0]||[],N=(0,w.Xc)((0,a.Yny)(N,r[y.LZ]));var n=(0,a.Yny)((0,w.Xc)(r[y.eT]),N);Q=(0,a.N6t)(n);var o=e.config[y.jy]||[];o.splice(0,o[l.oI]),(0,a.Yny)(o,Q);var i=de();N&&N[l.oI]>0&&(0,w.pI)(i[l.$o](N),n),(0,w.pI)(i,n),t&&be(t)}function ge(e){var t=null,r=null,n=[];return(0,a.Iuo)(Q,function(t){if(t[l.Ju]===e&&t!==q)return r=t,-1;t.getChannel&&n[l.y5](t)}),!r&&n[l.oI]>0&&(0,a.Iuo)(n,function(t){if(!(r=t.getChannel(e)))return-1}),r&&(t={plugin:r,setEnabled:function(e){(0,w.Cr)(r)[y.Hr]=!e},isEnabled:function(){var e=(0,w.Cr)(r);return!e[l.Ik]&&!e[y.Hr]},remove:function(e,t){var n;void 0===e&&(e=!0);var o=[r],i=((n={reason:1})[l.tI]=e,n);he(o,i,function(e){e&&fe({reason:32,removed:o}),t&&t(e)})}}),t}function me(){if(!H){var r=(Q||[]).slice();-1===(0,a.rDm)(r,q)&&r[l.y5](q),H=(0,T.PV)((0,w.Xc)(r),t.cfg,e)}return H}function he(r,n,o){if(r&&r[l.oI]>0){var i=(0,T.PV)(r,t.cfg,e),s=(0,T.tS)(i,e);s[l.by](function(){var e=!1,t=[];(0,a.Iuo)(j,function(n,o){A(n,r)?e=!0:t[l.y5](n)}),j=t,ee=null,Y=null;var n=[];M&&((0,a.Iuo)(M,function(t,o){var i=[];(0,a.Iuo)(t,function(t){A(t,r)?e=!0:i[l.y5](t)}),n[l.y5](i)}),M=n),o&&o(e),ue()}),s[l.$5](n)}else o(!1)}function ve(){if(n&&n.queue){var t=n.queue.slice(0);n.queue[l.oI]=0,(0,a.Iuo)(t,function(t){var r,n=((r={})[l.RS]=z||"InternalMessageId: "+t[l.JR],r[l.FI]=Z,r[l.fA]=(0,v._u)(new Date),r.baseType=h.WD.dataType,r.baseData={message:t[l.pM]},r);e.track(n)})}}function ye(e,t,r,n){var o=1,i=!1,s=null;function c(){o--,i&&0===o&&(s&&s[l._w](),s=null,t&&t(i),t=null)}return n=n||5e3,N&&N[l.oI]>0&&de()[l.$o](N).iterate(function(t){if(t.flush){o++;var i=!1;t.flush(e,function(){i=!0,c()},r)||i||(e&&null==s?s=(0,a.dRz)(function(){s=null,c()},n):c())}}),i=!0,c(),!0}function be(t){var r=(0,T.nU)(me(),e);r[l.by](ue),e._updateHook&&!0===e._updateHook(r,t)||r[l.$5](t)}function Ee(t){var r=e[l.Uw];r?((0,h.ZP)(r,2,73,t),ue()):(0,a.$8)(t)}function Te(t){var r=e[l.RF]();r&&r[y.Yp]([t],2)}function we(e){X.add(e)}pe(),e._getDbgPlgTargets=function(){return[Q,G]},e[l.tZ]=function(){return r},e.activeStatus=function(){return te},e._setPendingStatus=function(){te=3},e[l.mE]=function(p,d,f,g){var b;$&&(0,a.$8)(O),e[l.tZ]()&&(0,a.$8)("Core cannot be initialized more than once"),t=(0,c.e)(p,C,f||e[l.Uw],!1),p=t.cfg,we(t[l.x6](function(e){var t=e.cfg;if(3!==te){ne=t.initInMemoMaxSize||100;var o=t[l.sl],c=t.endpointUrl;if((0,a.hXl)(o)){Z=null,te=u.f[l.Yq];var d="Please provide instrumentation key";r?((0,h.ZP)(n,1,100,d),ce()):(0,a.$8)(d)}else{var f=[];if((0,a.$XS)(o)?(f[l.y5](o),Z=null):Z=o,(0,a.$XS)(c)?(f[l.y5](c),re=null):re=c,f[l.oI]){oe=!1,te=3;var g=(0,v.Gh)(t.initTimeOut)?t.initTimeOut:5e4,m=(0,s.lh)(f);(0,a.dRz)(function(){oe||i()},g),(0,s.Dv)(m,function(e){try{if(oe)return;if(!e.rejected){var t=e[l.pF];if(t&&t[l.oI]){var r=t[0];if(Z=r&&r[l.pF],t[l.oI]>1){var n=t[1];re=n&&n[l.pF]}}Z&&(p[l.sl]=Z,p.endpointUrl=re)}i()}catch(e){oe||i()}})}else i();var b=e.ref(e.cfg,y.Bw);(0,a.zav)(b,function(t){e.ref(b,t)})}}})),W=function(e,t,r,n){return t.add(e[l.x6](function(e){var t=e.cfg.disableDbgExt;!0===t&&n&&(r[l.h3](n),n=null),r&&!n&&!0!==t&&(n=(0,m.M)(e.cfg),r[l.vR](n))})),n}(t,X,(U=g)&&e[l.RF](),W),we(t[l.x6](function(t){if(t.cfg.enablePerfMgr){var r=t.cfg[y.Yd];b===r&&b||(r||(r=R),(0,v.c2)(t.cfg,y.Yd,r),b=r,L=null),F||L||!(0,a.Tnt)(r)||(L=r(e,e[l.RF]()))}else L=null,b=null})),e[l.Uw]=f;var E=p[y.jy];if((j=[])[l.y5].apply(j,(0,o.vz)((0,o.vz)([],d,!1),E,!1)),M=p[y.LZ],fe(null),N&&0!==N[l.oI]||(0,a.$8)("No "+y.LZ+" available"),M&&M[l.oI]>1){var T=e[l.AP]("TeeChannelController");T&&T.plugin||(0,h.ZP)(n,1,28,"TeeChannel required")}!function(e,t,r){(0,a.Iuo)(t,function(t){var n=(0,c.a)(e,t.w,r);delete t.w,t.rm=function(){n.rm()}})}(p,J,n),J=null,r=!0,te===u.f.ACTIVE&&ce()},e.getChannels=function(){var e=[];return N&&(0,a.Iuo)(N,function(t){e[l.y5](t)}),(0,a.N6t)(e)},e.track=function(t){(0,E.r2)(e[y.kI](),function(){return"AppInsightsCore:track"},function(){null===t&&(Te(t),(0,a.$8)("Invalid telemetry item")),!t[l.RS]&&(0,a.hXl)(t[l.RS])&&(Te(t),(0,a.$8)("telemetry name required")),t[l.FI]=t[l.FI]||Z,t[l.fA]=t[l.fA]||(0,v._u)(new Date),t.ver=t.ver||"4.0",!$&&e[l.tZ]()&&te===u.f.ACTIVE?de()[l.$5](t):te!==u.f[l.Yq]&&G[l.oI]<=ne&&G[l.y5](t)},function(){return{item:t}},!t.sync)},e[l.DI]=de,e[l.RF]=function(){return U||(U=new b.h(t.cfg),e._notificationManager=U),U},e[l.vR]=function(t){e.getNotifyMgr()[l.vR](t)},e[l.h3]=function(e){U&&U[l.h3](e)},e.getCookieMgr=function(){return x||(x=(0,f.xN)(t.cfg,e[l.Uw])),x},e.setCookieMgr=function(e){x!==e&&((0,p.K)(x,!1),x=e)},e[y.kI]=function(){return F||L||(0,E.Z4)()},e.setPerfMgr=function(e){F=e},e.eventCnt=function(){return G[l.oI]},e.releaseQueue=function(){if(r&&G[l.oI]>0){var e=G;G=[],2===te?(0,a.Iuo)(e,function(e){e[l.FI]=e[l.FI]||Z,de()[l.$5](e)}):(0,h.ZP)(n,2,20,"core init status is not active")}},e[l.h4]=function(e){return z=e||null,ae=!1,ie&&ie[l._w](),ue(!0)},e[l.Di]=function(){ae=!0,ie&&ie[l._w](),ve()},(0,v.o$)(e,function(){return q},["addTelemetryInitializer"]),e[l.M5]=function(t,o,i){var c;void 0===t&&(t=!0),r||(0,a.$8)("SDK is not initialized"),$&&(0,a.$8)(O);var u,d=((c={reason:50})[l.tI]=t,c.flushComplete=!1,c);t&&!o&&(u=(0,s.Qo)(function(e){o=e}));var f=(0,T.tS)(me(),e);function g(t){d.flushComplete=t,$=!0,V.run(f,d),e[l.Di](),f[l.$5](d)}return f[l.by](function(){X.run(e[l.Uw]),(0,p.k)([x,U,n],t,function(){pe(),o&&o(d)})},e),ve(),ye(t,g,6,i)||g(!1),u},e[l.AP]=ge,e.addPlugin=function(e,t,r,n){if(!e)return n&&n(!1),void Ee(k);var o=ge(e[l.Ju]);if(o&&!t)return n&&n(!1),void Ee("Plugin ["+e[l.Ju]+"] is already loaded!");var i={reason:16};function s(t){j[l.y5](e),i.added=[e],fe(i),n&&n(!0)}if(o){var a=[o.plugin];he(a,{reason:2,isAsync:!!r},function(e){e?(i.removed=a,i.reason|=32,s()):n&&n(!1)})}else s()},e.updateCfg=function(r,n){var o;if(void 0===n&&(n=!0),e[l.tZ]()){o={reason:1,cfg:t.cfg,oldCfg:(0,a.zwS)({},t.cfg),newConfig:(0,a.zwS)({},r),merge:n},r=o.newConfig;var i=t.cfg;r[y.jy]=i[y.jy],r[y.LZ]=i[y.LZ]}t._block(function(e){var t=e.cfg;I(e,t,r,n),n||(0,a.zav)(t,function(n){(0,a.KhI)(r,n)||e.set(t,n,y.HP)}),e[l.h0](t,C)},!0),t[l.zs](),o&&be(o)},e.evtNamespace=function(){return B},e.flush=ye,e.getTraceCtx=function(e){return K||(K=(0,w.u7)()),K},e.setTraceCtx=function(e){K=e||null},e.addUnloadHook=we,(0,v.RF)(e,"addUnloadCb",function(){return V},"add"),e.onCfgChange=function(n){var o,i,s,u;return r?o=(0,c.a)(t.cfg,n,e[l.Uw]):((u=D(i=J,s=n).l)||(u={w:s,rm:function(){var e=D(i,s);-1!==e.i&&i[l.Ic](e.i,1)}},i[l.y5](u)),o=u),function(e){return(0,a.vF1)({rm:function(){e.rm()}},"toJSON",{v:function(){return"aicore::onCfgChange<"+JSON.stringify(e)+">"}})}(o)},e.getWParam=function(){return(0,a.Wtk)()||t.cfg.enableWParam?0:-1}})}return e.__ieDyn=1,e}()},2910:(e,t,r)=>{"use strict";r.d(t,{a:()=>n});var n=(0,r(4282).o)({requestContextHeader:[0,"Request-Context"],requestContextTargetKey:[1,"appId"],requestContextAppIdFormat:[2,"appId=cid-v1:"],requestIdHeader:[3,"Request-Id"],traceParentHeader:[4,"traceparent"],traceStateHeader:[5,"tracestate"],sdkContextHeader:[6,"Sdk-Context"],sdkContextHeaderAppIdRequest:[7,"appId"],requestContextHeaderLowerCase:[8,"request-context"]})},2988:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GitHubCanonicalUriProvider=void 0;const n=r(1398),o=["ssh","https","file"];function i(e){if("ssh"===e.scheme&&"**************"===e.authority){const[t,r]=(e.path.endsWith(".git")?e.path.slice(0,-4):e.path).split("/").filter(e=>e.length>0);return n.Uri.parse(`https://github.com/${t}/${r}`)}if("https"===e.scheme&&"github.com"===e.authority)return e}t.GitHubCanonicalUriProvider=class{constructor(e){this.gitApi=e,this.disposables=[],this.disposables.push(...o.map(e=>n.workspace.registerCanonicalUriProvider(e,this)))}dispose(){this.disposables.forEach(e=>e.dispose())}provideCanonicalUri(e,t,r){if("https"===t.targetScheme){if("file"===e.scheme){const t=this.gitApi.getRepository(e),r=t?.state.remotes.find(e=>e.name===t.state.HEAD?.remote)?.pushUrl?.replace(/^(git@[^\/:]+)(:)/i,"ssh://$1/");if(r)return i(e)}return i(e)}}}},3072:(e,t,r)=>{"use strict";r.d(t,{J:()=>s});var n=r(5025),o=r(5130),i=r(7975),s=function(){function e(e,t,r,s){this.aiDataContract={ver:1,name:1,properties:0,measurements:0};var a=this;a.ver=2,a[o.RS]=(0,i.Rr)(e,t)||n.R2,a[o.$y]=(0,i.xP)(e,r),a[o.XA]=(0,i.Vj)(e,s)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Event",e.dataType="EventData",e}()},3662:(e,t,r)=>{"use strict";r.d(t,{x:()=>o});var n=r(4282),o=(0,n.H)({Unknown:0,NonRetryableStatus:1,InvalidEvent:2,SizeLimitExceeded:3,KillSwitch:4,QueueFull:5});(0,n.H)({Unknown:0,NonRetryableStatus:1,CleanStorage:2,MaxInStorageTimeExceeded:3})},3673:(e,t,r)=>{"use strict";r.d(t,{CP:()=>_,Gh:()=>l,H$:()=>I,HU:()=>S,IL:()=>D,Ju:()=>d,KY:()=>m,LU:()=>G,Lo:()=>P,RF:()=>b,SZ:()=>T,_u:()=>f,c2:()=>h,cH:()=>p,hW:()=>w,jL:()=>O,lL:()=>g,o$:()=>E,qz:()=>y,r4:()=>k,w3:()=>x});var n=r(269),o=r(5664),i=r(6182),s=r(6492),a=/-([a-z])/g,c=/([^\w\d_$])/g,u=/^(\d+[\w\d_$])/;function l(e){return!(0,n.hXl)(e)}function p(e){var t=e;return t&&(0,n.KgX)(t)&&(t=(t=(t=t[i.W7](a,function(e,t){return t.toUpperCase()}))[i.W7](c,"_"))[i.W7](u,function(e,t){return"_"+t})),t}function d(e,t){return!(!e||!t)&&-1!==(0,n.HzD)(e,t)}function f(e){return e&&e.toISOString()||""}function g(e){return(0,n.bJ7)(e)?e[i.RS]:s.m5}function m(e,t,r,n,o){var i=r;return e&&((i=e[t])===r||o&&!o(i)||n&&!n(r)||(i=r,e[t]=i)),i}function h(e,t,r){var o;return e?!(o=e[t])&&(0,n.hXl)(o)&&(o=(0,n.b07)(r)?{}:r,e[t]=o):o=(0,n.b07)(r)?{}:r,o}function v(e,t){var r=null,o=null;return(0,n.Tnt)(e)?r=e:o=e,function(){var e=arguments;if(r&&(o=r()),o)return o[t][i.y9](o,e)}}function y(e,t,r){if(e&&t&&(0,n.Gvm)(e)&&(0,n.Gvm)(t)){var o=function(o){if((0,n.KgX)(o)){var i=t[o];(0,n.Tnt)(i)?r&&!r(o,!0,t,e)||(e[o]=v(t,o)):r&&!r(o,!1,t,e)||((0,n.KhI)(e,o)&&delete e[o],(0,n.vF1)(e,o,{g:function(){return t[o]},s:function(e){t[o]=e}}))}};for(var i in t)o(i)}return e}function b(e,t,r,o,i){e&&t&&r&&(!1!==i||(0,n.b07)(e[t]))&&(e[t]=v(r,o))}function E(e,t,r,o){return e&&t&&(0,n.Gvm)(e)&&(0,n.cyL)(r)&&(0,n.Iuo)(r,function(r){(0,n.KgX)(r)&&b(e,r,t,r,o)}),e}function T(e){return function(){var t=this;e&&(0,n.zav)(e,function(e,r){t[e]=r})}}function w(e){return e&&n.vE3&&(e=(0,o.s6)((0,n.vE3)({},e))),e}function _(e,t,r,o,s,a){var c=arguments,u=c[0]||{},l=c[i.oI],p=!1,d=1;for(l>0&&(0,n.Lmq)(u)&&(p=u,u=c[d]||{},d++),(0,n.Gvm)(u)||(u={});d<l;d++){var f=c[d],g=(0,n.cyL)(f),m=(0,n.Gvm)(f);for(var h in f)if(g&&h in f||m&&(0,n.KhI)(f,h)){var v=f[h],y=void 0;if(p&&v&&((y=(0,n.cyL)(v))||(0,n.QdQ)(v))){var b=u[h];y?(0,n.cyL)(b)||(b=[]):(0,n.QdQ)(b)||(b={}),v=_(p,b,v)}void 0!==v&&(u[h]=v)}}return u}function P(e){try{return e.responseText}catch(e){}return null}function S(e,t){return e?"XDomainRequest,Response:"+P(e)||0:t}function k(e,t){return e?"XMLHttpRequest,Status:"+e[i.cV]+",Response:"+P(e)||0:t}function O(e,t){return t&&((0,n.EtT)(t)?e=[t].concat(e):(0,n.cyL)(t)&&(e=t.concat(e))),e}Object.getPrototypeOf;var C="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",R="withCredentials",A="timeout";function I(e,t,r,n,o,i){function s(e,t,r){try{e[t]=r}catch(e){}}void 0===n&&(n=!1),void 0===o&&(o=!1);var a=new XMLHttpRequest;return n&&s(a,C,n),r&&s(a,R,r),a.open(e,t,!o),r&&s(a,R,r),!o&&i&&s(a,A,i),a}function D(e){var t={};if((0,n.KgX)(e)){var r=(0,n.EHq)(e)[i.sY](/[\r\n]+/);(0,n.Iuo)(r,function(e){if(e){var r=e.indexOf(": ");if(-1!==r){var o=(0,n.EHq)(e.substring(0,r))[i.OL](),s=(0,n.EHq)(e.substring(r+1));t[o]=s}else t[(0,n.EHq)(e)]=1}})}return t}function G(e,t,r){if(!e[r]&&t&&t[i.Az]){var o=t[i.Az](r);o&&(e[r]=(0,n.EHq)(o))}return e}var U="kill-duration",F="kill-duration-seconds",L="time-delta-millis";function x(e,t){var r={};return e[i.wJ]?r=D(e[i.wJ]()):t&&(r=G(r,e,L),r=G(r,e,U),r=G(r,e,F)),r}},3698:(e,t,r)=>{"use strict";r.d(t,{E:()=>P});var n=r(5407),o=`octokit-endpoint.js/0.0.0-development ${(0,n.$)()}`;function i(e,t){const r=Object.assign({},e);return Object.keys(t).forEach(n=>{!function(e){if("object"!=typeof e||null===e)return!1;if("[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);if(null===t)return!0;const r=Object.prototype.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&Function.prototype.call(r)===Function.prototype.call(e)}(t[n])?Object.assign(r,{[n]:t[n]}):n in e?r[n]=i(e[n],t[n]):Object.assign(r,{[n]:t[n]})}),r}function s(e){for(const t in e)void 0===e[t]&&delete e[t];return e}function a(e,t,r){if("string"==typeof t){let[e,n]=t.split(" ");r=Object.assign(n?{method:e,url:n}:{url:e},r)}else r=Object.assign({},t);var n;r.headers=(n=r.headers)?Object.keys(n).reduce((e,t)=>(e[t.toLowerCase()]=n[t],e),{}):{},s(r),s(r.headers);const o=i(e||{},r);return"/graphql"===r.url&&(e&&e.mediaType.previews?.length&&(o.mediaType.previews=e.mediaType.previews.filter(e=>!o.mediaType.previews.includes(e)).concat(o.mediaType.previews)),o.mediaType.previews=(o.mediaType.previews||[]).map(e=>e.replace(/-preview/,""))),o}var c=/\{[^{}}]+\}/g;function u(e){return e.replace(/(?:^\W+)|(?:(?<!\W)\W+$)/g,"").split(/,/)}function l(e,t){const r={__proto__:null};for(const n of Object.keys(e))-1===t.indexOf(n)&&(r[n]=e[n]);return r}function p(e){return e.split(/(%[0-9A-Fa-f]{2})/g).map(function(e){return/%[0-9A-Fa-f]/.test(e)||(e=encodeURI(e).replace(/%5B/g,"[").replace(/%5D/g,"]")),e}).join("")}function d(e){return encodeURIComponent(e).replace(/[!'()*]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})}function f(e,t,r){return t="+"===e||"#"===e?p(t):d(t),r?d(r)+"="+t:t}function g(e){return null!=e}function m(e){return";"===e||"&"===e||"?"===e}function h(e,t){var r=["+","#",".","/",";","?","&"];return e=e.replace(/\{([^\{\}]+)\}|([^\{\}]+)/g,function(e,n,o){if(n){let e="";const o=[];if(-1!==r.indexOf(n.charAt(0))&&(e=n.charAt(0),n=n.substr(1)),n.split(/,/g).forEach(function(r){var n=/([^:\*]*)(?::(\d+)|(\*))?/.exec(r);o.push(function(e,t,r,n){var o=e[r],i=[];if(g(o)&&""!==o)if("string"==typeof o||"number"==typeof o||"boolean"==typeof o)o=o.toString(),n&&"*"!==n&&(o=o.substring(0,parseInt(n,10))),i.push(f(t,o,m(t)?r:""));else if("*"===n)Array.isArray(o)?o.filter(g).forEach(function(e){i.push(f(t,e,m(t)?r:""))}):Object.keys(o).forEach(function(e){g(o[e])&&i.push(f(t,o[e],e))});else{const e=[];Array.isArray(o)?o.filter(g).forEach(function(r){e.push(f(t,r))}):Object.keys(o).forEach(function(r){g(o[r])&&(e.push(d(r)),e.push(f(t,o[r].toString())))}),m(t)?i.push(d(r)+"="+e.join(",")):0!==e.length&&i.push(e.join(","))}else";"===t?g(o)&&i.push(d(r)):""!==o||"&"!==t&&"?"!==t?""===o&&i.push(""):i.push(d(r)+"=");return i}(t,e,n[1],n[2]||n[3]))}),e&&"+"!==e){var i=",";return"?"===e?i="&":"#"!==e&&(i=e),(0!==o.length?e:"")+o.join(i)}return o.join(",")}return p(o)}),"/"===e?e:e.replace(/\/$/,"")}function v(e){let t,r=e.method.toUpperCase(),n=(e.url||"/").replace(/:([a-z]\w+)/g,"{$1}"),o=Object.assign({},e.headers),i=l(e,["method","baseUrl","url","headers","request","mediaType"]);const s=function(e){const t=e.match(c);return t?t.map(u).reduce((e,t)=>e.concat(t),[]):[]}(n);var a;n=(a=n,{expand:h.bind(null,a)}).expand(i),/^http/.test(n)||(n=e.baseUrl+n);const p=l(i,Object.keys(e).filter(e=>s.includes(e)).concat("baseUrl"));if(!/application\/octet-stream/i.test(o.accept)&&(e.mediaType.format&&(o.accept=o.accept.split(/,/).map(t=>t.replace(/application\/vnd(\.\w+)(\.v3)?(\.\w+)?(\+json)?$/,`application/vnd$1$2.${e.mediaType.format}`)).join(",")),n.endsWith("/graphql")&&e.mediaType.previews?.length)){const t=o.accept.match(/(?<![\w-])[\w-]+(?=-preview)/g)||[];o.accept=t.concat(e.mediaType.previews).map(t=>`application/vnd.github.${t}-preview${e.mediaType.format?`.${e.mediaType.format}`:"+json"}`).join(",")}return["GET","HEAD"].includes(r)?n=function(e,t){const r=/\?/.test(e)?"&":"?",n=Object.keys(t);return 0===n.length?e:e+r+n.map(e=>"q"===e?"q="+t.q.split("+").map(encodeURIComponent).join("+"):`${e}=${encodeURIComponent(t[e])}`).join("&")}(n,p):"data"in p?t=p.data:Object.keys(p).length&&(t=p),o["content-type"]||void 0===t||(o["content-type"]="application/json; charset=utf-8"),["PATCH","PUT"].includes(r)&&void 0===t&&(t=""),Object.assign({method:r,url:n,headers:o},void 0!==t?{body:t}:null,e.request?{request:e.request}:null)}function y(e,t,r){return v(a(e,t,r))}var b=function e(t,r){const n=a(t,r),o=y.bind(null,n);return Object.assign(o,{DEFAULTS:n,defaults:e.bind(null,n),merge:a.bind(null,n),parse:v})}(null,{method:"GET",baseUrl:"https://api.github.com",headers:{accept:"application/vnd.github.v3+json","user-agent":o},mediaType:{format:""}}),E=r(6773);class T extends Error{name;status;request;response;constructor(e,t,r){super(e),this.name="HttpError",this.status=Number.parseInt(t),Number.isNaN(this.status)&&(this.status=0),"response"in r&&(this.response=r.response);const n=Object.assign({},r.request);r.request.headers.authorization&&(n.headers=Object.assign({},r.request.headers,{authorization:r.request.headers.authorization.replace(/(?<! ) .*$/," [REDACTED]")})),n.url=n.url.replace(/\bclient_secret=\w+/g,"client_secret=[REDACTED]").replace(/\baccess_token=\w+/g,"access_token=[REDACTED]"),this.request=n}}async function w(e){const t=e.request?.fetch||globalThis.fetch;if(!t)throw new Error("fetch is not set. Please pass a fetch implementation as new Octokit({ request: { fetch }}). Learn more at https://github.com/octokit/octokit.js/#fetch-missing");const r=e.request?.log||console,n=!1!==e.request?.parseSuccessResponseBody,o=function(e){if("object"!=typeof e||null===e)return!1;if("[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);if(null===t)return!0;const r=Object.prototype.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&Function.prototype.call(r)===Function.prototype.call(e)}(e.body)||Array.isArray(e.body)?JSON.stringify(e.body):e.body,i=Object.fromEntries(Object.entries(e.headers).map(([e,t])=>[e,String(t)]));let s;try{s=await t(e.url,{method:e.method,body:o,redirect:e.request?.redirect,headers:i,signal:e.request?.signal,...e.body&&{duplex:"half"}})}catch(t){let r="Unknown Error";if(t instanceof Error){if("AbortError"===t.name)throw t.status=500,t;r=t.message,"TypeError"===t.name&&"cause"in t&&(t.cause instanceof Error?r=t.cause.message:"string"==typeof t.cause&&(r=t.cause))}const n=new T(r,500,{request:e});throw n.cause=t,n}const a=s.status,c=s.url,u={};for(const[e,t]of s.headers)u[e]=t;const l={url:c,status:a,headers:u,data:""};if("deprecation"in u){const t=u.link&&u.link.match(/<([^<>]+)>; rel="deprecation"/),n=t&&t.pop();r.warn(`[@octokit/request] "${e.method} ${e.url}" is deprecated. It is scheduled to be removed on ${u.sunset}${n?`. See ${n}`:""}`)}if(204===a||205===a)return l;if("HEAD"===e.method){if(a<400)return l;throw new T(s.statusText,a,{response:l,request:e})}if(304===a)throw l.data=await _(s),new T("Not modified",a,{response:l,request:e});if(a>=400)throw l.data=await _(s),new T(function(e){if("string"==typeof e)return e;if(e instanceof ArrayBuffer)return"Unknown error";if("message"in e){const t="documentation_url"in e?` - ${e.documentation_url}`:"";return Array.isArray(e.errors)?`${e.message}: ${e.errors.map(e=>JSON.stringify(e)).join(", ")}${t}`:`${e.message}${t}`}return`Unknown error: ${JSON.stringify(e)}`}(l.data),a,{response:l,request:e});return l.data=n?await _(s):s.body,l}async function _(e){const t=e.headers.get("content-type");if(!t)return e.text().catch(()=>"");const r=(0,E.xL)(t);if(!function(e){return"application/json"===e.type||"application/scim+json"===e.type}(r))return r.type.startsWith("text/")||"utf-8"===r.parameters.charset?.toLowerCase()?e.text().catch(()=>""):e.arrayBuffer().catch(()=>new ArrayBuffer(0));{let t="";try{return t=await e.text(),JSON.parse(t)}catch(e){return t}}}var P=function e(t,r){const n=t.defaults(r);return Object.assign(function(t,r){const o=n.merge(t,r);if(!o.request||!o.request.hook)return w(n.parse(o));const i=(e,t)=>w(n.parse(n.merge(e,t)));return Object.assign(i,{endpoint:n,defaults:e.bind(null,n)}),o.request.hook(i,o)},{endpoint:n,defaults:e.bind(null,n)})}(b,{headers:{"user-agent":`octokit-request.js/0.0.0-development ${(0,n.$)()}`}})},3703:function(e,t,r){"use strict";var n,o=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(n=function(e){return n=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t},n(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=n(e),s=0;s<r.length;s++)"default"!==r[s]&&o(t,e,r[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.GithubPushErrorHandler=void 0,t.isInCodespaces=p,t.findPullRequestTemplates=g,t.pickPullRequestTemplate=m;const a=r(9023),c=r(1398),u=r(7171),l=s(r(6928));function p(){return"codespaces"===c.env.remoteName}const d=[{dir:".",files:["pull_request_template.md","PULL_REQUEST_TEMPLATE.md"]},{dir:"docs",files:["pull_request_template.md","PULL_REQUEST_TEMPLATE.md"]},{dir:".github",files:["PULL_REQUEST_TEMPLATE.md","PULL_REQUEST_TEMPLATE.md"]}],f=["PULL_REQUEST_TEMPLATE","docs/PULL_REQUEST_TEMPLATE",".github/PULL_REQUEST_TEMPLATE"];async function g(e){return(await Promise.allSettled([...d.map(t=>async function(e,t){return(await c.workspace.fs.readDirectory(e)).filter(([e,r])=>Boolean(r&c.FileType.File)&&-1!==t.indexOf(e)).map(([t])=>c.Uri.joinPath(e,t))}(c.Uri.joinPath(e,t.dir),t.files)),...f.map(t=>async function(e){return(await c.workspace.fs.readDirectory(e)).filter(([e,t])=>Boolean(t&c.FileType.File)&&".md"===l.extname(e)).map(([t])=>c.Uri.joinPath(e,t))}(c.Uri.joinPath(e,t)))])).flatMap(e=>"fulfilled"===e.status&&e.value||[])}async function m(e,t){const r=[{label:c.l10n.t("No template"),picked:!0,template:void 0},...t.map(t=>({label:l.relative(e.path,t.path),template:t}))],n={placeHolder:c.l10n.t("Select the Pull Request template"),ignoreFocusOut:!0},o=await c.window.showQuickPick(r,n);return o?.template}class h{constructor(){this.items=new Map}set(e,t){this.items.set(e.path,t)}delete(e){this.items.delete(e.path)}provideTextDocumentContent(e){return this.items.get(e.path)}}t.GithubPushErrorHandler=class{constructor(e){this.telemetryReporter=e,this.disposables=[],this.commandErrors=new h,this.disposables.push(c.workspace.registerTextDocumentContentProvider("github-output",this.commandErrors))}async handlePushError(e,t,r,n){if("PermissionDenied"!==n.gitErrorCode&&"PushRejected"!==n.gitErrorCode)return!1;const o=t.pushUrl||(p()?t.fetchUrl:void 0);if(!o)return!1;const i=/^(?:https:\/\/github\.com\/|git@github\.com:)([^\/]+)\/([^\/.]+)/i.exec(o);if(!i)return!1;if(/^:/.test(r))return!1;const[,s,a]=i;return"PermissionDenied"===n.gitErrorCode?(await this.handlePermissionDeniedError(e,t,r,s,a),this.telemetryReporter.sendTelemetryEvent("pushErrorHandler",{handler:"PermissionDenied"}),!0):/GH009: Secrets detected!/i.test(n.stderr)?(await this.handlePushProtectionError(s,a,n.stderr),this.telemetryReporter.sendTelemetryEvent("pushErrorHandler",{handler:"PushRejected.PushProtection"}),!0):(this.telemetryReporter.sendTelemetryEvent("pushErrorHandler",{handler:"None"}),!1)}async handlePermissionDeniedError(e,t,r,n,o){const i=c.l10n.t("Create Fork"),s=c.l10n.t("No"),l=c.l10n.t('You don\'t have permissions to push to "{0}/{1}" on GitHub. Would you like to create a fork and push to it instead?',n,o);if(await c.window.showWarningMessage(l,{modal:!0},i,s)!==i)return;const d=/^([^:]*):([^:]*)$/.exec(r),f=d?d[1]:r;let h=d?d[2]:r;const[v,y]=await c.window.withProgress({location:c.ProgressLocation.Notification,cancellable:!1,title:c.l10n.t("Create GitHub fork")},async r=>{r.report({message:c.l10n.t('Forking "{0}/{1}"...',n,o),increment:33});const i=await(0,u.getOctokit)();let s;try{if(p()){const e=await c.commands.executeCommand("github.codespaces.forkRepository");if(!e)throw new Error("Unable to fork respository");if(s=e.repository,e.ref){let t=e.ref;t.startsWith("refs/heads/")&&(t=t.substr(11)),h=t}}else s=(await i.repos.createFork({owner:n,repo:o})).data}catch(e){throw console.error(e),e}r.report({message:c.l10n.t("Pushing changes..."),increment:33}),await e.renameRemote(t.name,"upstream");const a="https"===c.workspace.getConfiguration("github").get("gitProtocol")?s.clone_url:s.ssh_url;await e.addRemote("origin",a);try{await e.fetch("origin",h),await e.setBranchUpstream(f,`origin/${h}`)}catch{}return await e.push("origin",f,!0),[i,s]});(async()=>{const t=c.l10n.t("Open on GitHub"),r=c.l10n.t("Create PR"),i=await c.window.showInformationMessage(c.l10n.t('The fork "{0}" was successfully created on GitHub.',y.full_name),t,r);if(i===t)await c.commands.executeCommand("vscode.open",c.Uri.parse(y.html_url));else if(i===r){const t=await c.window.withProgress({location:c.ProgressLocation.Notification,cancellable:!1,title:c.l10n.t("Creating GitHub Pull Request...")},async t=>{let r=`Update ${h}`;const i=e.state.HEAD?.name;let s;if(i){const t=await e.getCommit(i);r=t.message.split("\n")[0],s=t.message.slice(r.length+1).trim()}const u=await g(e.rootUri);if(u.length>0){u.sort((e,t)=>e.path.localeCompare(t.path));const t=await m(e.rootUri,u);t&&(s=new a.TextDecoder("utf-8").decode(await c.workspace.fs.readFile(t)))}const{data:l}=await v.pulls.create({owner:n,repo:o,title:r,body:s,head:`${y.owner.login}:${h}`,base:y.default_branch});return await e.setConfig(`branch.${f}.remote`,"upstream"),await e.setConfig(`branch.${f}.merge`,`refs/heads/${h}`),await e.setConfig(`branch.${f}.github-pr-owner-number`,`${n}#${o}#${l.number}`),l}),r=c.l10n.t("Open PR");await c.window.showInformationMessage(c.l10n.t('The PR "{0}/{1}#{2}" was successfully created on GitHub.',n,o,t.number),r)===r&&await c.commands.executeCommand("vscode.open",c.Uri.parse(t.html_url))}})()}async handlePushProtectionError(e,t,r){const n=(new Date).getTime(),o=c.Uri.parse(`github-output:/github-error-${n}`);this.commandErrors.set(o,r);try{const e=await c.workspace.openTextDocument(o);await c.window.showTextDocument(e)}finally{this.commandErrors.set(o,r)}const i=c.l10n.t("Learn More"),s=c.l10n.t('Your push to "{0}/{1}" was rejected by GitHub because push protection is enabled and one or more secrets were detected.',e,t);await c.window.showWarningMessage(s,{modal:!0},i)===i&&c.commands.executeCommand("vscode.open","https://aka.ms/vscode-github-push-protection")}dispose(){this.disposables.forEach(e=>e.dispose())}}},3775:(e,t,r)=>{"use strict";r.d(t,{OG:()=>T,Oc:()=>w,WD:()=>h,ZP:()=>E,wq:()=>y,y0:()=>v});var n,o=r(8279),i=r(269),s=r(9749),a=r(6182),c=r(7867),u=r(7292),l=r(6492),p="warnToConsole",d={loggingLevelConsole:0,loggingLevelTelemetry:1,maxMessageLimit:25,enableDebug:!1},f=((n={})[0]=null,n[1]="errorToConsole",n[2]=p,n[3]="debugToConsole",n);function g(e){return e?'"'+e[a.W7](/\"/g,l.m5)+'"':l.m5}function m(e,t){var r=(0,u.U5)();if(r){var n="log";r[e]&&(n=e),(0,i.Tnt)(r[n])&&r[n](t)}}var h=function(){function e(e,t,r,n){void 0===r&&(r=!1);var o=this;o[a.JR]=e,o[a.pM]=(r?"AI: ":"AI (Internal): ")+e;var i=l.m5;(0,u.Z)()&&(i=(0,u.hm)().stringify(n));var s=(t?" message:"+g(t):l.m5)+(n?" props:"+g(i):l.m5);o[a.pM]+=s}return e.dataType="MessageData",e}();function v(e,t){return(e||{})[a.Uw]||new y(t)}var y=function(){function e(t){this.identifier="DiagnosticLogger",this.queue=[];var r,n,u,l,g,v=0,y={};(0,o.A)(e,this,function(e){function o(t,r){if(!(v>=u)){var o=!0,i="AITR_"+r[a.JR];if(y[i]?o=!1:y[i]=!0,o&&(t<=n&&(e.queue[a.y5](r),v++,b(1===t?"error":"warn",r)),v===u)){var s="Internal events throttle limit per PageView reached for this app.",c=new h(23,s,!1);e.queue[a.y5](c),1===t?e.errorToConsole(s):e[a.on](s)}}}function b(e,r){var n=(0,c.$)(t||{});n&&n[a.e4]&&n[a.e4](e,r)}g=function(t){return(0,s.a)((0,s.e)(t,d,e).cfg,function(e){var t=e.cfg;r=t[a.Bl],n=t.loggingLevelTelemetry,u=t.maxMessageLimit,l=t.enableDebug})}(t||{}),e.consoleLoggingLevel=function(){return r},e[a.ih]=function(t,n,s,c,u){void 0===u&&(u=!1);var d=new h(n,s,u,c);if(l)throw(0,i.mmD)(d);var g=f[t]||p;if((0,i.b07)(d[a.pM]))b("throw"+(1===t?"Critical":"Warning"),d);else{if(u){var m=+d[a.JR];!y[m]&&r>=t&&(e[g](d[a.pM]),y[m]=!0)}else r>=t&&e[g](d[a.pM]);o(t,d)}},e.debugToConsole=function(e){m("debug",e),b("warning",e)},e[a.on]=function(e){m("warn",e),b("warning",e)},e.errorToConsole=function(e){m("error",e),b("error",e)},e.resetInternalMessageCount=function(){v=0,y={}},e[a.sx]=o,e[a.M5]=function(e){g&&g.rm(),g=null}})}return e.__ieDyn=1,e}();function b(e){return e||new y}function E(e,t,r,n,o,i){void 0===i&&(i=!1),b(e)[a.ih](t,r,n,o,i)}function T(e,t){b(e)[a.on](t)}function w(e,t,r){b(e)[a.sx](t,r)}},4013:(e,t,r)=>{"use strict";r.d(t,{K:()=>s,k:()=>a});var n=r(8205),o=r(269),i=r(6182);function s(e,t){if(e&&e[i.M5])return e[i.M5](t)}function a(e,t,r){var i;return r||(i=(0,n.Qo)(function(e){r=e})),e&&(0,o.R3R)(e)>0?(0,n.Dv)(s(e[0],t),function(){a((0,o.KVm)(e,1),t,r)}):r(),i}},4164:(e,t,r)=>{"use strict";r.d(t,{H:()=>s});var n=r(5025),o=r(5130),i=r(7975),s=function(){function e(e,t,r,s,a,c,u){this.aiDataContract={ver:1,name:0,url:0,duration:0,perfTotal:0,networkConnect:0,sentRequest:0,receivedResponse:0,domProcessing:0,properties:0,measurements:0};var l=this;l.ver=2,l.url=(0,i.pJ)(e,r),l[o.RS]=(0,i.Rr)(e,t)||n.R2,l[o.$y]=(0,i.xP)(e,a),l[o.XA]=(0,i.Vj)(e,c),u&&(l.domProcessing=u.domProcessing,l[o.qd]=u[o.qd],l.networkConnect=u.networkConnect,l.perfTotal=u.perfTotal,l[o.fd]=u[o.fd],l.sentRequest=u.sentRequest)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.PageviewPerformance",e.dataType="PageviewPerformanceData",e}()},4276:(e,t,r)=>{"use strict";r.d(t,{T:()=>f,Z:()=>d});var n=r(269),o=r(6182),i=r(3673),s=r(6492),a=r(6535),c="3.3.4",u="."+(0,a.Si)(6),l=0;function p(e){return 1===e[o.re]||9===e[o.re]||!+e[o.re]}function d(e,t){return void 0===t&&(t=!1),(0,i.cH)(e+l+++(t?"."+c:s.m5)+u)}function f(e){var t={id:d("_aiData-"+(e||s.m5)+"."+c),accept:function(e){return p(e)},get:function(e,r,o,s){var a=e[t.id];return a?a[(0,i.cH)(r)]:(s&&(a=function(e,t){var r=t[e.id];if(!r){r={};try{p(t)&&(0,n.vF1)(t,e.id,{e:!1,v:r})}catch(e){}}return r}(t,e),a[(0,i.cH)(r)]=o),o)},kill:function(e,t){if(e&&e[t])try{delete e[t]}catch(e){}}};return t}},4282:(e,t,r)=>{"use strict";r.d(t,{H:()=>o,o:()=>i});var n=r(269),o=n.WSA,i=n.fn0},4434:e=>{"use strict";e.exports=require("events")},4459:function(e,t,r){"use strict";var n,o=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(n=function(e){return n=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t},n(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=n(e),s=0;s<r.length;s++)"default"!==r[s]&&o(t,e,r[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.registerCommands=function(e){const t=new u.DisposableStore;return t.add(a.commands.registerCommand("github.publish",async()=>{try{(0,c.publishRepository)(e)}catch(e){a.window.showErrorMessage(e.message)}})),t.add(a.commands.registerCommand("github.copyVscodeDevLink",async t=>p(e,!0,t))),t.add(a.commands.registerCommand("github.copyVscodeDevLinkFile",async t=>p(e,!1,t))),t.add(a.commands.registerCommand("github.copyVscodeDevLinkWithoutRange",async t=>p(e,!0,t,!1))),t.add(a.commands.registerCommand("github.openOnGitHub",async(e,t)=>{const r=(0,l.getCommitLink)(e,t);a.env.openExternal(a.Uri.parse(r))})),t.add(a.commands.registerCommand("github.graph.openOnGitHub",async(t,r)=>{if(!t||!r)return;const n=e.repositories.find(e=>e.rootUri.fsPath===t.rootUri?.fsPath);n&&await d(n,r.id)})),t.add(a.commands.registerCommand("github.timeline.openOnGitHub",async(t,r)=>{if(!t.id||!r)return;const n=e.getRepository(r);n&&await d(n,t.id)})),t.add(a.commands.registerCommand("github.openOnVscodeDev",async()=>async function(e){try{const t=await(0,l.getLink)(e,!0,!1,(0,l.getVscodeDevHost)(),"headlink");return t?a.Uri.parse(t):void 0}catch(e){return void(e instanceof a.CancellationError||a.window.showErrorMessage(e.message))}}(e))),t};const a=s(r(1398)),c=r(8566),u=r(7937),l=r(2676);async function p(e,t,r,n=!0){try{const o=await(0,l.getLink)(e,t,!0,(0,l.getVscodeDevHost)(),"headlink",r,n);if(o)return a.env.clipboard.writeText(o)}catch(e){e instanceof a.CancellationError||a.window.showErrorMessage(e.message)}}async function d(e,t){const r=await e.getBranches({contains:t,remote:!0}),n=new Set(r.filter(e=>1===e.type&&e.remote).map(e=>e.remote)),o=e.state.remotes.filter(e=>n.has(e.name)&&e.fetchUrl&&(0,u.getRepositoryFromUrl)(e.fetchUrl));if(0===o.length)return void a.window.showInformationMessage(a.l10n.t("No GitHub remotes found that contain this commit."));const i=o.find(e=>"upstream"===e.name)??o.find(e=>"origin"===e.name)??o[0],s=(0,l.getCommitLink)(i.fetchUrl,t);a.env.openExternal(a.Uri.parse(s))}},4484:(e,t,r)=>{"use strict";r.d(t,{F:()=>u,H:()=>c});var n=r(269),o=r(5025),i=r(5130),s=";",a="=";function c(e){if(!e)return{};var t=e[i.sY](s),r=(0,n.KTd)(t,function(e,t){var r=t[i.sY](a);if(2===r[i.oI]){var n=r[0][i.OL](),o=r[1];e[n]=o}return e},{});if((0,n.cGk)(r)[i.oI]>0){if(r.endpointsuffix){var c=r.location?r.location+".":"";r[i.zV]=r[i.zV]||"https://"+c+"dc."+r.endpointsuffix}r[i.zV]=r[i.zV]||o._G,(0,n.Cv9)(r[i.zV],"/")&&(r[i.zV]=r[i.zV].slice(0,-1))}return r}var u={parse:c}},4658:(e,t,r)=>{"use strict";r.d(t,{AN:()=>T,BW:()=>v,Dt:()=>P,Nu:()=>g,Se:()=>y,T9:()=>w,_M:()=>b,iw:()=>h,tm:()=>E,v7:()=>S,vH:()=>_,vh:()=>m});var n=r(269),o=r(3775),i=r(3673),s=r(7374),a=r(5130),c=void 0,u=void 0,l="";function p(){return v()?d(s.eL.LocalStorage):null}function d(e){try{if((0,n.hXl)((0,n.mS$)()))return null;var t=(new Date)[a.xE](),r=(0,n.zS2)(e===s.eL.LocalStorage?"localStorage":"sessionStorage"),o=l+t;r.setItem(o,t);var i=r.getItem(o)!==t;if(r[a.AZ](o),!i)return r}catch(e){}return null}function f(){return T()?d(s.eL.SessionStorage):null}function g(){c=!1,u=!1}function m(e){l=e||""}function h(){c=v(!0),u=T(!0)}function v(e){return(e||void 0===c)&&(c=!!d(s.eL.LocalStorage)),c}function y(e,t){var r=p();if(null!==r)try{return r.getItem(t)}catch(t){c=!1,(0,o.ZP)(e,2,1,"Browser failed read of local storage. "+(0,i.lL)(t),{exception:(0,n.mmD)(t)})}return null}function b(e,t,r){var s=p();if(null!==s)try{return s.setItem(t,r),!0}catch(t){c=!1,(0,o.ZP)(e,2,3,"Browser failed write to local storage. "+(0,i.lL)(t),{exception:(0,n.mmD)(t)})}return!1}function E(e,t){var r=p();if(null!==r)try{return r[a.AZ](t),!0}catch(t){c=!1,(0,o.ZP)(e,2,5,"Browser failed removal of local storage item. "+(0,i.lL)(t),{exception:(0,n.mmD)(t)})}return!1}function T(e){return(e||void 0===u)&&(u=!!d(s.eL.SessionStorage)),u}function w(){var e=[];return T()&&(0,n.zav)((0,n.zS2)("sessionStorage"),function(t){e[a.y5](t)}),e}function _(e,t){var r=f();if(null!==r)try{return r.getItem(t)}catch(t){u=!1,(0,o.ZP)(e,2,2,"Browser failed read of session storage. "+(0,i.lL)(t),{exception:(0,n.mmD)(t)})}return null}function P(e,t,r){var s=f();if(null!==s)try{return s.setItem(t,r),!0}catch(t){u=!1,(0,o.ZP)(e,2,4,"Browser failed write to session storage. "+(0,i.lL)(t),{exception:(0,n.mmD)(t)})}return!1}function S(e,t){var r=f();if(null!==r)try{return r[a.AZ](t),!0}catch(t){u=!1,(0,o.ZP)(e,2,6,"Browser failed removal of session storage item. "+(0,i.lL)(t),{exception:(0,n.mmD)(t)})}return!1}},4756:e=>{"use strict";e.exports=require("tls")},4822:(e,t,r)=>{"use strict";r.d(t,{EO:()=>T,F2:()=>U,Go:()=>S,H$:()=>F,Hh:()=>_,P$:()=>b,Rx:()=>l,TC:()=>P,UM:()=>k,WB:()=>R,X$:()=>C,cq:()=>G,ei:()=>L,g8:()=>y,gj:()=>O,h3:()=>w,m0:()=>A,u9:()=>D,wJ:()=>I,xE:()=>p,yD:()=>E});var n,o=r(269),i=r(7292),s=r(9882),a=r(5664),c=r(937),u=r(1739),l="4.3.4",p="1DS-Web-JS-"+l,d=a.Wy.hasOwnProperty,f="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",g="withCredentials",m="timeout",h=((n={})[0]=0,n[2]=6,n[1]=1,n[3]=7,n[4098]=6,n[4097]=1,n[4099]=7,n),v=null,y=(0,o.Wtk)(),b=(0,o.Vdv)();function E(e){return!(e===c.m5||(0,o.hXl)(e))}function T(e){if(e){var t=(0,o.HzD)(e,"-");if(t>-1)return(0,o.ZWZ)(e,t)}return c.m5}function w(){return null===v&&(v=!(0,o.b07)(Uint8Array)&&!function(){var e=(0,o.w3n)();if(!(0,o.b07)(e)&&e.userAgent){var t=e.userAgent.toLowerCase();if((t[u.Sj]("safari")>=0||t[u.Sj]("firefox")>=0)&&t[u.Sj]("chrome")<0)return!0}return!1}()&&!(0,i.lV)()),v}function _(e){return!!(e&&(0,o.EtT)(e)&&e>=1&&e<=4)}function P(e,t,r){if(!t&&!E(t)||"string"!=typeof e)return null;var n=typeof t;if("string"===n||"number"===n||"boolean"===n||(0,o.cyL)(t))t={value:t};else if("object"!==n||d.call(t,"value")){if((0,o.hXl)(t[u.pF])||t[u.pF]===c.m5||!(0,o.KgX)(t[u.pF])&&!(0,o.EtT)(t[u.pF])&&!(0,o.Lmq)(t[u.pF])&&!(0,o.cyL)(t[u.pF]))return null}else t={value:r?JSON.stringify(t):t};if((0,o.cyL)(t[u.pF])&&!I(t[u.pF]))return null;if(!(0,o.hXl)(t[u.QV])){if((0,o.cyL)(t[u.pF])||!A(t[u.QV]))return null;t[u.pF]=t[u.pF].toString()}return t}function S(e,t,r){var n=-1;if(!(0,o.b07)(e))if(t>0&&(32===t?n=8192:t<=13&&(n=t<<5)),function(e){return e>=0&&e<=9}(r))-1===n&&(n=0),n|=r;else{var i=h[G(e)]||-1;-1!==n&&-1!==i?n|=i:6===i&&(n=i)}return n}function k(e,t,r){var n;return void 0===r&&(r=!0),e&&(n=e.get(t),r&&n&&decodeURIComponent&&(n=decodeURIComponent(n))),n||c.m5}function O(e){void 0===e&&(e="D");var t=(0,s.aq)();return"B"===e?t="{"+t+"}":"P"===e?t="("+t+")":"N"===e&&(t=t.replace(/-/g,c.m5)),t}function C(e,t,r,n,i){var s={},a=!1,c=0,l=arguments[u.oI],p=arguments;for((0,o.Lmq)(p[0])&&(a=p[0],c++);c<l;c++)e=p[c],(0,o.zav)(e,function(e,t){a&&t&&(0,o.Gvm)(t)?(0,o.cyL)(t)?(s[e]=s[e]||[],(0,o.Iuo)(t,function(t,r){t&&(0,o.Gvm)(t)?s[e][r]=C(!0,s[e][r],t):s[e][r]=t})):s[e]=C(!0,s[e],t):s[e]=t});return s}var R=o.UUD;function A(e){return 0===e||e>0&&e<=13||32===e}function I(e){return e[u.oI]>0}function D(e,t){var r=e;r[u.dg]=r[u.dg]||{},r[u.dg][u.Jg]=r[u.dg][u.Jg]||{},r[u.dg][u.Jg][t]=R()}function G(e){var t=0;if(null!=e){var r=typeof e;"string"===r?t=1:"number"===r?t=2:"boolean"===r?t=3:r===a._1&&(t=4,(0,o.cyL)(e)?(t=4096,e[u.oI]>0&&(t|=G(e[0]))):d.call(e,"value")&&(t=8192|G(e[u.pF])))}return t}function U(){return!!(0,o.zS2)("chrome")}function F(e,t,r,n,o,i){function s(e,t,r){try{e[t]=r}catch(e){}}void 0===n&&(n=!1),void 0===o&&(o=!1);var a=new XMLHttpRequest;return n&&s(a,f,n),r&&s(a,g,r),a.open(e,t,!o),r&&s(a,g,r),!o&&i&&s(a,m,i),a}function L(e){return e>0}},4875:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var n=(0,r(4282).H)({NONE:0,PENDING:3,INACTIVE:1,ACTIVE:2})},5014:(e,t,r)=>{"use strict";r.d(t,{J:()=>a});var n=r(5025),o=r(5130),i=function(){this.aiDataContract={name:1,kind:0,value:1,count:0,min:0,max:0,stdDev:0},this.kind=0},s=r(7975),a=function(){function e(e,t,r,a,c,u,l,p,d){this.aiDataContract={ver:1,metrics:1,properties:0};var f=this;f.ver=2;var g=new i;g[o.F2]=a>0?a:void 0,g.max=isNaN(u)||null===u?void 0:u,g.min=isNaN(c)||null===c?void 0:c,g[o.RS]=(0,s.Rr)(e,t)||n.R2,g.value=r,g.stdDev=isNaN(l)||null===l?void 0:l,f.metrics=[g],f[o.$y]=(0,s.xP)(e,p),f[o.XA]=(0,s.Vj)(e,d)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Metric",e.dataType="MetricData",e}()},5025:(e,t,r)=>{"use strict";r.d(t,{R2:()=>u,_G:()=>a,jp:()=>i,ks:()=>l,tU:()=>o,wc:()=>c,xF:()=>n,ym:()=>s});var n="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",o="sampleRate",i="ProcessLegacy",s="http.method",a="https://dc.services.visualstudio.com",c="/v2/track",u="not_specified",l="iKey"},5034:(e,t,r)=>{"use strict";r.d(t,{It:()=>j,gi:()=>G,um:()=>I,xN:()=>D});var n,o,i,s=r(269),a=r(2475),c=r(9749),u=r(6182),l=r(3775),p=r(7292),d=r(3673),f=r(6492),g="toGMTString",m="toUTCString",h="cookie",v="expires",y="isCookieUseDisabled",b="disableCookiesUsage",E="_ckMgr",T=null,w=null,_=null,P={},S={},k=((n={cookieCfg:(0,a.NU)((o={},o[f.Fk]={fb:"cookieDomain",dfVal:d.Gh},o.path={fb:"cookiePath",dfVal:d.Gh},o.enabled=f.HP,o.ignoreCookies=f.HP,o.blockedCookies=f.HP,o)),cookieDomain:f.HP,cookiePath:f.HP})[b]=f.HP,n);function O(){!i&&(i=(0,s.nRs)(function(){return(0,s.YEm)()}))}function C(e){return!e||e.isEnabled()}function R(e,t){return!!(t&&e&&(0,s.cyL)(e.ignoreCookies))&&-1!==(0,s.rDm)(e.ignoreCookies,t)}function A(e,t){var r=t[u.XM];if((0,s.hXl)(r)){var n=void 0;(0,s.b07)(e[y])||(n=!e[y]),(0,s.b07)(e[b])||(n=!e[b]),r=n}return r}function I(e,t){var r;if(e)r=e.getCookieMgr();else if(t){var n=t.cookieCfg;r=n&&n[E]?n[E]:D(t)}return r||(r=function(e,t){var r=D[E]||S[E];return r||(r=D[E]=D(e,t),S[E]=r),r}(t,(e||{})[u.Uw])),r}function D(e,t){var r,n,o,i,a,l,h,y,b;e=(0,c.e)(e||S,null,t).cfg,a=(0,c.a)(e,function(t){t[u.h0](t.cfg,k),n=t.ref(t.cfg,"cookieCfg"),o=n[f.QW]||"/",i=n[f.Fk],l=!1!==A(e,n),h=n.getCookie||x,y=n.setCookie||H,b=n.delCookie||H},t);var T=((r={isEnabled:function(){var r=!1!==A(e,n)&&l&&G(t),o=S[E];return r&&o&&T!==o&&(r=C(o)),r},setEnabled:function(e){l=!1!==e,n[u.XM]=e},set:function(e,t,r,a,c){var l=!1;if(C(T)&&!function(e,t){return!!(t&&e&&(0,s.cyL)(e.blockedCookies)&&-1!==(0,s.rDm)(e.blockedCookies,t))||R(e,t)}(n,e)){var h={},b=(0,s.EHq)(t||f.m5),E=(0,s.HzD)(b,";");if(-1!==E&&(b=(0,s.EHq)((0,s.ZWZ)(t,E)),h=U((0,s.P0f)(t,E+1))),(0,d.KY)(h,f.Fk,a||i,s.zzB,s.b07),!(0,s.hXl)(r)){var _=(0,p.lT)();if((0,s.b07)(h[v])){var P=(0,s.f0d)()+1e3*r;if(P>0){var S=new Date;S.setTime(P),(0,d.KY)(h,v,F(S,_?g:m)||F(S,_?g:m)||f.m5,s.zzB)}}_||(0,d.KY)(h,"max-age",f.m5+r,null,s.b07)}var k=(0,p.g$)();k&&"https:"===k[u.Qg]&&((0,d.KY)(h,"secure",null,null,s.b07),null===w&&(w=!j(((0,s.w3n)()||{})[u.tX])),w&&(0,d.KY)(h,"SameSite","None",null,s.b07)),(0,d.KY)(h,f.QW,c||o,null,s.b07),y(e,L(b,h)),l=!0}return l},get:function(e){var t=f.m5;return C(T)&&!R(n,e)&&(t=h(e)),t},del:function(e,t){var r=!1;return C(T)&&(r=T.purge(e,t)),r},purge:function(e,r){var n,o=!1;if(G(t)){var i=((n={})[f.QW]=r||"/",n[v]="Thu, 01 Jan 1970 00:00:01 GMT",n);(0,p.lT)()||(i["max-age"]="0"),b(e,L(f.m5,i)),o=!0}return o}})[u.M5]=function(e){a&&a.rm(),a=null},r);return T[E]=T,T}function G(e){if(null===T){T=!1,!i&&O();try{var t=i.v||{};T=void 0!==t[h]}catch(t){(0,l.ZP)(e,2,68,"Cannot access document.cookie - "+(0,d.lL)(t),{exception:(0,s.mmD)(t)})}}return T}function U(e){var t={};if(e&&e[u.oI]){var r=(0,s.EHq)(e)[u.sY](";");(0,s.Iuo)(r,function(e){if(e=(0,s.EHq)(e||f.m5)){var r=(0,s.HzD)(e,"=");-1===r?t[e]=null:t[(0,s.EHq)((0,s.ZWZ)(e,r))]=(0,s.EHq)((0,s.P0f)(e,r+1))}})}return t}function F(e,t){return(0,s.Tnt)(e[t])?e[t]():null}function L(e,t){var r=e||f.m5;return(0,s.zav)(t,function(e,t){r+="; "+e+((0,s.hXl)(t)?f.m5:"="+t)}),r}function x(e){var t=f.m5;if(!i&&O(),i.v){var r=i.v[h]||f.m5;_!==r&&(P=U(r),_=r),t=(0,s.EHq)(P[e]||f.m5)}return t}function H(e,t){!i&&O(),i.v&&(i.v[h]=e+"="+t)}function j(e){return!(!(0,s.KgX)(e)||!(0,d.Ju)(e,"CPU iPhone OS 12")&&!(0,d.Ju)(e,"iPad; CPU OS 12")&&!((0,d.Ju)(e,"Macintosh; Intel Mac OS X 10_14")&&(0,d.Ju)(e,"Version/")&&(0,d.Ju)(e,"Safari"))&&(!(0,d.Ju)(e,"Macintosh; Intel Mac OS X 10_14")||!(0,s.Cv9)(e,"AppleWebKit/605.1.15 (KHTML, like Gecko)"))&&!(0,d.Ju)(e,"Chrome/5")&&!(0,d.Ju)(e,"Chrome/6")&&(!(0,d.Ju)(e,"UnrealEngine")||(0,d.Ju)(e,"Chrome"))&&!(0,d.Ju)(e,"UCBrowser/12")&&!(0,d.Ju)(e,"UCBrowser/11"))}},5130:(e,t,r)=>{"use strict";r.d(t,{$e:()=>y,$y:()=>S,AZ:()=>u,Av:()=>L,C9:()=>v,Cx:()=>g,F2:()=>d,Fq:()=>A,IE:()=>U,J$:()=>O,Jj:()=>b,Jm:()=>f,OK:()=>_,OL:()=>i,Ol:()=>T,QE:()=>G,RS:()=>l,Ue:()=>E,Ur:()=>R,XA:()=>k,fd:()=>M,h_:()=>H,i9:()=>h,lW:()=>F,lx:()=>x,oI:()=>o,on:()=>P,pM:()=>p,qd:()=>j,qg:()=>C,r1:()=>I,sY:()=>n,up:()=>w,vu:()=>D,xE:()=>a,y5:()=>c,zV:()=>s,zw:()=>m});var n="split",o="length",i="toLowerCase",s="ingestionendpoint",a="toString",c="push",u="removeItem",l="name",p="message",d="count",f="preTriggerDate",g="disabled",m="interval",h="daysOfMonth",v="date",y="getUTCDate",b="stringify",E="pathname",T="correlationHeaderExcludePatterns",w="extensionConfig",_="exceptions",P="parsedStack",S="properties",k="measurements",O="sizeInBytes",C="typeName",R="severityLevel",A="problemGroup",I="isManual",D="CreateFromInterface",G="assembly",U="fileName",F="hasFullStack",L="level",x="method",H="line",j="duration",M="receivedResponse"},5183:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GithubRemoteSourceProvider=void 0;const n=r(1398),o=r(7171),i=r(7937),s=r(2676);function a(e){const t=n.workspace.getConfiguration("github").get("gitProtocol");return{name:`$(github) ${e.full_name}`,description:e.stargazers_count>0?`$(star-full) ${e.stargazers_count}`:"",detail:e.description||void 0,url:"https"===t?e.clone_url:e.ssh_url}}t.GithubRemoteSourceProvider=class{constructor(){this.name="GitHub",this.icon="github",this.supportsQuery=!0,this.userReposCache=[]}async getRemoteSources(e){const t=await(0,o.getOctokit)();if(e){const r=(0,i.getRepositoryFromUrl)(e);if(r)return[a((await t.repos.get(r)).data)]}const r=await Promise.all([this.getQueryRemoteSources(t,e),this.getUserRemoteSources(t,e)]),n=new Map;for(const e of r)for(const t of e)n.set(t.name,t);return[...n.values()]}async getUserRemoteSources(e,t){if(!t){const t=(await e.users.getAuthenticated({})).data.login,r=await e.repos.listForAuthenticatedUser({username:t,sort:"updated",per_page:100});this.userReposCache=r.data.map(a)}return this.userReposCache}async getQueryRemoteSources(e,t){if(!t)return[];const r=(0,i.getRepositoryFromQuery)(t);return r&&(t=`user:${r.owner}+${r.repo}`),t+=" fork:true",(await e.search.repos({q:t,sort:"stars"})).data.items.map(a)}async getBranches(e){const t=(0,i.getRepositoryFromUrl)(e);if(!t)return[];const r=await(0,o.getOctokit)(),n=[];let s=1;for(;;){const e=await r.repos.listBranches({...t,per_page:100,page:s});if(0===e.data.length)break;n.push(...e.data.map(e=>e.name)),s++}const a=(await r.repos.get(t)).data.default_branch;return n.sort((e,t)=>e===a?-1:t===a?1:0)}async getRemoteSourceActions(e){return(0,i.getRepositoryFromUrl)(e)?[{label:n.l10n.t("Open on GitHub"),icon:"github",run(t){const r=(0,s.getBranchLink)(e,t);n.env.openExternal(n.Uri.parse(r))}},{label:n.l10n.t("Checkout on vscode.dev"),icon:"globe",run(t){const r=(0,s.getBranchLink)(e,t,(0,s.getVscodeDevHost)());n.env.openExternal(n.Uri.parse(r))}}]:[]}}},5256:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.activate=function(e){const t=[];e.subscriptions.push(new o.Disposable(()=>o.Disposable.from(...t).dispose()));const n=o.window.createOutputChannel("GitHub",{log:!0});t.push(n);const h=e=>{n.appendLine(o.l10n.t("Log level: {0}",o.LogLevel[e]))};t.push(n.onDidChangeLogLevel(h)),h(n.logLevel);const{aiKey:v}=r(8330),y=new i.default(v);t.push(y),t.push(function(){const e=new u.DisposableStore,t=t=>{t?(()=>{try{const t=r.getAPI(1);e.add(t.registerRemoteSourceProvider(new s.GithubRemoteSourceProvider))}catch(e){console.error("Could not initialize GitHub extension"),console.warn(e)}})():e.dispose()},r=o.extensions.getExtension("vscode.git-base").exports;return e.add(r.onDidChangeEnablement(t)),t(r.enabled),e}()),t.push(function(e,t,r){const n=new u.DisposableStore;let i=o.extensions.getExtension("vscode.git");const s=()=>{i.activate().then(i=>{const s=s=>{if(s){const s=i.getAPI(1);n.add((0,a.registerCommands)(s)),n.add(new c.GithubCredentialProviderManager(s)),n.add(new d.GitHubBranchProtectionProviderManager(s,e.globalState,r,t)),n.add(s.registerPushErrorHandler(new l.GithubPushErrorHandler(t))),n.add(s.registerRemoteSourcePublisher(new p.GithubRemoteSourcePublisher(s))),n.add(s.registerSourceControlHistoryItemDetailsProvider(new m.GitHubSourceControlHistoryItemDetailsProvider(s,r))),n.add(new f.GitHubCanonicalUriProvider(s)),n.add(new g.VscodeDevShareProvider(s)),function(e,t){if(e.repositories.find(e=>(0,u.repositoryHasGitHubRemote)(e)))o.commands.executeCommand("setContext","github.hasGitHubRepo",!0);else{const r=e.onDidOpenRepository(async e=>{await e.status(),(0,u.repositoryHasGitHubRemote)(e)&&(o.commands.executeCommand("setContext","github.hasGitHubRepo",!0),r.dispose())});t.add(r)}}(s,n),o.commands.executeCommand("setContext","git-base.gitEnabled",!0)}else n.dispose()};n.add(i.onDidChangeEnablement(s)),s(i.enabled)})};if(i)s();else{const e=o.extensions.onDidChange(()=>{!i&&o.extensions.getExtension("vscode.git")&&(i=o.extensions.getExtension("vscode.git"),s(),e.dispose())});n.add(e)}return n}(e,y,n))};const o=r(1398),i=n(r(1170)),s=r(5183),a=r(4459),c=r(6805),u=r(7937),l=r(3703),p=r(2384),d=r(1558),f=r(2988),g=r(8262),m=r(8107)},5396:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.oneDataSystemClientFactory=void 0,t.oneDataSystemClientFactory=async(e,t,n)=>{let o=await(async(e,t,n)=>{const o=await Promise.resolve().then(()=>i(r(956))),s=await Promise.resolve().then(()=>i(r(8916))),a=new o.AppInsightsCore,c=new s.PostChannel,u={instrumentationKey:e,endpointUrl:"https://mobile.events.data.microsoft.com/OneCollector/1.0",loggingLevelTelemetry:0,loggingLevelConsole:0,disableCookiesUsage:!0,disableDbgExt:!0,disableInstrumentationKeyValidation:!0,channels:[[c]]};if(n){u.extensionConfig={};const e={alwaysUseXhrOverride:!0,httpXHROverride:n};u.extensionConfig[c.identifier]=e}const l=t.workspace.getConfiguration("telemetry").get("internalTesting");return a.initialize(u,[]),a.addTelemetryInitializer(e=>{e.ext=e.ext??{},e.ext.web=e.ext.web??{},e.ext.web.consentDetails='{"GPC_DataSharingOptIn":false}',l&&(e.ext.utc=e.ext.utc??{},e.ext.utc.flags=8462029)}),a})(e,t,n);return{logEvent:(e,t)=>{try{o?.track({name:e,baseData:{name:e,properties:t?.properties,measurements:t?.measurements}})}catch(e){throw new Error("Failed to log event to app insights!\n"+e.message)}},flush:async()=>{try{return new Promise((e,t)=>{o?o.flush(!0,e=>{e||t("Failed to flush app 1DS!")}):e()})}catch(e){throw new Error("Failed to flush 1DS!\n"+e.message)}},dispose:async()=>new Promise(e=>{o?o.unload(!1,()=>{e(),o=void 0},1e3):e()})}}},5397:(e,t,r)=>{"use strict";r.d(t,{WJ:()=>T});var n=r(659),o=r(269),i=r(5025),s=r(5130),a=r(7975),c="error",u="stack",l="stackDetails",p="errorSrc",d="message",f="description";function g(e,t){var r=e;return r&&!(0,o.KgX)(r)&&(JSON&&JSON[s.Jj]?(r=JSON[s.Jj](e),!t||r&&"{}"!==r||(r=(0,o.Tnt)(e[s.xE])?e[s.xE]():""+e)):r=e+" - (Missing JSON.stringify)"),r||""}function m(e,t){var r=e;return e&&(r&&!(0,o.KgX)(r)&&(r=e[d]||e[f]||r),r&&!(0,o.KgX)(r)&&(r=g(r,!0)),e.filename&&(r=r+" @"+(e.filename||"")+":"+(e.lineno||"?")+":"+(e.colno||"?"))),t&&"String"!==t&&"Object"!==t&&"Error"!==t&&-1===(0,o.HzD)(r||"",t)&&(r=t+": "+r),r||""}function h(e){return e&&e.src&&(0,o.KgX)(e.src)&&e.obj&&(0,o.cyL)(e.obj)}function v(e){var t=e||"";(0,o.KgX)(t)||(t=(0,o.KgX)(t[u])?t[u]:""+t);var r=t[s.sY]("\n");return{src:t,obj:r}}function y(e){var t=null;if(e)try{if(e[u])t=v(e[u]);else if(e[c]&&e[c][u])t=v(e[c][u]);else if(e.exception&&e.exception[u])t=v(e.exception[u]);else if(h(e))t=e;else if(h(e[l]))t=e[l];else if((0,o.zkX)()&&(0,o.zkX)().opera&&e[d])t=function(e){for(var t=[],r=e[s.sY]("\n"),n=0;n<r[s.oI];n++){var o=r[n];r[n+1]&&(o+="@"+r[n+1],n++),t[s.y5](o)}return{src:e,obj:t}}(e[s.pM]);else if(e.reason&&e.reason[u])t=v(e.reason[u]);else if((0,o.KgX)(e))t=v(e);else{var r=e[d]||e[f]||"";(0,o.KgX)(e[p])&&(r&&(r+="\n"),r+=" from "+e[p]),r&&(t=v(r))}}catch(e){t=v(e)}return t||{src:"",obj:null}}function b(e){var t="";if(e&&!(t=e.typeName||e[s.RS]||""))try{var r=/function (.{1,200})\(/.exec(e.constructor[s.xE]());t=r&&r[s.oI]>1?r[1]:""}catch(e){}return t}function E(e){if(e)try{if(!(0,o.KgX)(e)){var t=b(e),r=g(e,!1);return r&&"{}"!==r||(e[c]&&(t=b(e=e[c])),r=g(e,!0)),0!==(0,o.HzD)(r,t)&&"String"!==t?t+":"+r:r}}catch(e){}return""+(e||"")}var T=function(){function e(e,t,r,n,i,c){this.aiDataContract={ver:1,exceptions:1,severityLevel:0,properties:0,measurements:0};var u=this;u.ver=2,function(e){try{if((0,o.Gvm)(e))return"ver"in e&&"exceptions"in e&&"properties"in e}catch(e){}return!1}(t)?(u[s.OK]=t[s.OK]||[],u[s.$y]=t[s.$y],u[s.XA]=t[s.XA],t[s.Ur]&&(u[s.Ur]=t[s.Ur]),t.id&&(u.id=t.id,t[s.$y].id=t.id),t[s.Fq]&&(u[s.Fq]=t[s.Fq]),(0,o.hXl)(t[s.r1])||(u[s.r1]=t[s.r1])):(r||(r={}),c&&(r.id=c),u[s.OK]=[new w(e,t,r)],u[s.$y]=(0,a.xP)(e,r),u[s.XA]=(0,a.Vj)(e,n),i&&(u[s.Ur]=i),c&&(u.id=c))}return e.CreateAutoException=function(e,t,r,n,o,i,a,c){var u,l=b(o||i||e);return(u={})[s.pM]=m(e,l),u.url=t,u.lineNumber=r,u.columnNumber=n,u.error=E(o||i||e),u.evt=E(i||e),u[s.qg]=l,u.stackDetails=y(a||o||i),u.errorSrc=c,u},e.CreateFromInterface=function(t,r,i,a){var c=r[s.OK]&&(0,o.W$7)(r[s.OK],function(e){return w[s.vu](t,e)});return new e(t,(0,n.Im)((0,n.Im)({},r),{exceptions:c}),i,a)},e.prototype.toInterface=function(){var e,t=this,r=t.exceptions,n=t.properties,i=t.measurements,a=t.severityLevel,c=t.problemGroup,u=t.id,l=t.isManual,p=r instanceof Array&&(0,o.W$7)(r,function(e){return e.toInterface()})||void 0;return(e={ver:"4.0"})[s.OK]=p,e.severityLevel=a,e.properties=n,e.measurements=i,e.problemGroup=c,e.id=u,e.isManual=l,e},e.CreateSimpleException=function(e,t,r,n,o,i){var a;return{exceptions:[(a={},a[s.lW]=!0,a.message=e,a.stack=o,a.typeName=t,a)]}},e.envelopeType="Microsoft.ApplicationInsights.{0}.Exception",e.dataType="ExceptionData",e.formatError=E,e}(),w=function(){function e(e,t,r){this.aiDataContract={id:0,outerId:0,typeName:1,message:1,hasFullStack:0,stack:0,parsedStack:2};var n=this;if(function(e){try{if((0,o.Gvm)(e))return"hasFullStack"in e&&"typeName"in e}catch(e){}return!1}(t))n[s.qg]=t[s.qg],n[s.pM]=t[s.pM],n[u]=t[u],n[s.on]=t[s.on]||[],n[s.lW]=t[s.lW];else{var p=t,d=p&&p.evt;(0,o.bJ7)(p)||(p=p[c]||d||p),n[s.qg]=(0,a.Rr)(e,b(p))||i.R2,n[s.pM]=(0,a.Vk)(e,m(t||p,n[s.qg]))||i.R2;var f=t[l]||y(t);n[s.on]=function(e){var t,r=e.obj;if(r&&r[s.oI]>0){t=[];var n=0,i=0;if((0,o.Iuo)(r,function(e){var r=e[s.xE]();if(_.regex.test(r)){var o=new _(r,n++);i+=o[s.J$],t[s.y5](o)}}),i>32768)for(var a=0,c=t[s.oI]-1,u=0,l=a,p=c;a<c;){if((u+=t[a][s.J$]+t[c][s.J$])>32768){var d=p-l+1;t.splice(l,d);break}l=a,p=c,a++,c--}}return t}(f),(0,o.cyL)(n[s.on])&&(0,o.W$7)(n[s.on],function(t){t[s.QE]=(0,a.Rr)(e,t[s.QE]),t[s.IE]=(0,a.Rr)(e,t[s.IE])}),n[u]=(0,a.Vt)(e,function(e){var t="";return e&&(e.obj?(0,o.Iuo)(e.obj,function(e){t+=e+"\n"}):t=e.src||""),t}(f)),n.hasFullStack=(0,o.cyL)(n.parsedStack)&&n.parsedStack[s.oI]>0,r&&(r[s.qg]=r[s.qg]||n[s.qg])}}return e.prototype.toInterface=function(){var e,t=this,r=t[s.on]instanceof Array&&(0,o.W$7)(t[s.on],function(e){return e.toInterface()});return(e={id:t.id,outerId:t.outerId,typeName:t[s.qg],message:t[s.pM],hasFullStack:t[s.lW],stack:t[u]})[s.on]=r||void 0,e},e.CreateFromInterface=function(t,r){var i=r[s.on]instanceof Array&&(0,o.W$7)(r[s.on],function(e){return _[s.vu](e)})||r[s.on];return new e(t,(0,n.Im)((0,n.Im)({},r),{parsedStack:i}))},e}(),_=function(){function e(t,r){this.aiDataContract={level:1,method:1,assembly:0,fileName:0,line:0};var n=this;if(n[s.J$]=0,"string"==typeof t){var i=t;n[s.Av]=r,n[s.lx]="<no_method>",n[s.QE]=(0,o.EHq)(i),n[s.IE]="",n[s.h_]=0;var a=i.match(e.regex);a&&a[s.oI]>=5&&(n[s.lx]=(0,o.EHq)(a[2])||n[s.lx],n[s.IE]=(0,o.EHq)(a[4]),n[s.h_]=parseInt(a[5])||0)}else n[s.Av]=t[s.Av],n[s.lx]=t[s.lx],n[s.QE]=t[s.QE],n[s.IE]=t[s.IE],n[s.h_]=t[s.h_],n[s.J$]=0;n.sizeInBytes+=n.method[s.oI],n.sizeInBytes+=n.fileName[s.oI],n.sizeInBytes+=n.assembly[s.oI],n[s.J$]+=e.baseSize,n.sizeInBytes+=n.level.toString()[s.oI],n.sizeInBytes+=n.line.toString()[s.oI]}return e.CreateFromInterface=function(t){return new e(t,null)},e.prototype.toInterface=function(){var e=this;return{level:e[s.Av],method:e[s.lx],assembly:e[s.QE],fileName:e[s.IE],line:e[s.h_]}},e.regex=/^([\s]+at)?[\s]{0,50}([^\@\()]+?)[\s]{0,50}(\@|\()([^\(\n]+):([0-9]+):([0-9]+)(\)?)$/,e.baseSize=58,e}()},5407:(e,t,r)=>{"use strict";function n(){return"object"==typeof navigator&&"userAgent"in navigator?navigator.userAgent:"object"==typeof process&&void 0!==process.version?`Node.js/${process.version.substr(1)} (${process.platform}; ${process.arch})`:"<environment undetectable>"}r.d(t,{$:()=>n})},5571:(e,t,r)=>{"use strict";r.d(t,{G:()=>c});var n=r(6149),o=r(269),i=r(4276),s=r(5130);function a(e,t){(0,n.ML)(e,null,null,t)}function c(e){var t=(0,o.YEm)(),r=(0,o.w3n)(),c=!1,u=[],l=1;!r||(0,o.hXl)(r.onLine)||r.onLine||(l=2);var p=0,d=h(),f=(0,n.Hm)((0,i.Z)("OfflineListener"),e);try{if(m((0,o.zkX)())&&(c=!0),t){var g=t.body||t;g.ononline&&m(g)&&(c=!0)}}catch(e){c=!1}function m(e){var t=!1;return e&&(t=(0,n.mB)(e,"online",y,f))&&(0,n.mB)(e,"offline",b,f),t}function h(){return 2!==p&&2!==l}function v(){var e=h();d!==e&&(d=e,(0,o.Iuo)(u,function(e){var t={isOnline:d,rState:l,uState:p};try{e(t)}catch(e){}}))}function y(){l=1,v()}function b(){l=2,v()}return{isOnline:function(){return d},isListening:function(){return c},unload:function(){var e=(0,o.zkX)();if(e&&c){if(a(e,f),t){var r=t.body||t;(0,o.b07)(r.ononline)||a(r,f)}c=!1}},addListener:function(e){return u[s.y5](e),{rm:function(){var t=u.indexOf(e);return t>-1?u.splice(t,1):void 0}}},setOnlineState:function(e){p=e,v()}}}},5664:(e,t,r)=>{"use strict";r.d(t,{Wy:()=>c,_1:()=>o,bA:()=>i,hW:()=>n,s6:()=>a,vR:()=>s});var n="function",o="object",i="undefined",s="prototype",a=Object,c=a[s]},5692:e=>{"use strict";e.exports=require("https")},5927:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AppInsightsCore:()=>vt._,ApplicationInsights:()=>Ct,Sender:()=>mt,SeverityLevel:()=>St.O,arrForEach:()=>T.Iuo,isNullOrUndefined:()=>T.hXl,proxyFunctions:()=>E.o$,throwError:()=>T.$8});var n=r(8279),o=r(659),i=r(5025),s=r(3072),a=r(2445),c=r(1448),u=r(4164),l=r(5397),p=r(5014),d=r(1365),f=r(740),g=r(5571),m=r(4658),h=r(2318),v=r(2910),y=r(2475),b=r(3775),E=r(3673),T=r(269),w=r(6149),_=r(4276),P=r(9749),S=r(2317),k=r(4875),O=r(7292),C=r(856),R=r(4013),A=r(1190),I=r(8257),D=r(1575),G=r(7975),U=r(1062),F=r(7358),L="duration",x="tags",H="deviceType",j="data",M="name",N="traceID",$="length",q="stringify",z="measurements",B="dataType",V="envelopeType",X="toString",W="_get",K="enqueue",Z="count",J="eventsLimitInMem",Q="push",Y="item",ee="emitLineDelimitedJson",te="clear",re="createNew",ne="markAsSent",oe="clearSent",ie="bufferOverride",se="BUFFER_KEY",ae="SENT_BUFFER_KEY",ce="concat",ue="MAX_BUFFER_SIZE",le="triggerSend",pe="diagLog",de="initialize",fe="_sender",ge="endpointUrl",me="instrumentationKey",he="customHeaders",ve="maxBatchSizeInBytes",ye="onunloadDisableBeacon",be="isBeaconApiDisabled",Ee="alwaysUseXhrOverride",Te="disableXhr",we="enableSessionStorageBuffer",_e="_buffer",Pe="onunloadDisableFetch",Se="disableSendBeaconSplit",ke="enableSendPromise",Oe="getSenderInst",Ce="unloadTransports",Re="convertUndefined",Ae="maxBatchInterval",Ie="serialize",De="_onError",Ge="_onPartialSuccess",Ue="_onSuccess",Fe="itemsReceived",Le="itemsAccepted",xe="oriPayload",He="baseType",je="sampleRate",Me="eventsSendRequest",Ne="getSamplingScore",$e="baseType",qe="baseData",ze="properties",Be="true";function Ve(e,t,r){return(0,E.KY)(e,t,r,T.zzB)}function Xe(e,t,r){(0,T.hXl)(e)||(0,T.zav)(e,function(e,n){(0,T.EtT)(n)?r[e]=n:(0,T.KgX)(n)?t[e]=n:(0,O.Z)()&&(t[e]=(0,O.hm)()[q](n))})}function We(e,t){(0,T.hXl)(e)||(0,T.zav)(e,function(r,n){e[r]=n||t})}function Ke(e,t,r,n){var s=new U.L(e,n,t);Ve(s,"sampleRate",r[i.tU]),(r[qe]||{}).startTime&&(s.time=(0,E._u)(r[qe].startTime)),s.iKey=r.iKey;var a=r.iKey.replace(/-/g,"");return s[M]=s[M].replace("{0}",a),function(e,t,r){var n=r[x]=r[x]||{},i=t.ext=t.ext||{},s=t[x]=t[x]||[],a=i.user;a&&(Ve(n,D.O.userAuthUserId,a.authId),Ve(n,D.O.userId,a.id||a.localId));var c=i.app;c&&Ve(n,D.O.sessionId,c.sesId);var u=i.device;u&&(Ve(n,D.O.deviceId,u.id||u.localId),Ve(n,D.O[H],u.deviceClass),Ve(n,D.O.deviceIp,u.ip),Ve(n,D.O.deviceModel,u.model),Ve(n,D.O[H],u[H]));var l=t.ext.web;if(l){Ve(n,D.O.deviceLanguage,l.browserLang),Ve(n,D.O.deviceBrowserVersion,l.browserVer),Ve(n,D.O.deviceBrowser,l.browser);var p=r[j]=r[j]||{},d=p[qe]=p[qe]||{},f=d[ze]=d[ze]||{};Ve(f,"domain",l.domain),Ve(f,"isManual",l.isManual?Be:null),Ve(f,"screenRes",l.screenRes),Ve(f,"userConsent",l.userConsent?Be:null)}var g=i.os;g&&(Ve(n,D.O.deviceOS,g[M]),Ve(n,D.O.deviceOSVersion,g.osVer));var m=i.trace;m&&(Ve(n,D.O.operationParentId,m.parentID),Ve(n,D.O.operationName,(0,G.Rr)(e,m[M])),Ve(n,D.O.operationId,m[N]));for(var h={},v=s[$]-1;v>=0;v--){var y=s[v];(0,T.zav)(y,function(e,t){h[e]=t}),s.splice(v,1)}(0,T.zav)(s,function(e,t){h[e]=t});var b=(0,o.Im)((0,o.Im)({},n),h);b[D.O.internalSdkVersion]||(b[D.O.internalSdkVersion]=(0,G.Rr)(e,"javascript:".concat(Je.Version),64)),r[x]=(0,E.hW)(b)}(e,r,s),r[x]=r[x]||[],(0,E.hW)(s)}function Ze(e,t){(0,T.hXl)(t[qe])&&(0,b.ZP)(e,1,46,"telemetryItem.baseData cannot be null.")}var Je={Version:"3.3.4"};function Qe(e,t,r){Ze(e,t);var n={},o={};t[$e]!==s.J[B]&&(n.baseTypeSource=t[$e]),t[$e]===s.J[B]?(n=t[qe][ze]||{},o=t[qe][z]||{}):t[qe]&&Xe(t[qe],n,o),Xe(t[j],n,o),(0,T.hXl)(r)||We(n,r);var i=t[qe][M],a=new s.J(e,i,n,o),c=new F.B(s.J[B],a);return Ke(e,s.J[V],t,c)}var Ye,et,tt=function(){function e(t,r){var o=[],i=!1,s=r.maxRetryCnt;this[W]=function(){return o},this._set=function(e){return o=e},(0,n.A)(e,this,function(e){e[K]=function(n){e[Z]()>=r[J]?i||((0,b.ZP)(t,2,105,"Maximum in-memory buffer size reached: "+e[Z](),!0),i=!0):(n.cnt=n.cnt||0,!(0,T.hXl)(s)&&n.cnt>s||o[Q](n))},e[Z]=function(){return o[$]},e.size=function(){for(var e=o[$],t=0;t<o[$];t++)e+=o[t].item[$];return r[ee]||(e+=2),e},e[te]=function(){o=[],i=!1},e.getItems=function(){return o.slice(0)},e.batchPayloads=function(e){if(e&&e[$]>0){var t=[];return(0,T.Iuo)(e,function(e){t[Q](e[Y])}),r[ee]?t.join("\n"):"["+t.join(",")+"]"}return null},e[re]=function(e,r,n){var i=o.slice(0);e=e||t,r=r||{};var s=n?new ot(e,r):new rt(e,r);return(0,T.Iuo)(i,function(e){s[K](e)}),s}})}return e.__ieDyn=1,e}(),rt=function(e){function t(r,o){var i=e.call(this,r,o)||this;return(0,n.A)(t,i,function(e,t){e[ne]=function(e){t[te]()},e[oe]=function(e){}}),i}return(0,o.qU)(t,e),t.__ieDyn=1,t}(tt),nt=["AI_buffer","AI_sentBuffer"],ot=function(e){function t(r,o){var i=e.call(this,r,o)||this,s=!1,a=null==o?void 0:o.namePrefix,c=o[ie]||{getItem:m.vH,setItem:m.Dt},u=c.getItem,l=c.setItem,p=o.maxRetryCnt;return(0,n.A)(t,i,function(e,n){var o=h(t[se]),i=h(t[ae]),c=function(){var e=[];try{return(0,T.Iuo)(nt,function(t){var r=w(t);if(e=e[ce](r),a){var n=w(a+"_"+t);e=e[ce](n)}}),e}catch(e){(0,b.ZP)(r,2,41,"Transfer events from previous buffers: "+(0,E.lL)(e)+". previous Buffer items can not be removed",{exception:(0,T.mmD)(e)})}return[]}(),d=i[ce](c),f=e._set(o[ce](d));function g(e,t){var r=[],n=[];return(0,T.Iuo)(e,function(e){n[Q](e[Y])}),(0,T.Iuo)(t,function(e){(0,T.Tnt)(e)||-1!==(0,T.rDm)(n,e[Y])||r[Q](e)}),r}function h(e){return v(a?a+"_"+e:e)}function v(e){try{var t=u(r,e);if(t){var n=(0,O.hm)().parse(t);if((0,T.KgX)(n)&&(n=(0,O.hm)().parse(n)),n&&(0,T.cyL)(n))return n}}catch(t){(0,b.ZP)(r,1,42," storage key: "+e+", "+(0,E.lL)(t),{exception:(0,T.mmD)(t)})}return[]}function y(e,t){var n=e;try{n=a?a+"_"+n:n;var o=JSON[q](t);l(r,n,o)}catch(e){l(r,n,JSON[q]([])),(0,b.ZP)(r,2,41," storage key: "+n+", "+(0,E.lL)(e)+". Buffer cleared",{exception:(0,T.mmD)(e)})}}function w(e){try{var t=v(e),n=[];return(0,T.Iuo)(t,function(e){var t={item:e,cnt:0};n[Q](t)}),(0,m.v7)(r,e),n}catch(e){}return[]}f[$]>t[ue]&&(f[$]=t[ue]),y(t[ae],[]),y(t[se],f),e[K]=function(o){e[Z]()>=t[ue]?s||((0,b.ZP)(r,2,67,"Maximum buffer size reached: "+e[Z](),!0),s=!0):(o.cnt=o.cnt||0,!(0,T.hXl)(p)&&o.cnt>p||(n[K](o),y(t.BUFFER_KEY,e[W]())))},e[te]=function(){n[te](),y(t.BUFFER_KEY,e[W]()),y(t[ae],[]),s=!1},e[ne]=function(n){y(t[se],e._set(g(n,e[W]())));var o=h(t[ae]);o instanceof Array&&n instanceof Array&&((o=o[ce](n))[$]>t[ue]&&((0,b.ZP)(r,1,67,"Sent buffer reached its maximum size: "+o[$],!0),o[$]=t[ue]),y(t[ae],o))},e[oe]=function(e){var r=h(t[ae]);r=g(e,r),y(t[ae],r)},e[re]=function(n,o,i){i=!!i;var s=e[W]().slice(0),a=h(t[ae]).slice(0);n=n||r,o=o||{},e[te]();var c=i?new t(n,o):new rt(n,o);return(0,T.Iuo)(s,function(e){c[K](e)}),i&&c[ne](a),c}}),i}var r;return(0,o.qU)(t,e),r=t,t.VERSION="_1",t.BUFFER_KEY="AI_buffer"+r.VERSION,t.SENT_BUFFER_KEY="AI_sentBuffer"+r.VERSION,t.MAX_BUFFER_SIZE=2e3,t}(tt),it=function(){function e(t){(0,n.A)(e,this,function(e){function r(e,i){var s="__aiCircularRefCheck",a={};if(!e)return(0,b.ZP)(t,1,48,"cannot serialize object because it is null or undefined",{name:i},!0),a;if(e[s])return(0,b.ZP)(t,2,50,"Circular reference detected while serializing object",{name:i},!0),a;if(!e.aiDataContract){if("measurements"===i)a=o(e,"number",i);else if("properties"===i)a=o(e,"string",i);else if("tags"===i)a=o(e,"string",i);else if((0,T.cyL)(e))a=n(e,i);else{(0,b.ZP)(t,2,49,"Attempting to serialize an object which does not implement ISerializable",{name:i},!0);try{(0,O.hm)()[q](e),a=e}catch(e){(0,b.ZP)(t,1,48,e&&(0,T.Tnt)(e[X])?e[X]():"Error serializing object",null,!0)}}return a}return e[s]=!0,(0,T.zav)(e.aiDataContract,function(o,s){var c=(0,T.Tnt)(s)?1&s():1&s,u=(0,T.Tnt)(s)?4&s():4&s,l=2&s,p=void 0!==e[o],d=(0,T.Gvm)(e[o])&&null!==e[o];if(!c||p||l){if(!u){var f;void 0!==(f=d?l?n(e[o],o):r(e[o],o):e[o])&&(a[o]=f)}}else(0,b.ZP)(t,1,24,"Missing required field specification. The field is required but not present on source",{field:o,name:i})}),delete e[s],a}function n(e,n){var o;if(e)if((0,T.cyL)(e)){o=[];for(var i=0;i<e[$];i++){var s=r(e[i],n+"["+i+"]");o[Q](s)}}else(0,b.ZP)(t,1,54,"This field was specified as an array in the contract but the item is not an array.\r\n",{name:n},!0);return o}function o(e,r,n){var o;return e&&(o={},(0,T.zav)(e,function(e,i){if("string"===r)void 0===i?o[e]="undefined":null===i?o[e]="null":i[X]?o[e]=i[X]():o[e]="invalid field: toString() is not defined.";else if("number"===r)if(void 0===i)o[e]="undefined";else if(null===i)o[e]="null";else{var s=parseFloat(i);o[e]=s}else o[e]="invalid field: "+n+" is of unknown type.",(0,b.ZP)(t,1,o[e],null,!0)})),o}e[Ie]=function(e){var n=r(e,"root");try{return(0,O.hm)()[q](n)}catch(e){(0,b.ZP)(t,1,48,e&&(0,T.Tnt)(e[X])?e[X]():"Error serializing object",null,!0)}}})}return e.__ieDyn=1,e}(),st=r(8596),at=function(){function e(){}return e.prototype.getHashCodeScore=function(t){return this.getHashCode(t)/e.INT_MAX_VALUE*100},e.prototype.getHashCode=function(e){if(""===e)return 0;for(;e[$]<8;)e=e[ce](e);for(var t=5381,r=0;r<e[$];++r)t=(t<<5)+t+e.charCodeAt(r),t&=t;return Math.abs(t)},e.INT_MAX_VALUE=2147483647,e}(),ct=function(){var e=new at,t=new st.o;this[Ne]=function(r){return r[x]&&r[x][t.userId]?e.getHashCodeScore(r[x][t.userId]):r.ext&&r.ext.user&&r.ext.user.id?e.getHashCodeScore(r.ext.user.id):r[x]&&r[x][t.operationId]?e.getHashCodeScore(r[x][t.operationId]):r.ext&&r.ext.telemetryTrace&&r.ext.telemetryTrace[N]?e.getHashCodeScore(r.ext.telemetryTrace[N]):100*Math.random()}},ut=function(){function e(e,t){this.INT_MAX_VALUE=2147483647;var r=t||(0,b.y0)(null);(e>100||e<0)&&(r.throwInternal(2,58,"Sampling rate is out of range (0..100). Sampling will be disabled, you may be sending too much data which may affect your AI service level.",{samplingRate:e},!0),e=100),this[je]=e,this.samplingScoreGenerator=new ct}return e.prototype.isSampledIn=function(e){var t=this[je];return null==t||t>=100||e.baseType===p.J[B]||this.samplingScoreGenerator[Ne](e)<t},e}(),lt=void 0;function pt(e){try{return e.responseText}catch(e){}return null}var dt,ft=(0,T.ZHX)(((Ye={endpointUrl:(0,y.Lx)(T.zzB,i._G+i.wc)})[ee]=(0,y.DD)(),Ye[Ae]=15e3,Ye[ve]=102400,Ye.disableTelemetry=(0,y.DD)(),Ye[we]=(0,y.DD)(!0),Ye.isRetryDisabled=(0,y.DD)(),Ye[be]=(0,y.DD)(!0),Ye[Se]=(0,y.DD)(!0),Ye[Te]=(0,y.DD)(),Ye[Pe]=(0,y.DD)(),Ye[ye]=(0,y.DD)(),Ye[me]=lt,Ye.namePrefix=lt,Ye.samplingPercentage=(0,y.Lx)(function(e){return!isNaN(e)&&e>0&&e<=100},100),Ye[he]=lt,Ye[Re]=lt,Ye[J]=1e4,Ye[ie]=!1,Ye.httpXHROverride={isVal:function(e){return e&&e.sendPOST},v:lt},Ye[Ee]=(0,y.DD)(),Ye.transports=lt,Ye.retryCodes=lt,Ye.maxRetryCnt={isVal:T.EtT,v:10},Ye)),gt=((et={})[s.J.dataType]=Qe,et[a.C.dataType]=function(e,t,r){Ze(e,t);var n=t[qe].message,o=t[qe].severityLevel,i=t[qe][ze]||{},s=t[qe][z]||{};Xe(t[j],i,s),(0,T.hXl)(r)||We(i,r);var c=new a.C(e,n,o,i,s),u=new F.B(a.C[B],c);return Ke(e,a.C[V],t,u)},et[c.h.dataType]=function(e,t,r){var n;Ze(e,t);var o=t[qe];(0,T.hXl)(o)||(0,T.hXl)(o[ze])||(0,T.hXl)(o[ze][L])?(0,T.hXl)(t[j])||(0,T.hXl)(t[j][L])||(n=t[j][L],delete t[j][L]):(n=o[ze][L],delete o[ze][L]);var i,s=t[qe];((t.ext||{}).trace||{})[N]&&(i=t.ext.trace[N]);var a=s.id||i,u=s[M],l=s.uri,p=s[ze]||{},d=s[z]||{};if((0,T.hXl)(s.refUri)||(p.refUri=s.refUri),(0,T.hXl)(s.pageType)||(p.pageType=s.pageType),(0,T.hXl)(s.isLoggedIn)||(p.isLoggedIn=s.isLoggedIn[X]()),!(0,T.hXl)(s[ze])){var f=s[ze];(0,T.zav)(f,function(e,t){p[e]=t})}Xe(t[j],p,d),(0,T.hXl)(r)||We(p,r);var g=new c.h(e,u,l,n,p,d,a),m=new F.B(c.h[B],g);return Ke(e,c.h[V],t,m)},et[u.H.dataType]=function(e,t,r){Ze(e,t);var n=t[qe],o=n[M],i=n.uri||n.url,s=n[ze]||{},a=n[z]||{};Xe(t[j],s,a),(0,T.hXl)(r)||We(s,r);var c=new u.H(e,o,i,void 0,s,a,n),l=new F.B(u.H[B],c);return Ke(e,u.H[V],t,l)},et[l.WJ.dataType]=function(e,t,r){Ze(e,t);var n=t[qe][z]||{},o=t[qe][ze]||{};Xe(t[j],o,n),(0,T.hXl)(r)||We(o,r);var i=t[qe],s=l.WJ.CreateFromInterface(e,i,o,n),a=new F.B(l.WJ[B],s);return Ke(e,l.WJ[V],t,a)},et[p.J.dataType]=function(e,t,r){Ze(e,t);var n=t[qe],o=n[ze]||{},i=n[z]||{};Xe(t[j],o,i),(0,T.hXl)(r)||We(o,r);var s=new p.J(e,n[M],n.average,n.sampleCount,n.min,n.max,n.stdDev,o,i),a=new F.B(p.J[B],s);return Ke(e,p.J[V],t,a)},et[d.A.dataType]=function(e,t,r){Ze(e,t);var n=t[qe][z]||{},o=t[qe][ze]||{};Xe(t[j],o,n),(0,T.hXl)(r)||We(o,r);var s=t[qe];if((0,T.hXl)(s))return(0,b.OG)(e,"Invalid input for dependency data"),null;var a=s[ze]&&s[ze][i.ym]?s[ze][i.ym]:"GET",c=new d.A(e,s.id,s.target,s[M],s[L],s.success,s.responseCode,a,s.type,s.correlationContext,o,n),u=new F.B(d.A[B],c);return Ke(e,d.A[V],t,u)},et),mt=function(e){function t(){var r,o,s,a,c,u,l,p=e.call(this)||this;p.priority=1001,p.identifier=f.BreezeChannelIdentifier;var d,y,I,D,G,U,F,L,H,M,N,q,z,B,V,X,W,J,ee,se,ae,ce,ue,Ne,$e,qe,ze,Be=0;return(0,n.A)(t,p,function(e,n){function f(t,n){var o=pt(t);if(!t||o+""!="200"&&""!==o){var i=(0,A.x)(o);i&&i[Fe]&&i[Fe]>i[Le]&&!B?e[Ge](n,i):e[De](n,(0,E.HU)(t))}else r=0,e[Ue](n,0)}function Ve(e,t,r){4===e.readyState&&st(e.status,t,e.responseURL,r,(0,E.r4)(e),pt(e)||e.response)}function Xe(e){try{if(e){var t=e[xe];return t&&t[$]?t:null}}catch(e){}return null}function We(t,r){return!(N||(t?t.baseData&&!t[He]?(r&&(0,b.ZP)(r,1,70,"Cannot send telemetry without baseData and baseType"),1):(t[He]||(t[He]="EventData"),e[fe]?(n=t,e._sample.isSampledIn(n)?(t[i.tU]=e._sample[je],0):(r&&(0,b.ZP)(r,2,33,"Telemetry item was sampled out and not sent",{SampleRate:e._sample[je]}),1)):(r&&(0,b.ZP)(r,1,28,"Sender was not initialized"),1)):(r&&(0,b.ZP)(r,1,7,"Cannot send empty telemetry"),1)));var n}function Ke(e,r){var n=e.iKey||q,o=t.constructEnvelope(e,n,r,z);if(o){var s=!1;if(e[x]&&e[x][i.jp]&&((0,T.Iuo)(e[x][i.jp],function(e){try{e&&!1===e(o)&&(s=!0,(0,b.OG)(r,"Telemetry processor check returns false"))}catch(e){(0,b.ZP)(r,1,64,"One of telemetry initializers failed, telemetry item will not be sent: "+(0,E.lL)(e),{exception:(0,T.mmD)(e)},!0)}}),delete e[x][i.jp]),!s)return o}else(0,b.ZP)(r,1,47,"Unable to create an AppInsights envelope")}function Ze(t){var r="",n=e[pe]();try{var o=We(t,n),i=null;o&&(i=Ke(t,n)),i&&(r=c[Ie](i))}catch(e){}return r}function Je(e){var t="";return e&&e[$]&&(t="["+e.join(",")+"]"),t}function Qe(e){var t,r=tt();return(t={urlString:D})[j]=e,t.headers=r,t}function Ye(t,r,n,o){void 0===o&&(o=!0);var i=et(r),s=t&&t.sendPOST;return s&&i?(o&&e._buffer[ne](r),s(i,function(t,n,o){return function(t,r,n,o){200===r&&t?e._onSuccess(t,t[$]):o&&e[De](t,o)}(r,t,0,o)},!n)):null}function et(t){var r;if((0,T.cyL)(t)&&t[$]>0){var n=e[_e].batchPayloads(t),o=tt();return(r={})[j]=n,r.urlString=D,r.headers=o,r.disableXhrSync=ae,r.disableFetchKeepAlive=!ce,r[xe]=t,r}return null}function tt(){try{var e=l||{};return(0,h.Qu)(D)&&(e[v.a[6]]=v.a[7]),e}catch(e){}return null}function nt(t){var r=t?t[$]:0;return e[_e].size()+r>U&&(y&&!y.isOnline()||e[le](!0,null,10),!0)}function st(t,n,o,i,s,a){var c=null;if(e._appId||(c=(0,A.x)(a))&&c.appId&&(e._appId=c.appId),(t<200||t>=300)&&0!==t){if((301===t||307===t||308===t)&&!at(o))return void e[De](n,s);if(y&&!y.isOnline())return void(B||(mt(n,10),(0,b.ZP)(e[pe](),2,40,". Offline - Response Code: ".concat(t,". Offline status: ").concat(!y.isOnline(),". Will retry to send ").concat(n.length," items."))));!B&&yt(t)?(mt(n),(0,b.ZP)(e[pe](),2,40,". Response code "+t+". Will retry to send "+n[$]+" items.")):e[De](n,s)}else at(o),206===t?(c||(c=(0,A.x)(a)),c&&!B?e[Ge](n,c):e[De](n,s)):(r=0,e[Ue](n,i))}function at(e){return!(u>=10||(0,T.hXl)(e)||""===e||e===D||(D=e,++u,0))}function ct(e,t){if(!d)return Ye(qe&&qe[Oe]([3],!0),e,t);d(e,!1)}function dt(e){try{if(e&&e[$])return(0,T.KgX)(e[0])}catch(e){}return null}function gt(t,r){var n=null;if((0,T.cyL)(t)){for(var o=t[$],i=0;i<t[$];i++)o+=t[i].item[$];return qe.getSyncFetchPayload()+o<=65e3?n=2:(0,O.Uf)()?n=3:(n=1,(0,b.ZP)(e[pe](),2,40,". Failed to send telemetry with Beacon API, retried with xhrSender.")),Ye(qe&&qe[Oe]([n],!0),t,r)}return null}function mt(t,n){if(void 0===n&&(n=1),t&&0!==t[$]){var i=e[_e];i[oe](t),r++;for(var s=0,a=t;s<a.length;s++){var c=a[s];c.cnt=c.cnt||0,c.cnt++,i[K](c)}!function(e){var t;if(r<=1)t=10;else{var n=(Math.pow(2,r)-1)/2,i=Math.floor(Math.random()*n*10)+1;i*=e,t=Math.max(Math.min(i,3600),10)}var s=(0,T.f0d)()+1e3*t;o=s}(n),ht()}}function ht(){if(!a&&!s){var t=o?Math.max(0,o-(0,T.f0d)()):0,r=Math.max(V,t);a=(0,T.dRz)(function(){a=null,e[le](!0,null,1)},r)}}function vt(){a&&a.cancel(),a=null,o=null}function yt(e){return(0,T.hXl)(ze)?401===e||408===e||429===e||500===e||502===e||503===e||504===e:ze[$]&&ze.indexOf(e)>-1}function bt(){e[fe]=null,e[_e]=null,e._appId=null,e._sample=null,l={},y=null,r=0,o=null,s=!1,a=null,c=null,u=0,Be=0,d=null,I=null,D=null,G=null,U=0,F=!1,M=null,N=!1,q=null,z=lt,B=!1,X=null,J=lt,ae=!1,ce=!1,$e=!1,ue=null,Ne=null,qe=null,(0,T.vF1)(e,"_senderConfig",{g:function(){return(0,E.CP)({},ft)}})}bt(),e.pause=function(){vt(),s=!0},e.resume=function(){s&&(s=!1,o=null,nt(),ht())},e.flush=function(t,r,n){if(void 0===t&&(t=!0),!s){vt();try{return e[le](t,null,n||1)}catch(t){(0,b.ZP)(e[pe](),1,22,"flush failed, telemetry will not be collected: "+(0,E.lL)(t),{exception:(0,T.mmD)(t)})}}},e.onunloadFlush=function(){if(!s)if(F||se)try{return e[le](!0,ct,2)}catch(t){(0,b.ZP)(e[pe](),1,20,"failed to flush with beacon sender on page unload, telemetry will not be collected: "+(0,E.lL)(t),{exception:(0,T.mmD)(t)})}else e.flush(!1)},e.addHeader=function(e,t){l[e]=t},e[de]=function(t,i,s,a){e.isInitialized()&&(0,b.ZP)(e[pe](),1,28,"Sender is already initialized"),n[de](t,i,s,a);var v=e.identifier;c=new it(i.logger),r=0,o=null,e[fe]=null,u=0;var R=e[pe]();I=(0,w.Hm)((0,_.Z)("Sender"),i.evtNamespace&&i.evtNamespace()),y=(0,g.G)(I),e._addHook((0,P.a)(t,function(t){var r=t.cfg;r.storagePrefix&&(0,m.vh)(r.storagePrefix);var n=(0,S.i8)(null,r,i).getExtCfg(v,ft),o=n[ge];if(D&&o===D){var s=r[ge];s&&s!==o&&(n[ge]=s)}(0,T.$XS)(n[me])&&(n[me]=r[me]),(0,T.vF1)(e,"_senderConfig",{g:function(){return n}}),G!==n[ge]&&(D=G=n[ge]),i.activeStatus()===k.f.PENDING?e.pause():i.activeStatus()===k.f.ACTIVE&&e.resume(),M&&M!==n[he]&&(0,T.Iuo)(M,function(e){delete l[e.header]}),U=n[ve],F=(!1===n[ye]||!1===n[be])&&(0,O.Uf)(),L=!1===n[ye]&&(0,O.Uf)(),H=!1===n[be]&&(0,O.Uf)(),se=n[Ee],ae=!!n[Te],ze=n.retryCodes;var a=n[ie],c=!!n[we]&&(!!a||(0,m.AN)()),u=n.namePrefix,g=c!==X||c&&J!==u||c&&W!==a;if(e[_e]){if(g)try{e._buffer=e._buffer[re](R,n,c)}catch(t){(0,b.ZP)(e[pe](),1,12,"failed to transfer telemetry to different buffer storage, telemetry will be lost: "+(0,E.lL)(t),{exception:(0,T.mmD)(t)})}nt()}else e[_e]=c?new ot(R,n):new rt(R,n);J=u,X=c,W=a,ce=!n[Pe]&&(0,O.R7)(!0),$e=!!n[Se],e._sample=new ut(n.samplingPercentage,R),q=n[me],(0,T.$XS)(q)||function(e,t){var r=t.disableInstrumentationKeyValidation;return!((0,T.hXl)(r)||!r)||new RegExp("^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$").test(e)}(q,r)||(0,b.ZP)(R,1,100,"Invalid Instrumentation key "+q),M=n[he],(0,T.KgX)(D)&&!(0,h.Qu)(D)&&M&&M[$]>0?(0,T.Iuo)(M,function(e){p.addHeader(e.header,e.value)}):M=null,ee=n[ke];var y=function(){var t;try{var r={xdrOnComplete:function(e,t,r){var n=Xe(r);if(n)return f(e,n)},fetchOnComplete:function(e,t,r,n){var o=Xe(n);if(o)return st(e.status,o,e.url,o[$],e.statusText,r||"")},xhrOnComplete:function(e,t,r){var n=Xe(r);if(n)return Ve(e,n,n[$])},beaconOnRetry:function(t,r,n){return function(t,r,n){var o=t&&t[xe];if($e)Ne&&Ne(o,!0),(0,b.ZP)(e[pe](),2,40,". Failed to send telemetry with Beacon API, retried with normal sender.");else{for(var i=[],s=0;s<o[$];s++){var a=o[s],c=[a];n(et(c),r)?e._onSuccess(c,c[$]):i[Q](a)}i[$]>0&&(Ne&&Ne(i,!0),(0,b.ZP)(e[pe](),2,40,". Failed to send telemetry with Beacon API, retried with normal sender."))}}(t,r,n)}};return(t={})[ke]=ee,t.isOneDs=!1,t.disableCredentials=!1,t[Te]=ae,t.disableBeacon=!H,t.disableBeaconSync=!L,t.senderOnCompleteCallBack=r,t}catch(e){}return null}();qe?qe.SetConfig(y):(qe=new C.v)[de](y,R);var w=n.httpXHROverride,_=null,P=null,A=(0,E.jL)([3,1,2],n.transports);_=qe&&qe[Oe](A,!1);var I=qe&&qe.getFallbackInst();ue=function(e,t){return Ye(I,e,t)},Ne=function(e,t){return Ye(I,e,t,!1)},_=se?w:_||w||I,e[fe]=function(e,t){return Ye(_,e,t)},ce&&(d=gt);var x=(0,E.jL)([3,1],n[Ce]);ce||(x=x.filter(function(e){return 2!==e})),P=qe&&qe[Oe](x,!0),P=se?w:P||w,(se||n[Ce]||!d)&&P&&(d=function(e,t){return Ye(P,e,t)}),d||(d=ue),N=n.disableTelemetry,z=n[Re]||lt,B=n.isRetryDisabled,V=n[Ae]}))},e.processTelemetry=function(t,r){var n,o=(r=e._getTelCtx(r))[pe]();try{if(!We(t,o))return;var i=Ke(t,o);if(!i)return;var s=c[Ie](i),a=e[_e];nt(s);var u=((n={})[Y]=s,n.cnt=0,n);a[K](u),ht()}catch(e){(0,b.ZP)(o,2,12,"Failed adding telemetry to the sender's buffer, some telemetry will be lost: "+(0,E.lL)(e),{exception:(0,T.mmD)(e)})}e.processNext(t,r)},e.isCompletelyIdle=function(){return!s&&0===Be&&0===e._buffer[Z]()},e.getOfflineListener=function(){return y},e._xhrReadyStateChange=function(e,t,r){if(!dt(t))return Ve(e,t,r)},e[le]=function(t,r,n){var o;if(void 0===t&&(t=!0),!s)try{var i=e[_e];if(N)i[te]();else{if(i[Z]()>0){var a=i.getItems();!function(t,r){var n,o=(n="getNotifyMgr",e.core[n]?e.core[n]():e.core._notificationManager);if(o&&o[Me])try{o[Me](t,r)}catch(t){(0,b.ZP)(e[pe](),1,74,"send request notification failed: "+(0,E.lL)(t),{exception:(0,T.mmD)(t)})}}(n||0,t),o=r?r.call(e,a,t):e[fe](a,t)}new Date}vt()}catch(t){var c=(0,O.L0)();(!c||c>9)&&(0,b.ZP)(e[pe](),1,40,"Telemetry transmission failed, some telemetry will be lost: "+(0,E.lL)(t),{exception:(0,T.mmD)(t)})}return o},e.getOfflineSupport=function(){var e;return(e={getUrl:function(){return D},createPayload:Qe})[Ie]=Ze,e.batch=Je,e.shouldProcess=function(e){return!!We(e)},e},e._doTeardown=function(t,r){e.onunloadFlush(),(0,R.K)(y,!1),bt()},e[De]=function(t,r,n){if(!dt(t))return function(t,r){(0,b.ZP)(e[pe](),2,26,"Failed to send telemetry.",{message:r}),e._buffer&&e._buffer[oe](t)}(t,r)},e[Ge]=function(t,r){if(!dt(t))return function(t,r){for(var n=[],o=[],i=0,s=r.errors.reverse();i<s.length;i++){var a=s[i],c=t.splice(a.index,1)[0];yt(a.statusCode)?o[Q](c):n[Q](c)}t[$]>0&&e[Ue](t,r[Le]),n[$]>0&&e[De](n,(0,E.r4)(null,["partial success",r[Le],"of",r.itemsReceived].join(" "))),o[$]>0&&(mt(o),(0,b.ZP)(e[pe](),2,40,"Partial success. Delivered: "+t[$]+", Failed: "+n[$]+". Will retry to send "+o[$]+" our of "+r[Fe]+" items"))}(t,r)},e[Ue]=function(t,r){if(!dt(t))return function(t){e._buffer&&e._buffer[oe](t)}(t)},e._xdrOnLoad=function(e,t){if(!dt(t))return f(e,t)}}),p}return(0,o.qU)(t,e),t.constructEnvelope=function(e,t,r,n){var i;return i=t===e.iKey||(0,T.hXl)(t)?e:(0,o.Im)((0,o.Im)({},e),{iKey:t}),(gt[i.baseType]||Qe)(r,i,n)},t}(I.s),ht=r(4484),vt=r(2774),yt=r(8205),bt="instrumentationKey",Et="connectionString",Tt="instrumentationkey",wt="endpointUrl",_t="ingestionendpoint",Pt="userOverrideEndpointUrl",St=r(9762),kt=void 0,Ot=((dt={diagnosticLogInterval:(0,y.Lx)(function(e){return e&&e>0},1e4)})[Et]=kt,dt[wt]=kt,dt[bt]=kt,dt.extensionConfig={},dt),Ct=function(){function e(t){var r,o=new vt._;function s(e){e&&(e.baseData=e.baseData||{},e.baseType=e.baseType||"EventData"),o.track(e)}((0,T.hXl)(t)||(0,T.hXl)(t[bt])&&(0,T.hXl)(t[Et]))&&(0,T.$8)("Invalid input configuration"),(0,n.A)(e,this,function(e){function n(){var e=(0,P.e)(t||{},Ot);r=e.cfg,o.addUnloadHook((0,P.a)(e,function(){var e=r[Et];if((0,T.$XS)(e)){var t=(0,yt.Rf)(function(t,n){(0,yt.Dv)(e,function(e){var n=e.value,o=r[bt];!e.rejected&&n&&(r[Et]=n,o=(0,ht.H)(n)[Tt]||o),t(o)})}),n=(0,yt.Rf)(function(t,n){(0,yt.Dv)(e,function(e){var n=e.value,o=r[wt];if(!e.rejected&&n){var s=(0,ht.H)(n)[_t];o=s?s+i.wc:o}t(o)})});r[bt]=t,r[wt]=r[Pt]||n}if((0,T.KgX)(e)){var o=(0,ht.H)(e),s=o[_t];r[wt]=r[Pt]?r[Pt]:s+i.wc,r[bt]=o[Tt]||r[bt]}r[wt]=r[Pt]?r[Pt]:r[wt]})),o.initialize(r,[new mt])}(0,T.vF1)(e,"config",{g:function(){return r}}),n(),e.initialize=n,e.track=s,(0,E.o$)(e,o,["flush","pollInternalLogs","stopPollingInternalLogs","unload","getPlugin","addPlugin","evtNamespace","addUnloadCb","onCfgChange","getTraceCtx","updateCfg","addTelemetryInitializer"])})}return e.__ieDyn=1,e}()},6149:(e,t,r)=>{"use strict";r.d(t,{Ds:()=>$,El:()=>O,Fc:()=>q,Hm:()=>I,ML:()=>G,Q3:()=>U,So:()=>L,Wg:()=>M,Ym:()=>F,ee:()=>N,lQ:()=>j,mB:()=>D,oS:()=>B,sq:()=>z,vF:()=>V,zh:()=>x});var n=r(269),o=r(6182),i=r(4276),s=r(6492),a="on",c="attachEvent",u="addEventListener",l="detachEvent",p="removeEventListener",d="events",f="visibilitychange",g="pagehide",m="pageshow",h="unload",v="beforeunload",y=(0,i.Z)("aiEvtPageHide"),b=(0,i.Z)("aiEvtPageShow"),E=/\.[\.]+/g,T=/[\.]+$/,w=1,_=(0,i.T)("events"),P=/^([^.]*)(?:\.(.+)|)/;function S(e){return e&&e[o.W7]?e[o.W7](/^[\s\.]+|(?=[\s\.])[\.\s]+$/g,s.m5):e}function k(e,t){var r;if(t){var i=s.m5;(0,n.cyL)(t)?(i=s.m5,(0,n.Iuo)(t,function(e){(e=S(e))&&("."!==e[0]&&(e="."+e),i+=e)})):i=S(t),i&&("."!==i[0]&&(i="."+i),e=(e||s.m5)+i)}var a=P.exec(e||s.m5)||[];return(r={})[o.QM]=a[1],r.ns=(a[2]||s.m5).replace(E,".").replace(T,s.m5)[o.sY](".").sort().join("."),r}function O(e,t,r){var i=[],a=_.get(e,d,{},!1),c=k(t,r);return(0,n.zav)(a,function(e,t){(0,n.Iuo)(t,function(e){var t;c[o.QM]&&c[o.QM]!==e.evtName[o.QM]||c.ns&&c.ns!=c.ns||i[o.y5](((t={})[o.RS]=e.evtName[o.QM]+(e.evtName.ns?"."+e.evtName.ns:s.m5),t.handler=e[o.Yo],t))})}),i}function C(e,t,r){void 0===r&&(r=!0);var n=_.get(e,d,{},r),o=n[t];return o||(o=n[t]=[]),o}function R(e,t,r,n){e&&t&&t[o.QM]&&(e[p]?e[p](t[o.QM],r,n):e[l]&&e[l](a+t[o.QM],r))}function A(e,t,r,n){for(var i=t[o.oI];i--;){var s=t[i];s&&(r.ns&&r.ns!==s.evtName.ns||n&&!n(s)||(R(e,s.evtName,s[o.Yo],s.capture),t[o.Ic](i,1)))}}function I(e,t){return t?k("xx",(0,n.cyL)(t)?[e].concat(t):[e,t]).ns[o.sY]("."):e}function D(e,t,r,n,i){var s;void 0===i&&(i=!1);var l=!1;if(e)try{var p=k(t,n);if(l=function(e,t,r,n){var i=!1;return e&&t&&t[o.QM]&&r&&(e[u]?(e[u](t[o.QM],r,n),i=!0):e[c]&&(e[c](a+t[o.QM],r),i=!0)),i}(e,p,r,i),l&&_.accept(e)){var d=((s={guid:w++,evtName:p})[o.Yo]=r,s.capture=i,s);C(e,p.type)[o.y5](d)}}catch(e){}return l}function G(e,t,r,i,s){if(void 0===s&&(s=!1),e)try{var a=k(t,i),c=!1;!function(e,t,r){if(t[o.QM])A(e,C(e,t[o.QM]),t,r);else{var i=_.get(e,d,{});(0,n.zav)(i,function(n,o){A(e,o,t,r)}),0===(0,n.cGk)(i)[o.oI]&&_.kill(e,d)}}(e,a,function(e){return!((!a.ns||r)&&e[o.Yo]!==r||(c=!0,0))}),c||R(e,a,r,s)}catch(e){}}function U(e,t,r,n){return void 0===n&&(n=!1),D(e,t,r,null,n)}function F(e,t,r,n){void 0===n&&(n=!1),G(e,t,r,null,n)}function L(e,t,r){var o=!1,i=(0,n.zkX)();i&&(o=D(i,e,t,r),o=D(i.body,e,t,r)||o);var s=(0,n.YEm)();return s&&(o=D(s,e,t,r)||o),o}function x(e,t,r){var o=(0,n.zkX)();o&&(G(o,e,t,r),G(o.body,e,t,r));var i=(0,n.YEm)();i&&G(i,e,t,r)}function H(e,t,r,i){var s=!1;return t&&e&&e[o.oI]>0&&(0,n.Iuo)(e,function(e){e&&(r&&-1!==(0,n.rDm)(r,e)||(s=L(e,t,i)||s))}),s}function j(e,t,r,i){var s=!1;return t&&e&&(0,n.cyL)(e)&&!(s=H(e,t,r,i))&&r&&r[o.oI]>0&&(s=H(e,t,null,i)),s}function M(e,t,r){e&&(0,n.cyL)(e)&&(0,n.Iuo)(e,function(e){e&&x(e,t,r)})}function N(e,t,r){return j([v,h,g],e,t,r)}function $(e,t){M([v,h,g],e,t)}function q(e,t,r){var o=I(y,r),i=H([g],e,t,o);return t&&-1!==(0,n.rDm)(t,f)||(i=H([f],function(t){var r=(0,n.YEm)();e&&r&&"hidden"===r.visibilityState&&e(t)},t,o)||i),!i&&t&&(i=q(e,null,r)),i}function z(e,t){var r=I(y,t);M([g],e,r),M([f],null,r)}function B(e,t,r){var o=I(b,r),i=H([m],e,t,o);return!(i=H([f],function(t){var r=(0,n.YEm)();e&&r&&"visible"===r.visibilityState&&e(t)},t,o)||i)&&t&&(i=B(e,null,r)),i}function V(e,t){var r=I(b,t);M([m],e,r),M([f],null,r)}},6182:(e,t,r)=>{"use strict";r.d(t,{$5:()=>I,$o:()=>M,AP:()=>O,Az:()=>ie,Bl:()=>j,Cd:()=>ce,DI:()=>D,Di:()=>F,FI:()=>R,HC:()=>X,Ic:()=>m,Ik:()=>N,JQ:()=>o,JR:()=>$,Ju:()=>b,K0:()=>h,L:()=>ge,M5:()=>L,NA:()=>Te,OL:()=>n,O_:()=>ue,P5:()=>ye,QM:()=>re,Qg:()=>Z,RF:()=>k,RS:()=>C,Rr:()=>Ee,Ru:()=>pe,Uw:()=>d,Vq:()=>fe,W7:()=>ee,XM:()=>U,XW:()=>s,YH:()=>K,Yo:()=>ne,Yq:()=>P,Zu:()=>ae,_w:()=>v,by:()=>x,c1:()=>me,cV:()=>oe,cp:()=>de,do:()=>he,e4:()=>B,e_:()=>le,fA:()=>A,h0:()=>l,h3:()=>E,h4:()=>G,ih:()=>u,mE:()=>y,oI:()=>i,on:()=>c,pF:()=>S,pM:()=>q,re:()=>Y,s:()=>H,sY:()=>Q,sl:()=>_,sx:()=>te,tI:()=>z,tX:()=>J,tZ:()=>w,tn:()=>V,uR:()=>W,vR:()=>T,wJ:()=>se,wi:()=>be,x6:()=>p,y5:()=>g,y9:()=>f,yy:()=>ve,zs:()=>a});var n="toLowerCase",o="blkVal",i="length",s="rdOnly",a="notify",c="warnToConsole",u="throwInternal",l="setDf",p="watch",d="logger",f="apply",g="push",m="splice",h="hdlr",v="cancel",y="initialize",b="identifier",E="removeNotificationListener",T="addNotificationListener",w="isInitialized",_="instrumentationKey",P="INACTIVE",S="value",k="getNotifyMgr",O="getPlugin",C="name",R="iKey",A="time",I="processNext",D="getProcessTelContext",G="pollInternalLogs",U="enabled",F="stopPollingInternalLogs",L="unload",x="onComplete",H="version",j="loggingLevelConsole",M="createNew",N="teardown",$="messageId",q="message",z="isAsync",B="diagLog",V="_doTeardown",X="update",W="getNext",K="setNextPlugin",Z="protocol",J="userAgent",Q="split",Y="nodeType",ee="replace",te="logInternalMessage",re="type",ne="handler",oe="status",ie="getResponseHeader",se="getAllResponseHeaders",ae="isChildEvt",ce="data",ue="getCtx",le="setCtx",pe="complete",de="itemsReceived",fe="urlString",ge="sendPOST",me="headers",he="timeout",ve="setRequestHeader",ye="traceId",be="spanId",Ee="traceFlags",Te="getAttribute"},6401:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Octokit:()=>D});var n=r(5407);function o(e,t,r,n){if("function"!=typeof r)throw new Error("method for before hook must be a function");return n||(n={}),Array.isArray(t)?t.reverse().reduce((t,r)=>o.bind(null,e,r,t,n),r)():Promise.resolve().then(()=>e.registry[t]?e.registry[t].reduce((e,t)=>t.hook.bind(null,e,n),r)():r(n))}function i(e,t,r,n){const o=n;e.registry[r]||(e.registry[r]=[]),"before"===t&&(n=(e,t)=>Promise.resolve().then(o.bind(null,t)).then(e.bind(null,t))),"after"===t&&(n=(e,t)=>{let r;return Promise.resolve().then(e.bind(null,t)).then(e=>(r=e,o(r,t))).then(()=>r)}),"error"===t&&(n=(e,t)=>Promise.resolve().then(e.bind(null,t)).catch(e=>o(e,t))),e.registry[r].push({hook:n,orig:o})}function s(e,t,r){if(!e.registry[t])return;const n=e.registry[t].map(e=>e.orig).indexOf(r);-1!==n&&e.registry[t].splice(n,1)}const a=Function.bind,c=a.bind(a);function u(e,t,r){const n=c(s,null).apply(null,r?[t,r]:[t]);e.api={remove:n},e.remove=n,["before","error","after","wrap"].forEach(n=>{const o=r?[t,n,r]:[t,n];e[n]=e.api[n]=c(i,null).apply(null,o)})}const l=function(){const e={registry:{}},t=o.bind(null,e);return u(t,e),t};var p=r(3698),d=r(6555),f="(?:[a-zA-Z0-9_-]+)",g=new RegExp(`^${f}\\.${f}\\.${f}$`),m=g.test.bind(g);async function h(e){const t=m(e),r=e.startsWith("v1.")||e.startsWith("ghs_"),n=e.startsWith("ghu_");return{type:"token",token:e,tokenType:t?"app":r?"installation":n?"user-to-server":"oauth"}}async function v(e,t,r,n){const o=t.endpoint.merge(r,n);return o.headers.authorization=function(e){return 3===e.split(/\./).length?`bearer ${e}`:`token ${e}`}(e),t(o)}var y=function(e){if(!e)throw new Error("[@octokit/auth-token] No token passed to createTokenAuth");if("string"!=typeof e)throw new Error("[@octokit/auth-token] Token passed to createTokenAuth is not a string");return e=e.replace(/^(token|bearer) +/i,""),Object.assign(h.bind(null,e),{hook:v.bind(null,e)})};const b="6.1.4",E=()=>{},T=console.warn.bind(console),w=console.error.bind(console),_=`octokit-core.js/${b} ${(0,n.$)()}`;function P(e){e.hook.wrap("request",(t,r)=>{e.log.debug("request",r);const n=Date.now(),o=e.request.endpoint.parse(r),i=o.url.replace(r.baseUrl,"");return t(r).then(t=>{const r=t.headers["x-github-request-id"];return e.log.info(`${o.method} ${i} - ${t.status} with id ${r} in ${Date.now()-n}ms`),t}).catch(t=>{const r=t.response?.headers["x-github-request-id"]||"UNKNOWN";throw e.log.error(`${o.method} ${i} - ${t.status} with id ${r} in ${Date.now()-n}ms`),t})})}function S(e,t,r){const n="function"==typeof t?t.endpoint(r):e.request.endpoint(t,r),o="function"==typeof t?t:e.request,i=n.method,s=n.headers;let a=n.url;return{[Symbol.asyncIterator]:()=>({async next(){if(!a)return{done:!0};try{const e=function(e){if(!e.data)return{...e,data:[]};if(!("total_count"in e.data)||"url"in e.data)return e;const t=e.data.incomplete_results,r=e.data.repository_selection,n=e.data.total_count;delete e.data.incomplete_results,delete e.data.repository_selection,delete e.data.total_count;const o=Object.keys(e.data)[0],i=e.data[o];return e.data=i,void 0!==t&&(e.data.incomplete_results=t),void 0!==r&&(e.data.repository_selection=r),e.data.total_count=n,e}(await o({method:i,url:a,headers:s}));return a=((e.headers.link||"").match(/<([^<>]+)>;\s*rel="next"/)||[])[1],{value:e}}catch(e){if(409!==e.status)throw e;return a="",{value:{status:200,headers:{},data:[]}}}}})}}function k(e,t,r,n){return"function"==typeof r&&(n=r,r=void 0),O(e,[],S(e,t,r)[Symbol.asyncIterator](),n)}function O(e,t,r,n){return r.next().then(o=>{if(o.done)return t;let i=!1;return t=t.concat(n?n(o.value,function(){i=!0}):o.value.data),i?t:O(e,t,r,n)})}function C(e){return{paginate:Object.assign(k.bind(null,e),{iterator:S.bind(null,e)})}}P.VERSION="5.3.1",Object.assign(k,{iterator:S}),C.VERSION="0.0.0-development";const R=new Map;for(const[e,t]of Object.entries({actions:{addCustomLabelsToSelfHostedRunnerForOrg:["POST /orgs/{org}/actions/runners/{runner_id}/labels"],addCustomLabelsToSelfHostedRunnerForRepo:["POST /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],addRepoAccessToSelfHostedRunnerGroupInOrg:["PUT /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories/{repository_id}"],addSelectedRepoToOrgSecret:["PUT /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}"],addSelectedRepoToOrgVariable:["PUT /orgs/{org}/actions/variables/{name}/repositories/{repository_id}"],approveWorkflowRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/approve"],cancelWorkflowRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/cancel"],createEnvironmentVariable:["POST /repos/{owner}/{repo}/environments/{environment_name}/variables"],createOrUpdateEnvironmentSecret:["PUT /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"],createOrUpdateOrgSecret:["PUT /orgs/{org}/actions/secrets/{secret_name}"],createOrUpdateRepoSecret:["PUT /repos/{owner}/{repo}/actions/secrets/{secret_name}"],createOrgVariable:["POST /orgs/{org}/actions/variables"],createRegistrationTokenForOrg:["POST /orgs/{org}/actions/runners/registration-token"],createRegistrationTokenForRepo:["POST /repos/{owner}/{repo}/actions/runners/registration-token"],createRemoveTokenForOrg:["POST /orgs/{org}/actions/runners/remove-token"],createRemoveTokenForRepo:["POST /repos/{owner}/{repo}/actions/runners/remove-token"],createRepoVariable:["POST /repos/{owner}/{repo}/actions/variables"],createWorkflowDispatch:["POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches"],deleteActionsCacheById:["DELETE /repos/{owner}/{repo}/actions/caches/{cache_id}"],deleteActionsCacheByKey:["DELETE /repos/{owner}/{repo}/actions/caches{?key,ref}"],deleteArtifact:["DELETE /repos/{owner}/{repo}/actions/artifacts/{artifact_id}"],deleteEnvironmentSecret:["DELETE /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"],deleteEnvironmentVariable:["DELETE /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"],deleteOrgSecret:["DELETE /orgs/{org}/actions/secrets/{secret_name}"],deleteOrgVariable:["DELETE /orgs/{org}/actions/variables/{name}"],deleteRepoSecret:["DELETE /repos/{owner}/{repo}/actions/secrets/{secret_name}"],deleteRepoVariable:["DELETE /repos/{owner}/{repo}/actions/variables/{name}"],deleteSelfHostedRunnerFromOrg:["DELETE /orgs/{org}/actions/runners/{runner_id}"],deleteSelfHostedRunnerFromRepo:["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}"],deleteWorkflowRun:["DELETE /repos/{owner}/{repo}/actions/runs/{run_id}"],deleteWorkflowRunLogs:["DELETE /repos/{owner}/{repo}/actions/runs/{run_id}/logs"],disableSelectedRepositoryGithubActionsOrganization:["DELETE /orgs/{org}/actions/permissions/repositories/{repository_id}"],disableWorkflow:["PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/disable"],downloadArtifact:["GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}/{archive_format}"],downloadJobLogsForWorkflowRun:["GET /repos/{owner}/{repo}/actions/jobs/{job_id}/logs"],downloadWorkflowRunAttemptLogs:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/logs"],downloadWorkflowRunLogs:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/logs"],enableSelectedRepositoryGithubActionsOrganization:["PUT /orgs/{org}/actions/permissions/repositories/{repository_id}"],enableWorkflow:["PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/enable"],forceCancelWorkflowRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/force-cancel"],generateRunnerJitconfigForOrg:["POST /orgs/{org}/actions/runners/generate-jitconfig"],generateRunnerJitconfigForRepo:["POST /repos/{owner}/{repo}/actions/runners/generate-jitconfig"],getActionsCacheList:["GET /repos/{owner}/{repo}/actions/caches"],getActionsCacheUsage:["GET /repos/{owner}/{repo}/actions/cache/usage"],getActionsCacheUsageByRepoForOrg:["GET /orgs/{org}/actions/cache/usage-by-repository"],getActionsCacheUsageForOrg:["GET /orgs/{org}/actions/cache/usage"],getAllowedActionsOrganization:["GET /orgs/{org}/actions/permissions/selected-actions"],getAllowedActionsRepository:["GET /repos/{owner}/{repo}/actions/permissions/selected-actions"],getArtifact:["GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}"],getCustomOidcSubClaimForRepo:["GET /repos/{owner}/{repo}/actions/oidc/customization/sub"],getEnvironmentPublicKey:["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/public-key"],getEnvironmentSecret:["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"],getEnvironmentVariable:["GET /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"],getGithubActionsDefaultWorkflowPermissionsOrganization:["GET /orgs/{org}/actions/permissions/workflow"],getGithubActionsDefaultWorkflowPermissionsRepository:["GET /repos/{owner}/{repo}/actions/permissions/workflow"],getGithubActionsPermissionsOrganization:["GET /orgs/{org}/actions/permissions"],getGithubActionsPermissionsRepository:["GET /repos/{owner}/{repo}/actions/permissions"],getJobForWorkflowRun:["GET /repos/{owner}/{repo}/actions/jobs/{job_id}"],getOrgPublicKey:["GET /orgs/{org}/actions/secrets/public-key"],getOrgSecret:["GET /orgs/{org}/actions/secrets/{secret_name}"],getOrgVariable:["GET /orgs/{org}/actions/variables/{name}"],getPendingDeploymentsForRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments"],getRepoPermissions:["GET /repos/{owner}/{repo}/actions/permissions",{},{renamed:["actions","getGithubActionsPermissionsRepository"]}],getRepoPublicKey:["GET /repos/{owner}/{repo}/actions/secrets/public-key"],getRepoSecret:["GET /repos/{owner}/{repo}/actions/secrets/{secret_name}"],getRepoVariable:["GET /repos/{owner}/{repo}/actions/variables/{name}"],getReviewsForRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/approvals"],getSelfHostedRunnerForOrg:["GET /orgs/{org}/actions/runners/{runner_id}"],getSelfHostedRunnerForRepo:["GET /repos/{owner}/{repo}/actions/runners/{runner_id}"],getWorkflow:["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}"],getWorkflowAccessToRepository:["GET /repos/{owner}/{repo}/actions/permissions/access"],getWorkflowRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}"],getWorkflowRunAttempt:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}"],getWorkflowRunUsage:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/timing"],getWorkflowUsage:["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/timing"],listArtifactsForRepo:["GET /repos/{owner}/{repo}/actions/artifacts"],listEnvironmentSecrets:["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets"],listEnvironmentVariables:["GET /repos/{owner}/{repo}/environments/{environment_name}/variables"],listJobsForWorkflowRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs"],listJobsForWorkflowRunAttempt:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs"],listLabelsForSelfHostedRunnerForOrg:["GET /orgs/{org}/actions/runners/{runner_id}/labels"],listLabelsForSelfHostedRunnerForRepo:["GET /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],listOrgSecrets:["GET /orgs/{org}/actions/secrets"],listOrgVariables:["GET /orgs/{org}/actions/variables"],listRepoOrganizationSecrets:["GET /repos/{owner}/{repo}/actions/organization-secrets"],listRepoOrganizationVariables:["GET /repos/{owner}/{repo}/actions/organization-variables"],listRepoSecrets:["GET /repos/{owner}/{repo}/actions/secrets"],listRepoVariables:["GET /repos/{owner}/{repo}/actions/variables"],listRepoWorkflows:["GET /repos/{owner}/{repo}/actions/workflows"],listRunnerApplicationsForOrg:["GET /orgs/{org}/actions/runners/downloads"],listRunnerApplicationsForRepo:["GET /repos/{owner}/{repo}/actions/runners/downloads"],listSelectedReposForOrgSecret:["GET /orgs/{org}/actions/secrets/{secret_name}/repositories"],listSelectedReposForOrgVariable:["GET /orgs/{org}/actions/variables/{name}/repositories"],listSelectedRepositoriesEnabledGithubActionsOrganization:["GET /orgs/{org}/actions/permissions/repositories"],listSelfHostedRunnersForOrg:["GET /orgs/{org}/actions/runners"],listSelfHostedRunnersForRepo:["GET /repos/{owner}/{repo}/actions/runners"],listWorkflowRunArtifacts:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts"],listWorkflowRuns:["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs"],listWorkflowRunsForRepo:["GET /repos/{owner}/{repo}/actions/runs"],reRunJobForWorkflowRun:["POST /repos/{owner}/{repo}/actions/jobs/{job_id}/rerun"],reRunWorkflow:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun"],reRunWorkflowFailedJobs:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun-failed-jobs"],removeAllCustomLabelsFromSelfHostedRunnerForOrg:["DELETE /orgs/{org}/actions/runners/{runner_id}/labels"],removeAllCustomLabelsFromSelfHostedRunnerForRepo:["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],removeCustomLabelFromSelfHostedRunnerForOrg:["DELETE /orgs/{org}/actions/runners/{runner_id}/labels/{name}"],removeCustomLabelFromSelfHostedRunnerForRepo:["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels/{name}"],removeSelectedRepoFromOrgSecret:["DELETE /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}"],removeSelectedRepoFromOrgVariable:["DELETE /orgs/{org}/actions/variables/{name}/repositories/{repository_id}"],reviewCustomGatesForRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/deployment_protection_rule"],reviewPendingDeploymentsForRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments"],setAllowedActionsOrganization:["PUT /orgs/{org}/actions/permissions/selected-actions"],setAllowedActionsRepository:["PUT /repos/{owner}/{repo}/actions/permissions/selected-actions"],setCustomLabelsForSelfHostedRunnerForOrg:["PUT /orgs/{org}/actions/runners/{runner_id}/labels"],setCustomLabelsForSelfHostedRunnerForRepo:["PUT /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],setCustomOidcSubClaimForRepo:["PUT /repos/{owner}/{repo}/actions/oidc/customization/sub"],setGithubActionsDefaultWorkflowPermissionsOrganization:["PUT /orgs/{org}/actions/permissions/workflow"],setGithubActionsDefaultWorkflowPermissionsRepository:["PUT /repos/{owner}/{repo}/actions/permissions/workflow"],setGithubActionsPermissionsOrganization:["PUT /orgs/{org}/actions/permissions"],setGithubActionsPermissionsRepository:["PUT /repos/{owner}/{repo}/actions/permissions"],setSelectedReposForOrgSecret:["PUT /orgs/{org}/actions/secrets/{secret_name}/repositories"],setSelectedReposForOrgVariable:["PUT /orgs/{org}/actions/variables/{name}/repositories"],setSelectedRepositoriesEnabledGithubActionsOrganization:["PUT /orgs/{org}/actions/permissions/repositories"],setWorkflowAccessToRepository:["PUT /repos/{owner}/{repo}/actions/permissions/access"],updateEnvironmentVariable:["PATCH /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"],updateOrgVariable:["PATCH /orgs/{org}/actions/variables/{name}"],updateRepoVariable:["PATCH /repos/{owner}/{repo}/actions/variables/{name}"]},activity:{checkRepoIsStarredByAuthenticatedUser:["GET /user/starred/{owner}/{repo}"],deleteRepoSubscription:["DELETE /repos/{owner}/{repo}/subscription"],deleteThreadSubscription:["DELETE /notifications/threads/{thread_id}/subscription"],getFeeds:["GET /feeds"],getRepoSubscription:["GET /repos/{owner}/{repo}/subscription"],getThread:["GET /notifications/threads/{thread_id}"],getThreadSubscriptionForAuthenticatedUser:["GET /notifications/threads/{thread_id}/subscription"],listEventsForAuthenticatedUser:["GET /users/{username}/events"],listNotificationsForAuthenticatedUser:["GET /notifications"],listOrgEventsForAuthenticatedUser:["GET /users/{username}/events/orgs/{org}"],listPublicEvents:["GET /events"],listPublicEventsForRepoNetwork:["GET /networks/{owner}/{repo}/events"],listPublicEventsForUser:["GET /users/{username}/events/public"],listPublicOrgEvents:["GET /orgs/{org}/events"],listReceivedEventsForUser:["GET /users/{username}/received_events"],listReceivedPublicEventsForUser:["GET /users/{username}/received_events/public"],listRepoEvents:["GET /repos/{owner}/{repo}/events"],listRepoNotificationsForAuthenticatedUser:["GET /repos/{owner}/{repo}/notifications"],listReposStarredByAuthenticatedUser:["GET /user/starred"],listReposStarredByUser:["GET /users/{username}/starred"],listReposWatchedByUser:["GET /users/{username}/subscriptions"],listStargazersForRepo:["GET /repos/{owner}/{repo}/stargazers"],listWatchedReposForAuthenticatedUser:["GET /user/subscriptions"],listWatchersForRepo:["GET /repos/{owner}/{repo}/subscribers"],markNotificationsAsRead:["PUT /notifications"],markRepoNotificationsAsRead:["PUT /repos/{owner}/{repo}/notifications"],markThreadAsDone:["DELETE /notifications/threads/{thread_id}"],markThreadAsRead:["PATCH /notifications/threads/{thread_id}"],setRepoSubscription:["PUT /repos/{owner}/{repo}/subscription"],setThreadSubscription:["PUT /notifications/threads/{thread_id}/subscription"],starRepoForAuthenticatedUser:["PUT /user/starred/{owner}/{repo}"],unstarRepoForAuthenticatedUser:["DELETE /user/starred/{owner}/{repo}"]},apps:{addRepoToInstallation:["PUT /user/installations/{installation_id}/repositories/{repository_id}",{},{renamed:["apps","addRepoToInstallationForAuthenticatedUser"]}],addRepoToInstallationForAuthenticatedUser:["PUT /user/installations/{installation_id}/repositories/{repository_id}"],checkToken:["POST /applications/{client_id}/token"],createFromManifest:["POST /app-manifests/{code}/conversions"],createInstallationAccessToken:["POST /app/installations/{installation_id}/access_tokens"],deleteAuthorization:["DELETE /applications/{client_id}/grant"],deleteInstallation:["DELETE /app/installations/{installation_id}"],deleteToken:["DELETE /applications/{client_id}/token"],getAuthenticated:["GET /app"],getBySlug:["GET /apps/{app_slug}"],getInstallation:["GET /app/installations/{installation_id}"],getOrgInstallation:["GET /orgs/{org}/installation"],getRepoInstallation:["GET /repos/{owner}/{repo}/installation"],getSubscriptionPlanForAccount:["GET /marketplace_listing/accounts/{account_id}"],getSubscriptionPlanForAccountStubbed:["GET /marketplace_listing/stubbed/accounts/{account_id}"],getUserInstallation:["GET /users/{username}/installation"],getWebhookConfigForApp:["GET /app/hook/config"],getWebhookDelivery:["GET /app/hook/deliveries/{delivery_id}"],listAccountsForPlan:["GET /marketplace_listing/plans/{plan_id}/accounts"],listAccountsForPlanStubbed:["GET /marketplace_listing/stubbed/plans/{plan_id}/accounts"],listInstallationReposForAuthenticatedUser:["GET /user/installations/{installation_id}/repositories"],listInstallationRequestsForAuthenticatedApp:["GET /app/installation-requests"],listInstallations:["GET /app/installations"],listInstallationsForAuthenticatedUser:["GET /user/installations"],listPlans:["GET /marketplace_listing/plans"],listPlansStubbed:["GET /marketplace_listing/stubbed/plans"],listReposAccessibleToInstallation:["GET /installation/repositories"],listSubscriptionsForAuthenticatedUser:["GET /user/marketplace_purchases"],listSubscriptionsForAuthenticatedUserStubbed:["GET /user/marketplace_purchases/stubbed"],listWebhookDeliveries:["GET /app/hook/deliveries"],redeliverWebhookDelivery:["POST /app/hook/deliveries/{delivery_id}/attempts"],removeRepoFromInstallation:["DELETE /user/installations/{installation_id}/repositories/{repository_id}",{},{renamed:["apps","removeRepoFromInstallationForAuthenticatedUser"]}],removeRepoFromInstallationForAuthenticatedUser:["DELETE /user/installations/{installation_id}/repositories/{repository_id}"],resetToken:["PATCH /applications/{client_id}/token"],revokeInstallationAccessToken:["DELETE /installation/token"],scopeToken:["POST /applications/{client_id}/token/scoped"],suspendInstallation:["PUT /app/installations/{installation_id}/suspended"],unsuspendInstallation:["DELETE /app/installations/{installation_id}/suspended"],updateWebhookConfigForApp:["PATCH /app/hook/config"]},billing:{getGithubActionsBillingOrg:["GET /orgs/{org}/settings/billing/actions"],getGithubActionsBillingUser:["GET /users/{username}/settings/billing/actions"],getGithubBillingUsageReportOrg:["GET /organizations/{org}/settings/billing/usage"],getGithubPackagesBillingOrg:["GET /orgs/{org}/settings/billing/packages"],getGithubPackagesBillingUser:["GET /users/{username}/settings/billing/packages"],getSharedStorageBillingOrg:["GET /orgs/{org}/settings/billing/shared-storage"],getSharedStorageBillingUser:["GET /users/{username}/settings/billing/shared-storage"]},checks:{create:["POST /repos/{owner}/{repo}/check-runs"],createSuite:["POST /repos/{owner}/{repo}/check-suites"],get:["GET /repos/{owner}/{repo}/check-runs/{check_run_id}"],getSuite:["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}"],listAnnotations:["GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations"],listForRef:["GET /repos/{owner}/{repo}/commits/{ref}/check-runs"],listForSuite:["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs"],listSuitesForRef:["GET /repos/{owner}/{repo}/commits/{ref}/check-suites"],rerequestRun:["POST /repos/{owner}/{repo}/check-runs/{check_run_id}/rerequest"],rerequestSuite:["POST /repos/{owner}/{repo}/check-suites/{check_suite_id}/rerequest"],setSuitesPreferences:["PATCH /repos/{owner}/{repo}/check-suites/preferences"],update:["PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}"]},codeScanning:{commitAutofix:["POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix/commits"],createAutofix:["POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix"],createVariantAnalysis:["POST /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses"],deleteAnalysis:["DELETE /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}{?confirm_delete}"],deleteCodeqlDatabase:["DELETE /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}"],getAlert:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}",{},{renamedParameters:{alert_id:"alert_number"}}],getAnalysis:["GET /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}"],getAutofix:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix"],getCodeqlDatabase:["GET /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}"],getDefaultSetup:["GET /repos/{owner}/{repo}/code-scanning/default-setup"],getSarif:["GET /repos/{owner}/{repo}/code-scanning/sarifs/{sarif_id}"],getVariantAnalysis:["GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}"],getVariantAnalysisRepoTask:["GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}/repos/{repo_owner}/{repo_name}"],listAlertInstances:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances"],listAlertsForOrg:["GET /orgs/{org}/code-scanning/alerts"],listAlertsForRepo:["GET /repos/{owner}/{repo}/code-scanning/alerts"],listAlertsInstances:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances",{},{renamed:["codeScanning","listAlertInstances"]}],listCodeqlDatabases:["GET /repos/{owner}/{repo}/code-scanning/codeql/databases"],listRecentAnalyses:["GET /repos/{owner}/{repo}/code-scanning/analyses"],updateAlert:["PATCH /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}"],updateDefaultSetup:["PATCH /repos/{owner}/{repo}/code-scanning/default-setup"],uploadSarif:["POST /repos/{owner}/{repo}/code-scanning/sarifs"]},codeSecurity:{attachConfiguration:["POST /orgs/{org}/code-security/configurations/{configuration_id}/attach"],attachEnterpriseConfiguration:["POST /enterprises/{enterprise}/code-security/configurations/{configuration_id}/attach"],createConfiguration:["POST /orgs/{org}/code-security/configurations"],createConfigurationForEnterprise:["POST /enterprises/{enterprise}/code-security/configurations"],deleteConfiguration:["DELETE /orgs/{org}/code-security/configurations/{configuration_id}"],deleteConfigurationForEnterprise:["DELETE /enterprises/{enterprise}/code-security/configurations/{configuration_id}"],detachConfiguration:["DELETE /orgs/{org}/code-security/configurations/detach"],getConfiguration:["GET /orgs/{org}/code-security/configurations/{configuration_id}"],getConfigurationForRepository:["GET /repos/{owner}/{repo}/code-security-configuration"],getConfigurationsForEnterprise:["GET /enterprises/{enterprise}/code-security/configurations"],getConfigurationsForOrg:["GET /orgs/{org}/code-security/configurations"],getDefaultConfigurations:["GET /orgs/{org}/code-security/configurations/defaults"],getDefaultConfigurationsForEnterprise:["GET /enterprises/{enterprise}/code-security/configurations/defaults"],getRepositoriesForConfiguration:["GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories"],getRepositoriesForEnterpriseConfiguration:["GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories"],getSingleConfigurationForEnterprise:["GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}"],setConfigurationAsDefault:["PUT /orgs/{org}/code-security/configurations/{configuration_id}/defaults"],setConfigurationAsDefaultForEnterprise:["PUT /enterprises/{enterprise}/code-security/configurations/{configuration_id}/defaults"],updateConfiguration:["PATCH /orgs/{org}/code-security/configurations/{configuration_id}"],updateEnterpriseConfiguration:["PATCH /enterprises/{enterprise}/code-security/configurations/{configuration_id}"]},codesOfConduct:{getAllCodesOfConduct:["GET /codes_of_conduct"],getConductCode:["GET /codes_of_conduct/{key}"]},codespaces:{addRepositoryForSecretForAuthenticatedUser:["PUT /user/codespaces/secrets/{secret_name}/repositories/{repository_id}"],addSelectedRepoToOrgSecret:["PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}"],checkPermissionsForDevcontainer:["GET /repos/{owner}/{repo}/codespaces/permissions_check"],codespaceMachinesForAuthenticatedUser:["GET /user/codespaces/{codespace_name}/machines"],createForAuthenticatedUser:["POST /user/codespaces"],createOrUpdateOrgSecret:["PUT /orgs/{org}/codespaces/secrets/{secret_name}"],createOrUpdateRepoSecret:["PUT /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"],createOrUpdateSecretForAuthenticatedUser:["PUT /user/codespaces/secrets/{secret_name}"],createWithPrForAuthenticatedUser:["POST /repos/{owner}/{repo}/pulls/{pull_number}/codespaces"],createWithRepoForAuthenticatedUser:["POST /repos/{owner}/{repo}/codespaces"],deleteForAuthenticatedUser:["DELETE /user/codespaces/{codespace_name}"],deleteFromOrganization:["DELETE /orgs/{org}/members/{username}/codespaces/{codespace_name}"],deleteOrgSecret:["DELETE /orgs/{org}/codespaces/secrets/{secret_name}"],deleteRepoSecret:["DELETE /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"],deleteSecretForAuthenticatedUser:["DELETE /user/codespaces/secrets/{secret_name}"],exportForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/exports"],getCodespacesForUserInOrg:["GET /orgs/{org}/members/{username}/codespaces"],getExportDetailsForAuthenticatedUser:["GET /user/codespaces/{codespace_name}/exports/{export_id}"],getForAuthenticatedUser:["GET /user/codespaces/{codespace_name}"],getOrgPublicKey:["GET /orgs/{org}/codespaces/secrets/public-key"],getOrgSecret:["GET /orgs/{org}/codespaces/secrets/{secret_name}"],getPublicKeyForAuthenticatedUser:["GET /user/codespaces/secrets/public-key"],getRepoPublicKey:["GET /repos/{owner}/{repo}/codespaces/secrets/public-key"],getRepoSecret:["GET /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"],getSecretForAuthenticatedUser:["GET /user/codespaces/secrets/{secret_name}"],listDevcontainersInRepositoryForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces/devcontainers"],listForAuthenticatedUser:["GET /user/codespaces"],listInOrganization:["GET /orgs/{org}/codespaces",{},{renamedParameters:{org_id:"org"}}],listInRepositoryForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces"],listOrgSecrets:["GET /orgs/{org}/codespaces/secrets"],listRepoSecrets:["GET /repos/{owner}/{repo}/codespaces/secrets"],listRepositoriesForSecretForAuthenticatedUser:["GET /user/codespaces/secrets/{secret_name}/repositories"],listSecretsForAuthenticatedUser:["GET /user/codespaces/secrets"],listSelectedReposForOrgSecret:["GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories"],preFlightWithRepoForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces/new"],publishForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/publish"],removeRepositoryForSecretForAuthenticatedUser:["DELETE /user/codespaces/secrets/{secret_name}/repositories/{repository_id}"],removeSelectedRepoFromOrgSecret:["DELETE /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}"],repoMachinesForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces/machines"],setRepositoriesForSecretForAuthenticatedUser:["PUT /user/codespaces/secrets/{secret_name}/repositories"],setSelectedReposForOrgSecret:["PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories"],startForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/start"],stopForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/stop"],stopInOrganization:["POST /orgs/{org}/members/{username}/codespaces/{codespace_name}/stop"],updateForAuthenticatedUser:["PATCH /user/codespaces/{codespace_name}"]},copilot:{addCopilotSeatsForTeams:["POST /orgs/{org}/copilot/billing/selected_teams"],addCopilotSeatsForUsers:["POST /orgs/{org}/copilot/billing/selected_users"],cancelCopilotSeatAssignmentForTeams:["DELETE /orgs/{org}/copilot/billing/selected_teams"],cancelCopilotSeatAssignmentForUsers:["DELETE /orgs/{org}/copilot/billing/selected_users"],copilotMetricsForOrganization:["GET /orgs/{org}/copilot/metrics"],copilotMetricsForTeam:["GET /orgs/{org}/team/{team_slug}/copilot/metrics"],getCopilotOrganizationDetails:["GET /orgs/{org}/copilot/billing"],getCopilotSeatDetailsForUser:["GET /orgs/{org}/members/{username}/copilot"],listCopilotSeats:["GET /orgs/{org}/copilot/billing/seats"],usageMetricsForOrg:["GET /orgs/{org}/copilot/usage"],usageMetricsForTeam:["GET /orgs/{org}/team/{team_slug}/copilot/usage"]},dependabot:{addSelectedRepoToOrgSecret:["PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}"],createOrUpdateOrgSecret:["PUT /orgs/{org}/dependabot/secrets/{secret_name}"],createOrUpdateRepoSecret:["PUT /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"],deleteOrgSecret:["DELETE /orgs/{org}/dependabot/secrets/{secret_name}"],deleteRepoSecret:["DELETE /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"],getAlert:["GET /repos/{owner}/{repo}/dependabot/alerts/{alert_number}"],getOrgPublicKey:["GET /orgs/{org}/dependabot/secrets/public-key"],getOrgSecret:["GET /orgs/{org}/dependabot/secrets/{secret_name}"],getRepoPublicKey:["GET /repos/{owner}/{repo}/dependabot/secrets/public-key"],getRepoSecret:["GET /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"],listAlertsForEnterprise:["GET /enterprises/{enterprise}/dependabot/alerts"],listAlertsForOrg:["GET /orgs/{org}/dependabot/alerts"],listAlertsForRepo:["GET /repos/{owner}/{repo}/dependabot/alerts"],listOrgSecrets:["GET /orgs/{org}/dependabot/secrets"],listRepoSecrets:["GET /repos/{owner}/{repo}/dependabot/secrets"],listSelectedReposForOrgSecret:["GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories"],removeSelectedRepoFromOrgSecret:["DELETE /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}"],setSelectedReposForOrgSecret:["PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories"],updateAlert:["PATCH /repos/{owner}/{repo}/dependabot/alerts/{alert_number}"]},dependencyGraph:{createRepositorySnapshot:["POST /repos/{owner}/{repo}/dependency-graph/snapshots"],diffRange:["GET /repos/{owner}/{repo}/dependency-graph/compare/{basehead}"],exportSbom:["GET /repos/{owner}/{repo}/dependency-graph/sbom"]},emojis:{get:["GET /emojis"]},gists:{checkIsStarred:["GET /gists/{gist_id}/star"],create:["POST /gists"],createComment:["POST /gists/{gist_id}/comments"],delete:["DELETE /gists/{gist_id}"],deleteComment:["DELETE /gists/{gist_id}/comments/{comment_id}"],fork:["POST /gists/{gist_id}/forks"],get:["GET /gists/{gist_id}"],getComment:["GET /gists/{gist_id}/comments/{comment_id}"],getRevision:["GET /gists/{gist_id}/{sha}"],list:["GET /gists"],listComments:["GET /gists/{gist_id}/comments"],listCommits:["GET /gists/{gist_id}/commits"],listForUser:["GET /users/{username}/gists"],listForks:["GET /gists/{gist_id}/forks"],listPublic:["GET /gists/public"],listStarred:["GET /gists/starred"],star:["PUT /gists/{gist_id}/star"],unstar:["DELETE /gists/{gist_id}/star"],update:["PATCH /gists/{gist_id}"],updateComment:["PATCH /gists/{gist_id}/comments/{comment_id}"]},git:{createBlob:["POST /repos/{owner}/{repo}/git/blobs"],createCommit:["POST /repos/{owner}/{repo}/git/commits"],createRef:["POST /repos/{owner}/{repo}/git/refs"],createTag:["POST /repos/{owner}/{repo}/git/tags"],createTree:["POST /repos/{owner}/{repo}/git/trees"],deleteRef:["DELETE /repos/{owner}/{repo}/git/refs/{ref}"],getBlob:["GET /repos/{owner}/{repo}/git/blobs/{file_sha}"],getCommit:["GET /repos/{owner}/{repo}/git/commits/{commit_sha}"],getRef:["GET /repos/{owner}/{repo}/git/ref/{ref}"],getTag:["GET /repos/{owner}/{repo}/git/tags/{tag_sha}"],getTree:["GET /repos/{owner}/{repo}/git/trees/{tree_sha}"],listMatchingRefs:["GET /repos/{owner}/{repo}/git/matching-refs/{ref}"],updateRef:["PATCH /repos/{owner}/{repo}/git/refs/{ref}"]},gitignore:{getAllTemplates:["GET /gitignore/templates"],getTemplate:["GET /gitignore/templates/{name}"]},interactions:{getRestrictionsForAuthenticatedUser:["GET /user/interaction-limits"],getRestrictionsForOrg:["GET /orgs/{org}/interaction-limits"],getRestrictionsForRepo:["GET /repos/{owner}/{repo}/interaction-limits"],getRestrictionsForYourPublicRepos:["GET /user/interaction-limits",{},{renamed:["interactions","getRestrictionsForAuthenticatedUser"]}],removeRestrictionsForAuthenticatedUser:["DELETE /user/interaction-limits"],removeRestrictionsForOrg:["DELETE /orgs/{org}/interaction-limits"],removeRestrictionsForRepo:["DELETE /repos/{owner}/{repo}/interaction-limits"],removeRestrictionsForYourPublicRepos:["DELETE /user/interaction-limits",{},{renamed:["interactions","removeRestrictionsForAuthenticatedUser"]}],setRestrictionsForAuthenticatedUser:["PUT /user/interaction-limits"],setRestrictionsForOrg:["PUT /orgs/{org}/interaction-limits"],setRestrictionsForRepo:["PUT /repos/{owner}/{repo}/interaction-limits"],setRestrictionsForYourPublicRepos:["PUT /user/interaction-limits",{},{renamed:["interactions","setRestrictionsForAuthenticatedUser"]}]},issues:{addAssignees:["POST /repos/{owner}/{repo}/issues/{issue_number}/assignees"],addLabels:["POST /repos/{owner}/{repo}/issues/{issue_number}/labels"],addSubIssue:["POST /repos/{owner}/{repo}/issues/{issue_number}/sub_issues"],checkUserCanBeAssigned:["GET /repos/{owner}/{repo}/assignees/{assignee}"],checkUserCanBeAssignedToIssue:["GET /repos/{owner}/{repo}/issues/{issue_number}/assignees/{assignee}"],create:["POST /repos/{owner}/{repo}/issues"],createComment:["POST /repos/{owner}/{repo}/issues/{issue_number}/comments"],createLabel:["POST /repos/{owner}/{repo}/labels"],createMilestone:["POST /repos/{owner}/{repo}/milestones"],deleteComment:["DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}"],deleteLabel:["DELETE /repos/{owner}/{repo}/labels/{name}"],deleteMilestone:["DELETE /repos/{owner}/{repo}/milestones/{milestone_number}"],get:["GET /repos/{owner}/{repo}/issues/{issue_number}"],getComment:["GET /repos/{owner}/{repo}/issues/comments/{comment_id}"],getEvent:["GET /repos/{owner}/{repo}/issues/events/{event_id}"],getLabel:["GET /repos/{owner}/{repo}/labels/{name}"],getMilestone:["GET /repos/{owner}/{repo}/milestones/{milestone_number}"],list:["GET /issues"],listAssignees:["GET /repos/{owner}/{repo}/assignees"],listComments:["GET /repos/{owner}/{repo}/issues/{issue_number}/comments"],listCommentsForRepo:["GET /repos/{owner}/{repo}/issues/comments"],listEvents:["GET /repos/{owner}/{repo}/issues/{issue_number}/events"],listEventsForRepo:["GET /repos/{owner}/{repo}/issues/events"],listEventsForTimeline:["GET /repos/{owner}/{repo}/issues/{issue_number}/timeline"],listForAuthenticatedUser:["GET /user/issues"],listForOrg:["GET /orgs/{org}/issues"],listForRepo:["GET /repos/{owner}/{repo}/issues"],listLabelsForMilestone:["GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels"],listLabelsForRepo:["GET /repos/{owner}/{repo}/labels"],listLabelsOnIssue:["GET /repos/{owner}/{repo}/issues/{issue_number}/labels"],listMilestones:["GET /repos/{owner}/{repo}/milestones"],listSubIssues:["GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues"],lock:["PUT /repos/{owner}/{repo}/issues/{issue_number}/lock"],removeAllLabels:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels"],removeAssignees:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/assignees"],removeLabel:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels/{name}"],removeSubIssue:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/sub_issue"],reprioritizeSubIssue:["PATCH /repos/{owner}/{repo}/issues/{issue_number}/sub_issues/priority"],setLabels:["PUT /repos/{owner}/{repo}/issues/{issue_number}/labels"],unlock:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/lock"],update:["PATCH /repos/{owner}/{repo}/issues/{issue_number}"],updateComment:["PATCH /repos/{owner}/{repo}/issues/comments/{comment_id}"],updateLabel:["PATCH /repos/{owner}/{repo}/labels/{name}"],updateMilestone:["PATCH /repos/{owner}/{repo}/milestones/{milestone_number}"]},licenses:{get:["GET /licenses/{license}"],getAllCommonlyUsed:["GET /licenses"],getForRepo:["GET /repos/{owner}/{repo}/license"]},markdown:{render:["POST /markdown"],renderRaw:["POST /markdown/raw",{headers:{"content-type":"text/plain; charset=utf-8"}}]},meta:{get:["GET /meta"],getAllVersions:["GET /versions"],getOctocat:["GET /octocat"],getZen:["GET /zen"],root:["GET /"]},migrations:{deleteArchiveForAuthenticatedUser:["DELETE /user/migrations/{migration_id}/archive"],deleteArchiveForOrg:["DELETE /orgs/{org}/migrations/{migration_id}/archive"],downloadArchiveForOrg:["GET /orgs/{org}/migrations/{migration_id}/archive"],getArchiveForAuthenticatedUser:["GET /user/migrations/{migration_id}/archive"],getStatusForAuthenticatedUser:["GET /user/migrations/{migration_id}"],getStatusForOrg:["GET /orgs/{org}/migrations/{migration_id}"],listForAuthenticatedUser:["GET /user/migrations"],listForOrg:["GET /orgs/{org}/migrations"],listReposForAuthenticatedUser:["GET /user/migrations/{migration_id}/repositories"],listReposForOrg:["GET /orgs/{org}/migrations/{migration_id}/repositories"],listReposForUser:["GET /user/migrations/{migration_id}/repositories",{},{renamed:["migrations","listReposForAuthenticatedUser"]}],startForAuthenticatedUser:["POST /user/migrations"],startForOrg:["POST /orgs/{org}/migrations"],unlockRepoForAuthenticatedUser:["DELETE /user/migrations/{migration_id}/repos/{repo_name}/lock"],unlockRepoForOrg:["DELETE /orgs/{org}/migrations/{migration_id}/repos/{repo_name}/lock"]},oidc:{getOidcCustomSubTemplateForOrg:["GET /orgs/{org}/actions/oidc/customization/sub"],updateOidcCustomSubTemplateForOrg:["PUT /orgs/{org}/actions/oidc/customization/sub"]},orgs:{addSecurityManagerTeam:["PUT /orgs/{org}/security-managers/teams/{team_slug}",{},{deprecated:"octokit.rest.orgs.addSecurityManagerTeam() is deprecated, see https://docs.github.com/rest/orgs/security-managers#add-a-security-manager-team"}],assignTeamToOrgRole:["PUT /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}"],assignUserToOrgRole:["PUT /orgs/{org}/organization-roles/users/{username}/{role_id}"],blockUser:["PUT /orgs/{org}/blocks/{username}"],cancelInvitation:["DELETE /orgs/{org}/invitations/{invitation_id}"],checkBlockedUser:["GET /orgs/{org}/blocks/{username}"],checkMembershipForUser:["GET /orgs/{org}/members/{username}"],checkPublicMembershipForUser:["GET /orgs/{org}/public_members/{username}"],convertMemberToOutsideCollaborator:["PUT /orgs/{org}/outside_collaborators/{username}"],createInvitation:["POST /orgs/{org}/invitations"],createOrUpdateCustomProperties:["PATCH /orgs/{org}/properties/schema"],createOrUpdateCustomPropertiesValuesForRepos:["PATCH /orgs/{org}/properties/values"],createOrUpdateCustomProperty:["PUT /orgs/{org}/properties/schema/{custom_property_name}"],createWebhook:["POST /orgs/{org}/hooks"],delete:["DELETE /orgs/{org}"],deleteWebhook:["DELETE /orgs/{org}/hooks/{hook_id}"],enableOrDisableSecurityProductOnAllOrgRepos:["POST /orgs/{org}/{security_product}/{enablement}",{},{deprecated:"octokit.rest.orgs.enableOrDisableSecurityProductOnAllOrgRepos() is deprecated, see https://docs.github.com/rest/orgs/orgs#enable-or-disable-a-security-feature-for-an-organization"}],get:["GET /orgs/{org}"],getAllCustomProperties:["GET /orgs/{org}/properties/schema"],getCustomProperty:["GET /orgs/{org}/properties/schema/{custom_property_name}"],getMembershipForAuthenticatedUser:["GET /user/memberships/orgs/{org}"],getMembershipForUser:["GET /orgs/{org}/memberships/{username}"],getOrgRole:["GET /orgs/{org}/organization-roles/{role_id}"],getWebhook:["GET /orgs/{org}/hooks/{hook_id}"],getWebhookConfigForOrg:["GET /orgs/{org}/hooks/{hook_id}/config"],getWebhookDelivery:["GET /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}"],list:["GET /organizations"],listAppInstallations:["GET /orgs/{org}/installations"],listAttestations:["GET /orgs/{org}/attestations/{subject_digest}"],listBlockedUsers:["GET /orgs/{org}/blocks"],listCustomPropertiesValuesForRepos:["GET /orgs/{org}/properties/values"],listFailedInvitations:["GET /orgs/{org}/failed_invitations"],listForAuthenticatedUser:["GET /user/orgs"],listForUser:["GET /users/{username}/orgs"],listInvitationTeams:["GET /orgs/{org}/invitations/{invitation_id}/teams"],listMembers:["GET /orgs/{org}/members"],listMembershipsForAuthenticatedUser:["GET /user/memberships/orgs"],listOrgRoleTeams:["GET /orgs/{org}/organization-roles/{role_id}/teams"],listOrgRoleUsers:["GET /orgs/{org}/organization-roles/{role_id}/users"],listOrgRoles:["GET /orgs/{org}/organization-roles"],listOrganizationFineGrainedPermissions:["GET /orgs/{org}/organization-fine-grained-permissions"],listOutsideCollaborators:["GET /orgs/{org}/outside_collaborators"],listPatGrantRepositories:["GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories"],listPatGrantRequestRepositories:["GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories"],listPatGrantRequests:["GET /orgs/{org}/personal-access-token-requests"],listPatGrants:["GET /orgs/{org}/personal-access-tokens"],listPendingInvitations:["GET /orgs/{org}/invitations"],listPublicMembers:["GET /orgs/{org}/public_members"],listSecurityManagerTeams:["GET /orgs/{org}/security-managers",{},{deprecated:"octokit.rest.orgs.listSecurityManagerTeams() is deprecated, see https://docs.github.com/rest/orgs/security-managers#list-security-manager-teams"}],listWebhookDeliveries:["GET /orgs/{org}/hooks/{hook_id}/deliveries"],listWebhooks:["GET /orgs/{org}/hooks"],pingWebhook:["POST /orgs/{org}/hooks/{hook_id}/pings"],redeliverWebhookDelivery:["POST /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}/attempts"],removeCustomProperty:["DELETE /orgs/{org}/properties/schema/{custom_property_name}"],removeMember:["DELETE /orgs/{org}/members/{username}"],removeMembershipForUser:["DELETE /orgs/{org}/memberships/{username}"],removeOutsideCollaborator:["DELETE /orgs/{org}/outside_collaborators/{username}"],removePublicMembershipForAuthenticatedUser:["DELETE /orgs/{org}/public_members/{username}"],removeSecurityManagerTeam:["DELETE /orgs/{org}/security-managers/teams/{team_slug}",{},{deprecated:"octokit.rest.orgs.removeSecurityManagerTeam() is deprecated, see https://docs.github.com/rest/orgs/security-managers#remove-a-security-manager-team"}],reviewPatGrantRequest:["POST /orgs/{org}/personal-access-token-requests/{pat_request_id}"],reviewPatGrantRequestsInBulk:["POST /orgs/{org}/personal-access-token-requests"],revokeAllOrgRolesTeam:["DELETE /orgs/{org}/organization-roles/teams/{team_slug}"],revokeAllOrgRolesUser:["DELETE /orgs/{org}/organization-roles/users/{username}"],revokeOrgRoleTeam:["DELETE /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}"],revokeOrgRoleUser:["DELETE /orgs/{org}/organization-roles/users/{username}/{role_id}"],setMembershipForUser:["PUT /orgs/{org}/memberships/{username}"],setPublicMembershipForAuthenticatedUser:["PUT /orgs/{org}/public_members/{username}"],unblockUser:["DELETE /orgs/{org}/blocks/{username}"],update:["PATCH /orgs/{org}"],updateMembershipForAuthenticatedUser:["PATCH /user/memberships/orgs/{org}"],updatePatAccess:["POST /orgs/{org}/personal-access-tokens/{pat_id}"],updatePatAccesses:["POST /orgs/{org}/personal-access-tokens"],updateWebhook:["PATCH /orgs/{org}/hooks/{hook_id}"],updateWebhookConfigForOrg:["PATCH /orgs/{org}/hooks/{hook_id}/config"]},packages:{deletePackageForAuthenticatedUser:["DELETE /user/packages/{package_type}/{package_name}"],deletePackageForOrg:["DELETE /orgs/{org}/packages/{package_type}/{package_name}"],deletePackageForUser:["DELETE /users/{username}/packages/{package_type}/{package_name}"],deletePackageVersionForAuthenticatedUser:["DELETE /user/packages/{package_type}/{package_name}/versions/{package_version_id}"],deletePackageVersionForOrg:["DELETE /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}"],deletePackageVersionForUser:["DELETE /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}"],getAllPackageVersionsForAPackageOwnedByAnOrg:["GET /orgs/{org}/packages/{package_type}/{package_name}/versions",{},{renamed:["packages","getAllPackageVersionsForPackageOwnedByOrg"]}],getAllPackageVersionsForAPackageOwnedByTheAuthenticatedUser:["GET /user/packages/{package_type}/{package_name}/versions",{},{renamed:["packages","getAllPackageVersionsForPackageOwnedByAuthenticatedUser"]}],getAllPackageVersionsForPackageOwnedByAuthenticatedUser:["GET /user/packages/{package_type}/{package_name}/versions"],getAllPackageVersionsForPackageOwnedByOrg:["GET /orgs/{org}/packages/{package_type}/{package_name}/versions"],getAllPackageVersionsForPackageOwnedByUser:["GET /users/{username}/packages/{package_type}/{package_name}/versions"],getPackageForAuthenticatedUser:["GET /user/packages/{package_type}/{package_name}"],getPackageForOrganization:["GET /orgs/{org}/packages/{package_type}/{package_name}"],getPackageForUser:["GET /users/{username}/packages/{package_type}/{package_name}"],getPackageVersionForAuthenticatedUser:["GET /user/packages/{package_type}/{package_name}/versions/{package_version_id}"],getPackageVersionForOrganization:["GET /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}"],getPackageVersionForUser:["GET /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}"],listDockerMigrationConflictingPackagesForAuthenticatedUser:["GET /user/docker/conflicts"],listDockerMigrationConflictingPackagesForOrganization:["GET /orgs/{org}/docker/conflicts"],listDockerMigrationConflictingPackagesForUser:["GET /users/{username}/docker/conflicts"],listPackagesForAuthenticatedUser:["GET /user/packages"],listPackagesForOrganization:["GET /orgs/{org}/packages"],listPackagesForUser:["GET /users/{username}/packages"],restorePackageForAuthenticatedUser:["POST /user/packages/{package_type}/{package_name}/restore{?token}"],restorePackageForOrg:["POST /orgs/{org}/packages/{package_type}/{package_name}/restore{?token}"],restorePackageForUser:["POST /users/{username}/packages/{package_type}/{package_name}/restore{?token}"],restorePackageVersionForAuthenticatedUser:["POST /user/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"],restorePackageVersionForOrg:["POST /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"],restorePackageVersionForUser:["POST /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"]},privateRegistries:{createOrgPrivateRegistry:["POST /orgs/{org}/private-registries"],deleteOrgPrivateRegistry:["DELETE /orgs/{org}/private-registries/{secret_name}"],getOrgPrivateRegistry:["GET /orgs/{org}/private-registries/{secret_name}"],getOrgPublicKey:["GET /orgs/{org}/private-registries/public-key"],listOrgPrivateRegistries:["GET /orgs/{org}/private-registries"],updateOrgPrivateRegistry:["PATCH /orgs/{org}/private-registries/{secret_name}"]},projects:{addCollaborator:["PUT /projects/{project_id}/collaborators/{username}"],createCard:["POST /projects/columns/{column_id}/cards"],createColumn:["POST /projects/{project_id}/columns"],createForAuthenticatedUser:["POST /user/projects"],createForOrg:["POST /orgs/{org}/projects"],createForRepo:["POST /repos/{owner}/{repo}/projects"],delete:["DELETE /projects/{project_id}"],deleteCard:["DELETE /projects/columns/cards/{card_id}"],deleteColumn:["DELETE /projects/columns/{column_id}"],get:["GET /projects/{project_id}"],getCard:["GET /projects/columns/cards/{card_id}"],getColumn:["GET /projects/columns/{column_id}"],getPermissionForUser:["GET /projects/{project_id}/collaborators/{username}/permission"],listCards:["GET /projects/columns/{column_id}/cards"],listCollaborators:["GET /projects/{project_id}/collaborators"],listColumns:["GET /projects/{project_id}/columns"],listForOrg:["GET /orgs/{org}/projects"],listForRepo:["GET /repos/{owner}/{repo}/projects"],listForUser:["GET /users/{username}/projects"],moveCard:["POST /projects/columns/cards/{card_id}/moves"],moveColumn:["POST /projects/columns/{column_id}/moves"],removeCollaborator:["DELETE /projects/{project_id}/collaborators/{username}"],update:["PATCH /projects/{project_id}"],updateCard:["PATCH /projects/columns/cards/{card_id}"],updateColumn:["PATCH /projects/columns/{column_id}"]},pulls:{checkIfMerged:["GET /repos/{owner}/{repo}/pulls/{pull_number}/merge"],create:["POST /repos/{owner}/{repo}/pulls"],createReplyForReviewComment:["POST /repos/{owner}/{repo}/pulls/{pull_number}/comments/{comment_id}/replies"],createReview:["POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews"],createReviewComment:["POST /repos/{owner}/{repo}/pulls/{pull_number}/comments"],deletePendingReview:["DELETE /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"],deleteReviewComment:["DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}"],dismissReview:["PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/dismissals"],get:["GET /repos/{owner}/{repo}/pulls/{pull_number}"],getReview:["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"],getReviewComment:["GET /repos/{owner}/{repo}/pulls/comments/{comment_id}"],list:["GET /repos/{owner}/{repo}/pulls"],listCommentsForReview:["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments"],listCommits:["GET /repos/{owner}/{repo}/pulls/{pull_number}/commits"],listFiles:["GET /repos/{owner}/{repo}/pulls/{pull_number}/files"],listRequestedReviewers:["GET /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"],listReviewComments:["GET /repos/{owner}/{repo}/pulls/{pull_number}/comments"],listReviewCommentsForRepo:["GET /repos/{owner}/{repo}/pulls/comments"],listReviews:["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews"],merge:["PUT /repos/{owner}/{repo}/pulls/{pull_number}/merge"],removeRequestedReviewers:["DELETE /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"],requestReviewers:["POST /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"],submitReview:["POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/events"],update:["PATCH /repos/{owner}/{repo}/pulls/{pull_number}"],updateBranch:["PUT /repos/{owner}/{repo}/pulls/{pull_number}/update-branch"],updateReview:["PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"],updateReviewComment:["PATCH /repos/{owner}/{repo}/pulls/comments/{comment_id}"]},rateLimit:{get:["GET /rate_limit"]},reactions:{createForCommitComment:["POST /repos/{owner}/{repo}/comments/{comment_id}/reactions"],createForIssue:["POST /repos/{owner}/{repo}/issues/{issue_number}/reactions"],createForIssueComment:["POST /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions"],createForPullRequestReviewComment:["POST /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions"],createForRelease:["POST /repos/{owner}/{repo}/releases/{release_id}/reactions"],createForTeamDiscussionCommentInOrg:["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions"],createForTeamDiscussionInOrg:["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions"],deleteForCommitComment:["DELETE /repos/{owner}/{repo}/comments/{comment_id}/reactions/{reaction_id}"],deleteForIssue:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/reactions/{reaction_id}"],deleteForIssueComment:["DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions/{reaction_id}"],deleteForPullRequestComment:["DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions/{reaction_id}"],deleteForRelease:["DELETE /repos/{owner}/{repo}/releases/{release_id}/reactions/{reaction_id}"],deleteForTeamDiscussion:["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions/{reaction_id}"],deleteForTeamDiscussionComment:["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions/{reaction_id}"],listForCommitComment:["GET /repos/{owner}/{repo}/comments/{comment_id}/reactions"],listForIssue:["GET /repos/{owner}/{repo}/issues/{issue_number}/reactions"],listForIssueComment:["GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions"],listForPullRequestReviewComment:["GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions"],listForRelease:["GET /repos/{owner}/{repo}/releases/{release_id}/reactions"],listForTeamDiscussionCommentInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions"],listForTeamDiscussionInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions"]},repos:{acceptInvitation:["PATCH /user/repository_invitations/{invitation_id}",{},{renamed:["repos","acceptInvitationForAuthenticatedUser"]}],acceptInvitationForAuthenticatedUser:["PATCH /user/repository_invitations/{invitation_id}"],addAppAccessRestrictions:["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps",{},{mapToData:"apps"}],addCollaborator:["PUT /repos/{owner}/{repo}/collaborators/{username}"],addStatusCheckContexts:["POST /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts",{},{mapToData:"contexts"}],addTeamAccessRestrictions:["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams",{},{mapToData:"teams"}],addUserAccessRestrictions:["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users",{},{mapToData:"users"}],cancelPagesDeployment:["POST /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}/cancel"],checkAutomatedSecurityFixes:["GET /repos/{owner}/{repo}/automated-security-fixes"],checkCollaborator:["GET /repos/{owner}/{repo}/collaborators/{username}"],checkPrivateVulnerabilityReporting:["GET /repos/{owner}/{repo}/private-vulnerability-reporting"],checkVulnerabilityAlerts:["GET /repos/{owner}/{repo}/vulnerability-alerts"],codeownersErrors:["GET /repos/{owner}/{repo}/codeowners/errors"],compareCommits:["GET /repos/{owner}/{repo}/compare/{base}...{head}"],compareCommitsWithBasehead:["GET /repos/{owner}/{repo}/compare/{basehead}"],createAttestation:["POST /repos/{owner}/{repo}/attestations"],createAutolink:["POST /repos/{owner}/{repo}/autolinks"],createCommitComment:["POST /repos/{owner}/{repo}/commits/{commit_sha}/comments"],createCommitSignatureProtection:["POST /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"],createCommitStatus:["POST /repos/{owner}/{repo}/statuses/{sha}"],createDeployKey:["POST /repos/{owner}/{repo}/keys"],createDeployment:["POST /repos/{owner}/{repo}/deployments"],createDeploymentBranchPolicy:["POST /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"],createDeploymentProtectionRule:["POST /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules"],createDeploymentStatus:["POST /repos/{owner}/{repo}/deployments/{deployment_id}/statuses"],createDispatchEvent:["POST /repos/{owner}/{repo}/dispatches"],createForAuthenticatedUser:["POST /user/repos"],createFork:["POST /repos/{owner}/{repo}/forks"],createInOrg:["POST /orgs/{org}/repos"],createOrUpdateCustomPropertiesValues:["PATCH /repos/{owner}/{repo}/properties/values"],createOrUpdateEnvironment:["PUT /repos/{owner}/{repo}/environments/{environment_name}"],createOrUpdateFileContents:["PUT /repos/{owner}/{repo}/contents/{path}"],createOrgRuleset:["POST /orgs/{org}/rulesets"],createPagesDeployment:["POST /repos/{owner}/{repo}/pages/deployments"],createPagesSite:["POST /repos/{owner}/{repo}/pages"],createRelease:["POST /repos/{owner}/{repo}/releases"],createRepoRuleset:["POST /repos/{owner}/{repo}/rulesets"],createUsingTemplate:["POST /repos/{template_owner}/{template_repo}/generate"],createWebhook:["POST /repos/{owner}/{repo}/hooks"],declineInvitation:["DELETE /user/repository_invitations/{invitation_id}",{},{renamed:["repos","declineInvitationForAuthenticatedUser"]}],declineInvitationForAuthenticatedUser:["DELETE /user/repository_invitations/{invitation_id}"],delete:["DELETE /repos/{owner}/{repo}"],deleteAccessRestrictions:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions"],deleteAdminBranchProtection:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"],deleteAnEnvironment:["DELETE /repos/{owner}/{repo}/environments/{environment_name}"],deleteAutolink:["DELETE /repos/{owner}/{repo}/autolinks/{autolink_id}"],deleteBranchProtection:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection"],deleteCommitComment:["DELETE /repos/{owner}/{repo}/comments/{comment_id}"],deleteCommitSignatureProtection:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"],deleteDeployKey:["DELETE /repos/{owner}/{repo}/keys/{key_id}"],deleteDeployment:["DELETE /repos/{owner}/{repo}/deployments/{deployment_id}"],deleteDeploymentBranchPolicy:["DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"],deleteFile:["DELETE /repos/{owner}/{repo}/contents/{path}"],deleteInvitation:["DELETE /repos/{owner}/{repo}/invitations/{invitation_id}"],deleteOrgRuleset:["DELETE /orgs/{org}/rulesets/{ruleset_id}"],deletePagesSite:["DELETE /repos/{owner}/{repo}/pages"],deletePullRequestReviewProtection:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"],deleteRelease:["DELETE /repos/{owner}/{repo}/releases/{release_id}"],deleteReleaseAsset:["DELETE /repos/{owner}/{repo}/releases/assets/{asset_id}"],deleteRepoRuleset:["DELETE /repos/{owner}/{repo}/rulesets/{ruleset_id}"],deleteWebhook:["DELETE /repos/{owner}/{repo}/hooks/{hook_id}"],disableAutomatedSecurityFixes:["DELETE /repos/{owner}/{repo}/automated-security-fixes"],disableDeploymentProtectionRule:["DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}"],disablePrivateVulnerabilityReporting:["DELETE /repos/{owner}/{repo}/private-vulnerability-reporting"],disableVulnerabilityAlerts:["DELETE /repos/{owner}/{repo}/vulnerability-alerts"],downloadArchive:["GET /repos/{owner}/{repo}/zipball/{ref}",{},{renamed:["repos","downloadZipballArchive"]}],downloadTarballArchive:["GET /repos/{owner}/{repo}/tarball/{ref}"],downloadZipballArchive:["GET /repos/{owner}/{repo}/zipball/{ref}"],enableAutomatedSecurityFixes:["PUT /repos/{owner}/{repo}/automated-security-fixes"],enablePrivateVulnerabilityReporting:["PUT /repos/{owner}/{repo}/private-vulnerability-reporting"],enableVulnerabilityAlerts:["PUT /repos/{owner}/{repo}/vulnerability-alerts"],generateReleaseNotes:["POST /repos/{owner}/{repo}/releases/generate-notes"],get:["GET /repos/{owner}/{repo}"],getAccessRestrictions:["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions"],getAdminBranchProtection:["GET /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"],getAllDeploymentProtectionRules:["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules"],getAllEnvironments:["GET /repos/{owner}/{repo}/environments"],getAllStatusCheckContexts:["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts"],getAllTopics:["GET /repos/{owner}/{repo}/topics"],getAppsWithAccessToProtectedBranch:["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps"],getAutolink:["GET /repos/{owner}/{repo}/autolinks/{autolink_id}"],getBranch:["GET /repos/{owner}/{repo}/branches/{branch}"],getBranchProtection:["GET /repos/{owner}/{repo}/branches/{branch}/protection"],getBranchRules:["GET /repos/{owner}/{repo}/rules/branches/{branch}"],getClones:["GET /repos/{owner}/{repo}/traffic/clones"],getCodeFrequencyStats:["GET /repos/{owner}/{repo}/stats/code_frequency"],getCollaboratorPermissionLevel:["GET /repos/{owner}/{repo}/collaborators/{username}/permission"],getCombinedStatusForRef:["GET /repos/{owner}/{repo}/commits/{ref}/status"],getCommit:["GET /repos/{owner}/{repo}/commits/{ref}"],getCommitActivityStats:["GET /repos/{owner}/{repo}/stats/commit_activity"],getCommitComment:["GET /repos/{owner}/{repo}/comments/{comment_id}"],getCommitSignatureProtection:["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"],getCommunityProfileMetrics:["GET /repos/{owner}/{repo}/community/profile"],getContent:["GET /repos/{owner}/{repo}/contents/{path}"],getContributorsStats:["GET /repos/{owner}/{repo}/stats/contributors"],getCustomDeploymentProtectionRule:["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}"],getCustomPropertiesValues:["GET /repos/{owner}/{repo}/properties/values"],getDeployKey:["GET /repos/{owner}/{repo}/keys/{key_id}"],getDeployment:["GET /repos/{owner}/{repo}/deployments/{deployment_id}"],getDeploymentBranchPolicy:["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"],getDeploymentStatus:["GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses/{status_id}"],getEnvironment:["GET /repos/{owner}/{repo}/environments/{environment_name}"],getLatestPagesBuild:["GET /repos/{owner}/{repo}/pages/builds/latest"],getLatestRelease:["GET /repos/{owner}/{repo}/releases/latest"],getOrgRuleSuite:["GET /orgs/{org}/rulesets/rule-suites/{rule_suite_id}"],getOrgRuleSuites:["GET /orgs/{org}/rulesets/rule-suites"],getOrgRuleset:["GET /orgs/{org}/rulesets/{ruleset_id}"],getOrgRulesets:["GET /orgs/{org}/rulesets"],getPages:["GET /repos/{owner}/{repo}/pages"],getPagesBuild:["GET /repos/{owner}/{repo}/pages/builds/{build_id}"],getPagesDeployment:["GET /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}"],getPagesHealthCheck:["GET /repos/{owner}/{repo}/pages/health"],getParticipationStats:["GET /repos/{owner}/{repo}/stats/participation"],getPullRequestReviewProtection:["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"],getPunchCardStats:["GET /repos/{owner}/{repo}/stats/punch_card"],getReadme:["GET /repos/{owner}/{repo}/readme"],getReadmeInDirectory:["GET /repos/{owner}/{repo}/readme/{dir}"],getRelease:["GET /repos/{owner}/{repo}/releases/{release_id}"],getReleaseAsset:["GET /repos/{owner}/{repo}/releases/assets/{asset_id}"],getReleaseByTag:["GET /repos/{owner}/{repo}/releases/tags/{tag}"],getRepoRuleSuite:["GET /repos/{owner}/{repo}/rulesets/rule-suites/{rule_suite_id}"],getRepoRuleSuites:["GET /repos/{owner}/{repo}/rulesets/rule-suites"],getRepoRuleset:["GET /repos/{owner}/{repo}/rulesets/{ruleset_id}"],getRepoRulesets:["GET /repos/{owner}/{repo}/rulesets"],getStatusChecksProtection:["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"],getTeamsWithAccessToProtectedBranch:["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams"],getTopPaths:["GET /repos/{owner}/{repo}/traffic/popular/paths"],getTopReferrers:["GET /repos/{owner}/{repo}/traffic/popular/referrers"],getUsersWithAccessToProtectedBranch:["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users"],getViews:["GET /repos/{owner}/{repo}/traffic/views"],getWebhook:["GET /repos/{owner}/{repo}/hooks/{hook_id}"],getWebhookConfigForRepo:["GET /repos/{owner}/{repo}/hooks/{hook_id}/config"],getWebhookDelivery:["GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}"],listActivities:["GET /repos/{owner}/{repo}/activity"],listAttestations:["GET /repos/{owner}/{repo}/attestations/{subject_digest}"],listAutolinks:["GET /repos/{owner}/{repo}/autolinks"],listBranches:["GET /repos/{owner}/{repo}/branches"],listBranchesForHeadCommit:["GET /repos/{owner}/{repo}/commits/{commit_sha}/branches-where-head"],listCollaborators:["GET /repos/{owner}/{repo}/collaborators"],listCommentsForCommit:["GET /repos/{owner}/{repo}/commits/{commit_sha}/comments"],listCommitCommentsForRepo:["GET /repos/{owner}/{repo}/comments"],listCommitStatusesForRef:["GET /repos/{owner}/{repo}/commits/{ref}/statuses"],listCommits:["GET /repos/{owner}/{repo}/commits"],listContributors:["GET /repos/{owner}/{repo}/contributors"],listCustomDeploymentRuleIntegrations:["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps"],listDeployKeys:["GET /repos/{owner}/{repo}/keys"],listDeploymentBranchPolicies:["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"],listDeploymentStatuses:["GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses"],listDeployments:["GET /repos/{owner}/{repo}/deployments"],listForAuthenticatedUser:["GET /user/repos"],listForOrg:["GET /orgs/{org}/repos"],listForUser:["GET /users/{username}/repos"],listForks:["GET /repos/{owner}/{repo}/forks"],listInvitations:["GET /repos/{owner}/{repo}/invitations"],listInvitationsForAuthenticatedUser:["GET /user/repository_invitations"],listLanguages:["GET /repos/{owner}/{repo}/languages"],listPagesBuilds:["GET /repos/{owner}/{repo}/pages/builds"],listPublic:["GET /repositories"],listPullRequestsAssociatedWithCommit:["GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls"],listReleaseAssets:["GET /repos/{owner}/{repo}/releases/{release_id}/assets"],listReleases:["GET /repos/{owner}/{repo}/releases"],listTags:["GET /repos/{owner}/{repo}/tags"],listTeams:["GET /repos/{owner}/{repo}/teams"],listWebhookDeliveries:["GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries"],listWebhooks:["GET /repos/{owner}/{repo}/hooks"],merge:["POST /repos/{owner}/{repo}/merges"],mergeUpstream:["POST /repos/{owner}/{repo}/merge-upstream"],pingWebhook:["POST /repos/{owner}/{repo}/hooks/{hook_id}/pings"],redeliverWebhookDelivery:["POST /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}/attempts"],removeAppAccessRestrictions:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps",{},{mapToData:"apps"}],removeCollaborator:["DELETE /repos/{owner}/{repo}/collaborators/{username}"],removeStatusCheckContexts:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts",{},{mapToData:"contexts"}],removeStatusCheckProtection:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"],removeTeamAccessRestrictions:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams",{},{mapToData:"teams"}],removeUserAccessRestrictions:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users",{},{mapToData:"users"}],renameBranch:["POST /repos/{owner}/{repo}/branches/{branch}/rename"],replaceAllTopics:["PUT /repos/{owner}/{repo}/topics"],requestPagesBuild:["POST /repos/{owner}/{repo}/pages/builds"],setAdminBranchProtection:["POST /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"],setAppAccessRestrictions:["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps",{},{mapToData:"apps"}],setStatusCheckContexts:["PUT /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts",{},{mapToData:"contexts"}],setTeamAccessRestrictions:["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams",{},{mapToData:"teams"}],setUserAccessRestrictions:["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users",{},{mapToData:"users"}],testPushWebhook:["POST /repos/{owner}/{repo}/hooks/{hook_id}/tests"],transfer:["POST /repos/{owner}/{repo}/transfer"],update:["PATCH /repos/{owner}/{repo}"],updateBranchProtection:["PUT /repos/{owner}/{repo}/branches/{branch}/protection"],updateCommitComment:["PATCH /repos/{owner}/{repo}/comments/{comment_id}"],updateDeploymentBranchPolicy:["PUT /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"],updateInformationAboutPagesSite:["PUT /repos/{owner}/{repo}/pages"],updateInvitation:["PATCH /repos/{owner}/{repo}/invitations/{invitation_id}"],updateOrgRuleset:["PUT /orgs/{org}/rulesets/{ruleset_id}"],updatePullRequestReviewProtection:["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"],updateRelease:["PATCH /repos/{owner}/{repo}/releases/{release_id}"],updateReleaseAsset:["PATCH /repos/{owner}/{repo}/releases/assets/{asset_id}"],updateRepoRuleset:["PUT /repos/{owner}/{repo}/rulesets/{ruleset_id}"],updateStatusCheckPotection:["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks",{},{renamed:["repos","updateStatusCheckProtection"]}],updateStatusCheckProtection:["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"],updateWebhook:["PATCH /repos/{owner}/{repo}/hooks/{hook_id}"],updateWebhookConfigForRepo:["PATCH /repos/{owner}/{repo}/hooks/{hook_id}/config"],uploadReleaseAsset:["POST /repos/{owner}/{repo}/releases/{release_id}/assets{?name,label}",{baseUrl:"https://uploads.github.com"}]},search:{code:["GET /search/code"],commits:["GET /search/commits"],issuesAndPullRequests:["GET /search/issues"],labels:["GET /search/labels"],repos:["GET /search/repositories"],topics:["GET /search/topics"],users:["GET /search/users"]},secretScanning:{createPushProtectionBypass:["POST /repos/{owner}/{repo}/secret-scanning/push-protection-bypasses"],getAlert:["GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}"],getScanHistory:["GET /repos/{owner}/{repo}/secret-scanning/scan-history"],listAlertsForEnterprise:["GET /enterprises/{enterprise}/secret-scanning/alerts"],listAlertsForOrg:["GET /orgs/{org}/secret-scanning/alerts"],listAlertsForRepo:["GET /repos/{owner}/{repo}/secret-scanning/alerts"],listLocationsForAlert:["GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations"],updateAlert:["PATCH /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}"]},securityAdvisories:{createFork:["POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/forks"],createPrivateVulnerabilityReport:["POST /repos/{owner}/{repo}/security-advisories/reports"],createRepositoryAdvisory:["POST /repos/{owner}/{repo}/security-advisories"],createRepositoryAdvisoryCveRequest:["POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/cve"],getGlobalAdvisory:["GET /advisories/{ghsa_id}"],getRepositoryAdvisory:["GET /repos/{owner}/{repo}/security-advisories/{ghsa_id}"],listGlobalAdvisories:["GET /advisories"],listOrgRepositoryAdvisories:["GET /orgs/{org}/security-advisories"],listRepositoryAdvisories:["GET /repos/{owner}/{repo}/security-advisories"],updateRepositoryAdvisory:["PATCH /repos/{owner}/{repo}/security-advisories/{ghsa_id}"]},teams:{addOrUpdateMembershipForUserInOrg:["PUT /orgs/{org}/teams/{team_slug}/memberships/{username}"],addOrUpdateProjectPermissionsInOrg:["PUT /orgs/{org}/teams/{team_slug}/projects/{project_id}"],addOrUpdateRepoPermissionsInOrg:["PUT /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"],checkPermissionsForProjectInOrg:["GET /orgs/{org}/teams/{team_slug}/projects/{project_id}"],checkPermissionsForRepoInOrg:["GET /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"],create:["POST /orgs/{org}/teams"],createDiscussionCommentInOrg:["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments"],createDiscussionInOrg:["POST /orgs/{org}/teams/{team_slug}/discussions"],deleteDiscussionCommentInOrg:["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"],deleteDiscussionInOrg:["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"],deleteInOrg:["DELETE /orgs/{org}/teams/{team_slug}"],getByName:["GET /orgs/{org}/teams/{team_slug}"],getDiscussionCommentInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"],getDiscussionInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"],getMembershipForUserInOrg:["GET /orgs/{org}/teams/{team_slug}/memberships/{username}"],list:["GET /orgs/{org}/teams"],listChildInOrg:["GET /orgs/{org}/teams/{team_slug}/teams"],listDiscussionCommentsInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments"],listDiscussionsInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions"],listForAuthenticatedUser:["GET /user/teams"],listMembersInOrg:["GET /orgs/{org}/teams/{team_slug}/members"],listPendingInvitationsInOrg:["GET /orgs/{org}/teams/{team_slug}/invitations"],listProjectsInOrg:["GET /orgs/{org}/teams/{team_slug}/projects"],listReposInOrg:["GET /orgs/{org}/teams/{team_slug}/repos"],removeMembershipForUserInOrg:["DELETE /orgs/{org}/teams/{team_slug}/memberships/{username}"],removeProjectInOrg:["DELETE /orgs/{org}/teams/{team_slug}/projects/{project_id}"],removeRepoInOrg:["DELETE /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"],updateDiscussionCommentInOrg:["PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"],updateDiscussionInOrg:["PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"],updateInOrg:["PATCH /orgs/{org}/teams/{team_slug}"]},users:{addEmailForAuthenticated:["POST /user/emails",{},{renamed:["users","addEmailForAuthenticatedUser"]}],addEmailForAuthenticatedUser:["POST /user/emails"],addSocialAccountForAuthenticatedUser:["POST /user/social_accounts"],block:["PUT /user/blocks/{username}"],checkBlocked:["GET /user/blocks/{username}"],checkFollowingForUser:["GET /users/{username}/following/{target_user}"],checkPersonIsFollowedByAuthenticated:["GET /user/following/{username}"],createGpgKeyForAuthenticated:["POST /user/gpg_keys",{},{renamed:["users","createGpgKeyForAuthenticatedUser"]}],createGpgKeyForAuthenticatedUser:["POST /user/gpg_keys"],createPublicSshKeyForAuthenticated:["POST /user/keys",{},{renamed:["users","createPublicSshKeyForAuthenticatedUser"]}],createPublicSshKeyForAuthenticatedUser:["POST /user/keys"],createSshSigningKeyForAuthenticatedUser:["POST /user/ssh_signing_keys"],deleteEmailForAuthenticated:["DELETE /user/emails",{},{renamed:["users","deleteEmailForAuthenticatedUser"]}],deleteEmailForAuthenticatedUser:["DELETE /user/emails"],deleteGpgKeyForAuthenticated:["DELETE /user/gpg_keys/{gpg_key_id}",{},{renamed:["users","deleteGpgKeyForAuthenticatedUser"]}],deleteGpgKeyForAuthenticatedUser:["DELETE /user/gpg_keys/{gpg_key_id}"],deletePublicSshKeyForAuthenticated:["DELETE /user/keys/{key_id}",{},{renamed:["users","deletePublicSshKeyForAuthenticatedUser"]}],deletePublicSshKeyForAuthenticatedUser:["DELETE /user/keys/{key_id}"],deleteSocialAccountForAuthenticatedUser:["DELETE /user/social_accounts"],deleteSshSigningKeyForAuthenticatedUser:["DELETE /user/ssh_signing_keys/{ssh_signing_key_id}"],follow:["PUT /user/following/{username}"],getAuthenticated:["GET /user"],getById:["GET /user/{account_id}"],getByUsername:["GET /users/{username}"],getContextForUser:["GET /users/{username}/hovercard"],getGpgKeyForAuthenticated:["GET /user/gpg_keys/{gpg_key_id}",{},{renamed:["users","getGpgKeyForAuthenticatedUser"]}],getGpgKeyForAuthenticatedUser:["GET /user/gpg_keys/{gpg_key_id}"],getPublicSshKeyForAuthenticated:["GET /user/keys/{key_id}",{},{renamed:["users","getPublicSshKeyForAuthenticatedUser"]}],getPublicSshKeyForAuthenticatedUser:["GET /user/keys/{key_id}"],getSshSigningKeyForAuthenticatedUser:["GET /user/ssh_signing_keys/{ssh_signing_key_id}"],list:["GET /users"],listAttestations:["GET /users/{username}/attestations/{subject_digest}"],listBlockedByAuthenticated:["GET /user/blocks",{},{renamed:["users","listBlockedByAuthenticatedUser"]}],listBlockedByAuthenticatedUser:["GET /user/blocks"],listEmailsForAuthenticated:["GET /user/emails",{},{renamed:["users","listEmailsForAuthenticatedUser"]}],listEmailsForAuthenticatedUser:["GET /user/emails"],listFollowedByAuthenticated:["GET /user/following",{},{renamed:["users","listFollowedByAuthenticatedUser"]}],listFollowedByAuthenticatedUser:["GET /user/following"],listFollowersForAuthenticatedUser:["GET /user/followers"],listFollowersForUser:["GET /users/{username}/followers"],listFollowingForUser:["GET /users/{username}/following"],listGpgKeysForAuthenticated:["GET /user/gpg_keys",{},{renamed:["users","listGpgKeysForAuthenticatedUser"]}],listGpgKeysForAuthenticatedUser:["GET /user/gpg_keys"],listGpgKeysForUser:["GET /users/{username}/gpg_keys"],listPublicEmailsForAuthenticated:["GET /user/public_emails",{},{renamed:["users","listPublicEmailsForAuthenticatedUser"]}],listPublicEmailsForAuthenticatedUser:["GET /user/public_emails"],listPublicKeysForUser:["GET /users/{username}/keys"],listPublicSshKeysForAuthenticated:["GET /user/keys",{},{renamed:["users","listPublicSshKeysForAuthenticatedUser"]}],listPublicSshKeysForAuthenticatedUser:["GET /user/keys"],listSocialAccountsForAuthenticatedUser:["GET /user/social_accounts"],listSocialAccountsForUser:["GET /users/{username}/social_accounts"],listSshSigningKeysForAuthenticatedUser:["GET /user/ssh_signing_keys"],listSshSigningKeysForUser:["GET /users/{username}/ssh_signing_keys"],setPrimaryEmailVisibilityForAuthenticated:["PATCH /user/email/visibility",{},{renamed:["users","setPrimaryEmailVisibilityForAuthenticatedUser"]}],setPrimaryEmailVisibilityForAuthenticatedUser:["PATCH /user/email/visibility"],unblock:["DELETE /user/blocks/{username}"],unfollow:["DELETE /user/following/{username}"],updateAuthenticated:["PATCH /user"]}}))for(const[r,n]of Object.entries(t)){const[t,o,i]=n,[s,a]=t.split(/ /),c=Object.assign({method:s,url:a},o);R.has(e)||R.set(e,new Map),R.get(e).set(r,{scope:e,methodName:r,endpointDefaults:c,decorations:i})}const A={has:({scope:e},t)=>R.get(e).has(t),getOwnPropertyDescriptor(e,t){return{value:this.get(e,t),configurable:!0,writable:!0,enumerable:!0}},defineProperty:(e,t,r)=>(Object.defineProperty(e.cache,t,r),!0),deleteProperty:(e,t)=>(delete e.cache[t],!0),ownKeys:({scope:e})=>[...R.get(e).keys()],set:(e,t,r)=>e.cache[t]=r,get({octokit:e,scope:t,cache:r},n){if(r[n])return r[n];const o=R.get(t).get(n);if(!o)return;const{endpointDefaults:i,decorations:s}=o;return r[n]=s?function(e,t,r,n,o){const i=e.request.defaults(n);return Object.assign(function(...n){let s=i.endpoint.merge(...n);if(o.mapToData)return s=Object.assign({},s,{data:s[o.mapToData],[o.mapToData]:void 0}),i(s);if(o.renamed){const[n,i]=o.renamed;e.log.warn(`octokit.${t}.${r}() has been renamed to octokit.${n}.${i}()`)}if(o.deprecated&&e.log.warn(o.deprecated),o.renamedParameters){const s=i.endpoint.merge(...n);for(const[n,i]of Object.entries(o.renamedParameters))n in s&&(e.log.warn(`"${n}" parameter is deprecated for "octokit.${t}.${r}()". Use "${i}" instead`),i in s||(s[i]=s[n]),delete s[n]);return i(s)}return i(...n)},i)}(e,t,n,i,s):e.request.defaults(i),r[n]}};function I(e){const t=function(e){const t={};for(const r of R.keys())t[r]=new Proxy({octokit:e,scope:r,cache:{}},A);return t}(e);return{...t,rest:t}}I.VERSION="13.3.1";const D=class{static VERSION=b;static defaults(e){return class extends(this){constructor(...t){const r=t[0]||{};super("function"!=typeof e?Object.assign({},e,r,r.userAgent&&e.userAgent?{userAgent:`${r.userAgent} ${e.userAgent}`}:null):e(r))}}}static plugins=[];static plugin(...e){const t=this.plugins;return class extends(this){static plugins=t.concat(e.filter(e=>!t.includes(e)))}}constructor(e={}){const t=new l,r={baseUrl:p.E.endpoint.DEFAULTS.baseUrl,headers:{},request:Object.assign({},e.request,{hook:t.bind(null,"request")}),mediaType:{previews:[],format:""}};if(r.headers["user-agent"]=e.userAgent?`${e.userAgent} ${_}`:_,e.baseUrl&&(r.baseUrl=e.baseUrl),e.previews&&(r.mediaType.previews=e.previews),e.timeZone&&(r.headers["time-zone"]=e.timeZone),this.request=p.E.defaults(r),this.graphql=(0,d.withCustomRequest)(this.request).defaults(r),this.log=Object.assign({debug:E,info:E,warn:T,error:w},e.log),this.hook=t,e.authStrategy){const{authStrategy:r,...n}=e,o=r(Object.assign({request:this.request,log:this.log,octokit:this,octokitOptions:n},e.auth));t.wrap("request",o.hook),this.auth=o}else if(e.auth){const r=y(e.auth);t.wrap("request",r.hook),this.auth=r}else this.auth=async()=>({type:"unauthenticated"});const n=this.constructor;for(let t=0;t<n.plugins.length;++t)Object.assign(this,n.plugins[t](this,e))}request;graphql;log;hook;auth}.plugin(P,I,C).defaults({userAgent:"octokit-rest.js/21.1.0"})},6492:(e,t,r)=>{"use strict";r.d(t,{Bw:()=>u,Ev:()=>b,Fk:()=>T,HP:()=>n,Hr:()=>c,LZ:()=>i,QW:()=>w,Vj:()=>y,Vo:()=>d,Yd:()=>a,Yp:()=>g,dI:()=>m,eT:()=>s,fc:()=>f,jy:()=>l,kI:()=>E,l0:()=>h,m5:()=>o,qT:()=>p,s4:()=>v,xW:()=>_});var n=void 0,o="",i="channels",s="core",a="createPerfMgr",c="disabled",u="extensionConfig",l="extensions",p="processTelemetry",d="priority",f="eventsSent",g="eventsDiscarded",m="eventsSendRequest",h="perfEvent",v="offlineEventsStored",y="offlineBatchSent",b="offlineBatchDrop",E="getPerfMgr",T="domain",w="path",_="Not dynamic - "},6535:(e,t,r)=>{"use strict";r.d(t,{Si:()=>v,VN:()=>h,Z1:()=>m});var n=r(269),o=r(6182),i=r(7292),s=r(6492),a=4294967296,c=4294967295,u=123456789,l=987654321,p=!1,d=u,f=l;function g(){try{var e=2147483647&(0,n.f0d)();(t=(Math.random()*a^e)+e)<0&&(t>>>=0),d=u+t&c,f=l-t&c,p=!0}catch(e){}var t}function m(e){return e>0?Math.floor(h()/c*(e+1))>>>0:0}function h(e){var t=0,r=(0,i.MY)()||(0,i.iN)();return r&&r.getRandomValues&&(t=r.getRandomValues(new Uint32Array(1))[0]&c),0===t&&(0,i.lT)()&&(p||g(),t=function(){var e=((f=36969*(65535&f)+(f>>16)&c)<<16)+(65535&(d=18e3*(65535&d)+(d>>16)&c))>>>0&c;return e>>>=0}()&c),0===t&&(t=Math.floor(a*Math.random()|0)),e||(t>>>=0),t}function v(e){void 0===e&&(e=22);for(var t=h()>>>0,r=0,n=s.m5;n[o.oI]<e;)r++,n+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(63&t),t>>>=6,5===r&&(t=(h()<<2&4294967295|3&t)>>>0,r=0);return n}},6548:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BaseTelemetryReporter=void 0,t.BaseTelemetryReporter=class{constructor(e,t,r){this.telemetrySender=e,this.vscodeAPI=t,this.userOptIn=!1,this.errorOptIn=!1,this.disposables=[],this._onDidChangeTelemetryLevel=new this.vscodeAPI.EventEmitter,this.onDidChangeTelemetryLevel=this._onDidChangeTelemetryLevel.event,this.telemetryLogger=this.vscodeAPI.env.createTelemetryLogger(this.telemetrySender,r),this.updateUserOptIn(),this.telemetryLogger.onDidChangeEnableStates(()=>{this.updateUserOptIn()})}updateUserOptIn(){this.errorOptIn=this.telemetryLogger.isErrorsEnabled,this.userOptIn=this.telemetryLogger.isUsageEnabled,(this.telemetryLogger.isErrorsEnabled||this.telemetryLogger.isUsageEnabled)&&this.telemetrySender.instantiateSender(),this._onDidChangeTelemetryLevel.fire(this.telemetryLevel)}get telemetryLevel(){return this.errorOptIn&&this.userOptIn?"all":this.errorOptIn?"error":"off"}internalSendTelemetryEvent(e,t,r,n){n?this.telemetrySender.sendEventData(e,{properties:t,measurements:r}):this.telemetryLogger.logUsage(e,{properties:t,measurements:r})}sendTelemetryEvent(e,t,r){this.internalSendTelemetryEvent(e,t,r,!1)}sendRawTelemetryEvent(e,t,r){const n={...t};for(const e of Object.keys(n??{})){const t=n[e];"string"==typeof e&&void 0!==t&&(n[e]=new this.vscodeAPI.TelemetryTrustedValue("string"==typeof t?t:t.value))}this.sendTelemetryEvent(e,n,r)}sendDangerousTelemetryEvent(e,t,r){this.telemetrySender.instantiateSender(),this.internalSendTelemetryEvent(e,t,r,!0)}internalSendTelemetryErrorEvent(e,t,r,n){n?this.telemetrySender.sendEventData(e,{properties:t,measurements:r}):this.telemetryLogger.logError(e,{properties:t,measurements:r})}sendTelemetryErrorEvent(e,t,r){this.internalSendTelemetryErrorEvent(e,t,r,!1)}sendDangerousTelemetryErrorEvent(e,t,r){this.telemetrySender.instantiateSender(),this.internalSendTelemetryErrorEvent(e,t,r,!0)}async dispose(){return await this.telemetrySender.dispose(),this.telemetryLogger.dispose(),Promise.all(this.disposables.map(e=>e.dispose()))}}},6555:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GraphqlResponseError:()=>i,graphql:()=>l,withCustomRequest:()=>p});var n=r(3698),o=r(5407),i=class extends Error{constructor(e,t,r){super("Request failed due to following response errors:\n"+r.errors.map(e=>` - ${e.message}`).join("\n")),this.request=e,this.headers=t,this.response=r,this.errors=r.errors,this.data=r.data,Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}name="GraphqlResponseError";errors;data},s=["method","baseUrl","url","headers","request","query","mediaType","operationName"],a=["query","method","url"],c=/\/api\/v3\/?$/;function u(e,t){const r=e.defaults(t);return Object.assign((e,t)=>function(e,t,r){if(r){if("string"==typeof t&&"query"in r)return Promise.reject(new Error('[@octokit/graphql] "query" cannot be used as variable name'));for(const e in r)if(a.includes(e))return Promise.reject(new Error(`[@octokit/graphql] "${e}" cannot be used as variable name`))}const n="string"==typeof t?Object.assign({query:t},r):t,o=Object.keys(n).reduce((e,t)=>s.includes(t)?(e[t]=n[t],e):(e.variables||(e.variables={}),e.variables[t]=n[t],e),{}),u=n.baseUrl||e.endpoint.DEFAULTS.baseUrl;return c.test(u)&&(o.url=u.replace(c,"/api/graphql")),e(o).then(e=>{if(e.data.errors){const t={};for(const r of Object.keys(e.headers))t[r]=e.headers[r];throw new i(o,t,e.data)}return e.data.data})}(r,e,t),{defaults:u.bind(null,r),endpoint:r.endpoint})}var l=u(n.E,{headers:{"user-agent":`octokit-graphql.js/0.0.0-development ${(0,o.$)()}`},method:"POST",url:"/graphql"});function p(e){return u(e,{method:"POST",url:"/graphql"})}},6773:e=>{"use strict";const t=function(){};t.prototype=Object.create(null);const r=/; *([!#$%&'*+.^\w`|~-]+)=("(?:[\v\u0020\u0021\u0023-\u005b\u005d-\u007e\u0080-\u00ff]|\\[\v\u0020-\u00ff])*"|[!#$%&'*+.^\w`|~-]+) */gu,n=/\\([\v\u0020-\u00ff])/gu,o=/^[!#$%&'*+.^\w|~-]+\/[!#$%&'*+.^\w|~-]+$/u,i={type:"",parameters:new t};Object.freeze(i.parameters),Object.freeze(i),e.exports.xL=function(e){if("string"!=typeof e)return i;let s=e.indexOf(";");const a=-1!==s?e.slice(0,s).trim():e.trim();if(!1===o.test(a))return i;const c={type:a.toLowerCase(),parameters:new t};if(-1===s)return c;let u,l,p;for(r.lastIndex=s;l=r.exec(e);){if(l.index!==s)return i;s+=l[0].length,u=l[1].toLowerCase(),p=l[2],'"'===p[0]&&(p=p.slice(1,p.length-1),n.test(p)&&(p=p.replace(n,"$1"))),c.parameters[u]=p}return s!==e.length?i:c}},6805:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GithubCredentialProviderManager=void 0;const n=r(1398),o=r(7171),i={dispose(){}};class s{async getCredentials(e){if(!/github\.com/i.test(e.authority))return;const t=await(0,o.getSession)();return{username:t.account.id,password:t.accessToken}}}t.GithubCredentialProviderManager=class{set enabled(e){this._enabled!==e&&(this._enabled=e,e?this.providerDisposable=this.gitAPI.registerCredentialsProvider(new s):this.providerDisposable.dispose())}constructor(e){this.gitAPI=e,this.providerDisposable=i,this._enabled=!1,this.disposable=n.workspace.onDidChangeConfiguration(e=>{e.affectsConfiguration("github")&&this.refresh()}),this.refresh()}refresh(){const e=n.workspace.getConfiguration("github",null).get("gitAuthentication",!0);this.enabled=!!e}dispose(){this.enabled=!1,this.disposable.dispose()}}},6928:e=>{"use strict";e.exports=require("path")},7016:e=>{"use strict";e.exports=require("url")},7171:function(e,t,r){"use strict";var n,o=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(n=function(e){return n=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t},n(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=n(e),s=0;s<r.length;s++)"default"!==r[s]&&o(t,e,r[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.AuthenticationError=void 0,t.getSession=g,t.getOctokit=function(){return m||(m=g().then(async e=>{const t=e.accessToken,n=d(),{Octokit:o}=await Promise.resolve().then(()=>s(r(6401)));return new o({request:{agent:n},userAgent:"GitHub VSCode",auth:`token ${t}`})}).then(null,async e=>{throw m=void 0,e})),m},t.getOctokitGraphql=async function(){if(!h)try{const e=await a.authentication.getSession("github",f,{silent:!0});if(!e)throw new p("No GitHub authentication session available.");const t=e.accessToken,{graphql:n}=await Promise.resolve().then(()=>s(r(6555)));return n.defaults({headers:{authorization:`token ${t}`},request:{agent:d()}})}catch(e){throw h=void 0,e}return h};const a=r(1398),c=r(5692),u=r(803),l=r(7016);class p extends Error{}function d(e=process.env.HTTPS_PROXY){if(!e)return c.globalAgent;try{const{hostname:t,port:r,username:n,password:o}=new l.URL(e),i=n&&o&&`${n}:${o}`;return(0,u.httpsOverHttp)({proxy:{host:t,port:r,proxyAuth:i}})}catch(e){return a.window.showErrorMessage(`HTTPS_PROXY environment variable ignored: ${e.message}`),c.globalAgent}}t.AuthenticationError=p;const f=["repo","workflow","user:email","read:user"];async function g(){return await a.authentication.getSession("github",f,{createIfNone:!0})}let m,h},7292:(e,t,r)=>{"use strict";r.d(t,{$Z:()=>j,Iu:()=>M,L0:()=>G,MY:()=>R,PV:()=>L,R7:()=>F,U5:()=>k,Uf:()=>U,Z:()=>O,cU:()=>P,g$:()=>S,hm:()=>C,iN:()=>A,lT:()=>D,lV:()=>I,xk:()=>x});var n=r(5664),o=r(269),i=r(6182),s=r(3673),a=r(6492),c="documentMode",u="location",l="console",p="JSON",d="crypto",f="msCrypto",g="ReactNative",m="msie",h="trident/",v="XMLHttpRequest",y=null,b=null,E=!1,T=null,w=null;function _(e,t){var r=!1;if(e){try{if(!(r=t in e)){var i=e[n.vR];i&&(r=t in i)}}catch(e){}if(!r)try{var s=new e;r=!(0,o.b07)(s[t])}catch(e){}}return r}function P(e){E=e}function S(e){if(e&&E){var t=(0,o.zS2)("__mockLocation");if(t)return t}return typeof location===n._1&&location?location:(0,o.zS2)(u)}function k(){return typeof console!==n.bA?console:(0,o.zS2)(l)}function O(){return Boolean(typeof JSON===n._1&&JSON||null!==(0,o.zS2)(p))}function C(){return O()?JSON||(0,o.zS2)(p):null}function R(){return(0,o.zS2)(d)}function A(){return(0,o.zS2)(f)}function I(){var e=(0,o.w3n)();return!(!e||!e.product)&&e.product===g}function D(){var e=(0,o.w3n)();if(e&&(e[i.tX]!==b||null===y)){var t=((b=e[i.tX])||a.m5)[i.OL]();y=(0,s.Ju)(t,m)||(0,s.Ju)(t,h)}return y}function G(e){if(void 0===e&&(e=null),!e){var t=(0,o.w3n)()||{};e=t?(t.userAgent||a.m5)[i.OL]():a.m5}var r=(e||a.m5)[i.OL]();if((0,s.Ju)(r,m)){var n=(0,o.YEm)()||{};return Math.max(parseInt(r[i.sY](m)[1]),n[c]||0)}if((0,s.Ju)(r,h)){var u=parseInt(r[i.sY](h)[1]);if(u)return u+4}return null}function U(e){return null!==w&&!1!==e||(w=(0,o.w9M)()&&Boolean((0,o.w3n)().sendBeacon)),w}function F(e){var t=!1;try{t=!!(0,o.zS2)("fetch");var r=(0,o.zS2)("Request");t&&e&&r&&(t=_(r,"keepalive"))}catch(e){}return t}function L(){return null===T&&(T=typeof XDomainRequest!==n.bA)&&x()&&(T=T&&!_((0,o.zS2)(v),"withCredentials")),T}function x(){var e=!1;try{e=!!(0,o.zS2)(v)}catch(e){}return e}function H(e,t){if(e)for(var r=0;r<e[i.oI];r++){var n=e[r];if(n[i.RS]&&n[i.RS]===t)return n}return{}}function j(e){var t=(0,o.YEm)();return t&&e?H(t.querySelectorAll("meta"),e).content:null}function M(e){var t,r=(0,o.FJj)();if(r){var n=r.getEntriesByType("navigation")||[];t=H((n[i.oI]>0?n[0]:{}).serverTiming,e).description}return t}},7358:(e,t,r)=>{"use strict";r.d(t,{B:()=>n});var n=function(e,t){this.aiDataContract={baseType:1,baseData:1},this.baseType=e,this.baseData=t}},7374:(e,t,r)=>{"use strict";r.d(t,{eL:()=>o,iD:()=>s,uG:()=>i});var n=r(4282),o=(0,n.H)({LocalStorage:0,SessionStorage:1}),i=(0,n.H)({AI:0,AI_AND_W3C:1,W3C:2}),s=(0,n.H)({Normal:1,Critical:2})},7469:(e,t,r)=>{"use strict";r(9278);var n,o=r(4756),i=r(8611),s=r(5692),a=r(4434),c=(r(2613),r(9023));function u(e){var t=this;t.options=e||{},t.proxyOptions=t.options.proxy||{},t.maxSockets=t.options.maxSockets||i.Agent.defaultMaxSockets,t.requests=[],t.sockets=[],t.on("free",function(e,r,n,o){for(var i=p(r,n,o),s=0,a=t.requests.length;s<a;++s){var c=t.requests[s];if(c.host===i.host&&c.port===i.port)return t.requests.splice(s,1),void c.request.onSocket(e)}e.destroy(),t.removeSocket(e)})}function l(e,t){var r=this;u.prototype.createSocket.call(r,e,function(n){var i=e.request.getHeader("host"),s=d({},r.options,{socket:n,servername:i?i.replace(/:.*$/,""):e.host}),a=o.connect(0,s);r.sockets[r.sockets.indexOf(n)]=a,t(a)})}function p(e,t,r){return"string"==typeof e?{host:e,port:t,localAddress:r}:e}function d(e){for(var t=1,r=arguments.length;t<r;++t){var n=arguments[t];if("object"==typeof n)for(var o=Object.keys(n),i=0,s=o.length;i<s;++i){var a=o[i];void 0!==n[a]&&(e[a]=n[a])}}return e}t.httpOverHttp=function(e){var t=new u(e);return t.request=i.request,t},t.httpsOverHttp=function(e){var t=new u(e);return t.request=i.request,t.createSocket=l,t.defaultPort=443,t},t.httpOverHttps=function(e){var t=new u(e);return t.request=s.request,t},t.httpsOverHttps=function(e){var t=new u(e);return t.request=s.request,t.createSocket=l,t.defaultPort=443,t},c.inherits(u,a.EventEmitter),u.prototype.addRequest=function(e,t,r,n){var o=this,i=d({request:e},o.options,p(t,r,n));o.sockets.length>=this.maxSockets?o.requests.push(i):o.createSocket(i,function(t){function r(){o.emit("free",t,i)}function n(e){o.removeSocket(t),t.removeListener("free",r),t.removeListener("close",n),t.removeListener("agentRemove",n)}t.on("free",r),t.on("close",n),t.on("agentRemove",n),e.onSocket(t)})},u.prototype.createSocket=function(e,t){var r=this,o={};r.sockets.push(o);var i=d({},r.proxyOptions,{method:"CONNECT",path:e.host+":"+e.port,agent:!1,headers:{host:e.host+":"+e.port}});e.localAddress&&(i.localAddress=e.localAddress),i.proxyAuth&&(i.headers=i.headers||{},i.headers["Proxy-Authorization"]="Basic "+new Buffer(i.proxyAuth).toString("base64")),n("making CONNECT request");var s=r.request(i);function a(i,a,c){var u;return s.removeAllListeners(),a.removeAllListeners(),200!==i.statusCode?(n("tunneling socket could not be established, statusCode=%d",i.statusCode),a.destroy(),(u=new Error("tunneling socket could not be established, statusCode="+i.statusCode)).code="ECONNRESET",e.request.emit("error",u),void r.removeSocket(o)):c.length>0?(n("got illegal response body from proxy"),a.destroy(),(u=new Error("got illegal response body from proxy")).code="ECONNRESET",e.request.emit("error",u),void r.removeSocket(o)):(n("tunneling connection has established"),r.sockets[r.sockets.indexOf(o)]=a,t(a))}s.useChunkedEncodingByDefault=!1,s.once("response",function(e){e.upgrade=!0}),s.once("upgrade",function(e,t,r){process.nextTick(function(){a(e,t,r)})}),s.once("connect",a),s.once("error",function(t){s.removeAllListeners(),n("tunneling socket could not be established, cause=%s\n",t.message,t.stack);var i=new Error("tunneling socket could not be established, cause="+t.message);i.code="ECONNRESET",e.request.emit("error",i),r.removeSocket(o)}),s.end()},u.prototype.removeSocket=function(e){var t=this.sockets.indexOf(e);if(-1!==t){this.sockets.splice(t,1);var r=this.requests.shift();r&&this.createSocket(r,function(e){r.request.onSocket(e)})}},n=process.env.NODE_DEBUG&&/\btunnel\b/.test(process.env.NODE_DEBUG)?function(){var e=Array.prototype.slice.call(arguments);"string"==typeof e[0]?e[0]="TUNNEL: "+e[0]:e.unshift("TUNNEL:"),console.error.apply(console,e)}:function(){},t.debug=n},7847:(e,t,r)=>{"use strict";r.d(t,{i:()=>n,x:()=>o});var n=500,o="Microsoft_ApplicationInsights_BypassAjaxInstrumentation"},7867:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,M:()=>p});var n,o=r(269),i=r(6182),s=r(6492),a=[s.fc,s.Yp,s.dI,s.l0],c=null;function u(e,t){return function(){var r=arguments,n=l(t);if(n){var o=n.listener;o&&o[e]&&o[e][i.y9](o,r)}}}function l(e){var t,r=c;return r||!0===e.disableDbgExt||(r=c||((t=(0,o.zS2)("Microsoft"))&&(c=t.ApplicationInsights),c)),r?r.ChromeDbgExt:null}function p(e){if(!n){n={};for(var t=0;t<a[i.oI];t++)n[a[t]]=u(a[t],e)}return n}},7937:(e,t)=>{"use strict";var r;function n(e){const t=/^https:\/\/github\.com\/([^/]+)\/([^/]+?)(\.git)?$/i.exec(e)||/^git@github\.com:([^/]+)\/([^/]+?)(\.git)?$/i.exec(e);return t?{owner:t[1],repo:t[2]}:void 0}function o(e){const t=e.state.remotes.filter(e=>e.fetchUrl&&n(e.fetchUrl));if(0!==t.length)return(t.find(e=>"upstream"===e.name)??t.find(e=>"origin"===e.name)??t[0]).fetchUrl}Object.defineProperty(t,"__esModule",{value:!0}),t.sequentialize=t.DisposableStore=void 0,t.groupBy=function(e,t){const r=[];let n;for(const o of e.slice(0).sort(t))n&&0===t(n[0],o)?n.push(o):(n=[o],r.push(n));return r},t.getRepositoryFromUrl=n,t.getRepositoryFromQuery=function(e){const t=/^([^/]+)\/([^/]+)$/i.exec(e);return t?{owner:t[1],repo:t[2]}:void 0},t.repositoryHasGitHubRemote=function(e){return!!e.state.remotes.find(e=>e.fetchUrl?n(e.fetchUrl):void 0)},t.getRepositoryDefaultRemoteUrl=o,t.getRepositoryDefaultRemote=function(e){const t=o(e);return t?n(t):void 0},t.DisposableStore=class{constructor(){this.disposables=new Set}add(e){this.disposables.add(e)}dispose(){for(const e of this.disposables)e.dispose();this.disposables.clear()}},t.sequentialize=(r=function(e,t){const r=`__$sequence$${t}`;return function(...t){const n=this[r]||Promise.resolve(null),o=async()=>await e.apply(this,t);return this[r]=n.then(o,o),this[r]}},(e,t,n)=>{let o=null,i=null;if("function"==typeof n.value?(o="value",i=n.value):"function"==typeof n.get&&(o="get",i=n.get),!i||!o)throw new Error("not supported");n[o]=r(i,t)})},7975:(e,t,r)=>{"use strict";r.d(t,{HQ:()=>m,Rr:()=>u,Vj:()=>g,Vk:()=>p,Vt:()=>d,_T:()=>h,lq:()=>c,pJ:()=>l,qW:()=>v,xP:()=>f,zx:()=>a});var n=r(269),o=r(3775),i=r(7292),s=r(5130);function a(e,t,r){var o=t[s.oI],i=c(e,t);if(i[s.oI]!==o){for(var a=0,u=i;void 0!==r[u];)a++,u=(0,n.P0f)(i,0,147)+v(a);i=u}return i}function c(e,t){var r;return t&&(t=(0,n.EHq)((0,n.oJg)(t)))[s.oI]>150&&(r=(0,n.P0f)(t,0,150),(0,o.ZP)(e,2,57,"name is too long.  It has been truncated to 150 characters.",{name:t},!0)),r||t}function u(e,t,r){var i;return void 0===r&&(r=1024),t&&(r=r||1024,(t=(0,n.EHq)((0,n.oJg)(t)))[s.oI]>r&&(i=(0,n.P0f)(t,0,r),(0,o.ZP)(e,2,61,"string value is too long. It has been truncated to "+r+" characters.",{value:t},!0))),i||t}function l(e,t){return h(e,t,2048,66)}function p(e,t){var r;return t&&t[s.oI]>32768&&(r=(0,n.P0f)(t,0,32768),(0,o.ZP)(e,2,56,"message is too long, it has been truncated to 32768 characters.",{message:t},!0)),r||t}function d(e,t){var r;if(t){var i=""+t;i[s.oI]>32768&&(r=(0,n.P0f)(i,0,32768),(0,o.ZP)(e,2,52,"exception is too long, it has been truncated to 32768 characters.",{exception:t},!0))}return r||t}function f(e,t){if(t){var r={};(0,n.zav)(t,function(t,c){if((0,n.Gvm)(c)&&(0,i.Z)())try{c=(0,i.hm)()[s.Jj](c)}catch(t){(0,o.ZP)(e,2,49,"custom property is not valid",{exception:t},!0)}c=u(e,c,8192),t=a(e,t,r),r[t]=c}),t=r}return t}function g(e,t){if(t){var r={};(0,n.zav)(t,function(t,n){t=a(e,t,r),r[t]=n}),t=r}return t}function m(e,t){return t?h(e,t,128,69)[s.xE]():t}function h(e,t,r,i){var a;return t&&(t=(0,n.EHq)((0,n.oJg)(t)))[s.oI]>r&&(a=(0,n.P0f)(t,0,r),(0,o.ZP)(e,2,i,"input is too long, it has been truncated to "+r+" characters.",{data:t},!0)),a||t}function v(e){var t="00"+e;return(0,n.hKY)(t,t[s.oI]-3)}},8107:function(e,t,r){"use strict";var n=this&&this.__decorate||function(e,t,r,n){var o,i=arguments.length,s=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(i<3?o(s):i>3?o(t,r,s):o(t,r))||s);return i>3&&s&&Object.defineProperty(t,r,s),s};Object.defineProperty(t,"__esModule",{value:!0}),t.GitHubSourceControlHistoryItemDetailsProvider=void 0;const o=r(1398),i=r(7937),s=r(7171),a=r(2676),c=/(([A-Za-z0-9_.\-]+)\/([A-Za-z0-9_.\-]+))?(#|GH-)([1-9][0-9]*)($|\b)/g;function u(e,t){const r=(e.authorEmail??"").localeCompare(t.authorEmail??"");return 0!==r?r:(e.authorName??"").localeCompare(t.authorName??"")}class l{constructor(e,t){this._gitAPI=e,this._logger=t,this._isUserAuthenticated=!0,this._store=new Map,this._disposables=new i.DisposableStore,this._disposables.add(this._gitAPI.onDidCloseRepository(e=>this._onDidCloseRepository(e))),this._disposables.add(o.authentication.onDidChangeSessions(e=>{"github"===e.provider.id&&(this._isUserAuthenticated=!0)})),this._disposables.add(o.workspace.onDidChangeConfiguration(e=>{e.affectsConfiguration("github.showAvatar")&&this._store.clear()}))}async provideAvatar(e,t){this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][provideAvatar] Avatar resolution for ${t.commits.length} commit(s) in ${e.rootUri.fsPath}.`);const r=!0===o.workspace.getConfiguration("github",e.rootUri).get("showAvatar",!0);if(!this._isUserAuthenticated||!r)return void this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][provideAvatar] Avatar resolution is disabled. (${!1===r?"setting":"auth"})`);const n=(0,i.getRepositoryDefaultRemote)(e);if(n)try{const r={cached:0,email:0,github:0,incomplete:0};await this._loadAssignableUsers(n);const o=this._store.get(this._getRepositoryKey(n));if(!o)return;const s=(0,i.groupBy)(t.commits,u),c=new Map;return await Promise.all(s.map(async e=>{if(0===e.length)return;const i=o.users.find(t=>t.email===e[0].authorEmail||t.name===e[0].authorName)?.avatarUrl;if(i)return r.cached+=e.length,void e.forEach(({hash:e})=>c.set(e,`${i}&s=${t.size}`));if(e.some(({hash:e})=>o.commits.has(e)))return void e.forEach(({hash:e})=>c.set(e,void 0));const s=function(e){const t=e?.match(/^([0-9]+)\+[^@]+@users\.noreply\.github\.com$/);return t?.[1]}(e[0].authorEmail);if(s){r.email+=e.length;const n=(0,a.getAvatarLink)(s,t.size);return void e.forEach(({hash:e})=>c.set(e,n))}const u=await this._getCommitAuthor(n,e[0].hash);if(u)o.users.push(u),r.github+=e.length,e.forEach(({hash:e})=>c.set(e,`${u.avatarUrl}&s=${t.size}`));else{r.incomplete+=e.length;for(const{hash:t}of e)o.commits.add(t),c.set(t,void 0)}})),this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][provideAvatar] Avatar resolution for ${t.commits.length} commit(s) in ${e.rootUri.fsPath} complete: ${JSON.stringify(r)}.`),c}catch(e){return void(e instanceof s.AuthenticationError&&(this._isUserAuthenticated=!1))}else this._logger.trace("[GitHubSourceControlHistoryItemDetailsProvider][provideAvatar] Repository does not have a GitHub remote.")}async provideHoverCommands(e){const t=(0,i.getRepositoryDefaultRemoteUrl)(e);if(t)return[{title:o.l10n.t("{0} Open on GitHub","$(github)"),tooltip:o.l10n.t("Open on GitHub"),command:"github.openOnGitHub",arguments:[t]}]}async provideMessageLinks(e,t){const r=(0,i.getRepositoryDefaultRemote)(e);if(r)return t.replace(c,(e,t,n,o,i,s)=>!s||Number.isNaN(parseInt(s))?e:`[${n&&o?`${n}/${o}#${s}`:`#${s}`}](https://github.com/${n=n??r.owner}/${o=o??r.repo}/issues/${s})`)}_onDidCloseRepository(e){for(const t of e.state.remotes){if(!t.fetchUrl)continue;const e=(0,i.getRepositoryFromUrl)(t.fetchUrl);e&&this._store.delete(this._getRepositoryKey(e))}}async _loadAssignableUsers(e){if(!this._store.has(this._getRepositoryKey(e))){this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][_loadAssignableUsers] Querying assignable user(s) for ${e.owner}/${e.repo}.`);try{const t=await(0,s.getOctokitGraphql)(),{repository:r}=await t("\n\tquery assignableUsers($owner: String!, $repo: String!) {\n\t\trepository(owner: $owner, name: $repo) {\n\t\t\tassignableUsers(first: 100) {\n\t\t\t\tnodes {\n\t\t\t\t\tid\n\t\t\t\t\tlogin\n\t\t\t\t\tname\n\t\t\t\t\temail\n\t\t\t\t\tavatarUrl\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n",e),n=[];for(const e of r.assignableUsers.nodes??[])e&&n.push({id:e.id,login:e.login,name:e.name,email:e.email,avatarUrl:e.avatarUrl});this._store.set(this._getRepositoryKey(e),{users:n,commits:new Set}),this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][_loadAssignableUsers] Successfully queried assignable user(s) for ${e.owner}/${e.repo}: ${n.length} user(s).`)}catch(t){throw this._logger.warn(`[GitHubSourceControlHistoryItemDetailsProvider][_loadAssignableUsers] Failed to load assignable user(s) for ${e.owner}/${e.repo}: ${t}`),t}}}async _getCommitAuthor(e,t){this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][_getCommitAuthor] Querying commit author for ${e.owner}/${e.repo}/${t}.`);try{const r=await(0,s.getOctokitGraphql)(),{repository:n}=await r("\n\tquery commitAuthor($owner: String!, $repo: String!, $commit: String!) {\n\t\trepository(owner: $owner, name: $repo) {\n\t\t\tobject(expression: $commit) {\n\t\t\t\t... on Commit {\n\t\t\t\t\tauthor {\n\t\t\t\t\t\tname\n\t\t\t\t\t\temail\n\t\t\t\t\t\tavatarUrl\n\t\t\t\t\t\tuser {\n\t\t\t\t\t\t\tid\n\t\t\t\t\t\t\tlogin\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n",{...e,commit:t}),o=n.object.author;if(!(o?.user?.id&&o.user?.login&&o?.name&&o?.email&&o?.avatarUrl))return void this._logger.info(`[GitHubSourceControlHistoryItemDetailsProvider][_getCommitAuthor] Incomplete commit author for ${e.owner}/${e.repo}/${t}: ${JSON.stringify(n.object)}`);const i={id:o.user.id,login:o.user.login,name:o.name,email:o.email,avatarUrl:o.avatarUrl};return this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][_getCommitAuthor] Successfully queried commit author for ${e.owner}/${e.repo}/${t}: ${i.login}.`),i}catch(r){throw this._logger.warn(`[GitHubSourceControlHistoryItemDetailsProvider][_getCommitAuthor] Failed to get commit author for ${e.owner}/${e.repo}/${t}: ${r}`),r}}_getRepositoryKey(e){return`${e.owner}/${e.repo}`}dispose(){this._disposables.dispose()}}t.GitHubSourceControlHistoryItemDetailsProvider=l,n([i.sequentialize],l.prototype,"_loadAssignableUsers",null)},8156:(e,t,r)=>{"use strict";r.d(t,{NS:()=>d,Q6:()=>p,Z4:()=>m,r2:()=>g});var n=r(8279),o=r(269),i=r(6182),s=r(6492),a="ctx",c="ParentContextKey",u="ChildrenContextKey",l=null,p=function(){function e(t,r,n){var s,l=this;l.start=(0,o.f0d)(),l[i.RS]=t,l[i.tI]=n,l[i.Zu]=function(){return!1},(0,o.Tnt)(r)&&(0,o.vF1)(l,"payload",{g:function(){return!s&&(0,o.Tnt)(r)&&(s=r(),r=null),s}}),l[i.O_]=function(t){return t?t===e[c]||t===e[u]?l[t]:(l[a]||{})[t]:null},l[i.e_]=function(t,r){t&&(t===e[c]?(l[t]||(l[i.Zu]=function(){return!0}),l[t]=r):t===e[u]?l[t]=r:(l[a]=l[a]||{})[t]=r)},l[i.Ru]=function(){var t=0,r=l[i.O_](e[u]);if((0,o.cyL)(r))for(var n=0;n<r[i.oI];n++){var s=r[n];s&&(t+=s[i.fA])}l[i.fA]=(0,o.f0d)()-l.start,l.exTime=l[i.fA]-t,l[i.Ru]=function(){}}}return e.ParentContextKey="parent",e.ChildrenContextKey="childEvts",e}(),d=function(){function e(t){this.ctx={},(0,n.A)(e,this,function(e){e.create=function(e,t,r){return new p(e,t,r)},e.fire=function(e){e&&(e[i.Ru](),t&&(0,o.Tnt)(t[s.l0])&&t[s.l0](e))},e[i.e_]=function(t,r){t&&((e[a]=e[a]||{})[t]=r)},e[i.O_]=function(t){return(e[a]||{})[t]}})}return e.__ieDyn=1,e}(),f="CoreUtils.doPerf";function g(e,t,r,n,o){if(e){var a=e;if(a[s.kI]&&(a=a[s.kI]()),a){var l=void 0,d=a[i.O_](f);try{if(l=a.create(t(),n,o)){if(d&&l[i.e_]&&(l[i.e_](p[c],d),d[i.O_]&&d[i.e_])){var g=d[i.O_](p[u]);g||(g=[],d[i.e_](p[u],g)),g[i.y5](l)}return a[i.e_](f,l),r(l)}}catch(e){l&&l[i.e_]&&l[i.e_]("exception",e)}finally{l&&a.fire(l),a[i.e_](f,d)}}}return r()}function m(){return l}},8205:(e,t,r)=>{"use strict";r.d(t,{Dv:()=>u,Qo:()=>G,Rf:()=>I,Xf:()=>U,lh:()=>D});var n,o,i,s=r(269),a="Promise",c="rejected";function u(e,t){return l(e,function(e){return t?t({status:"fulfilled",rejected:!1,value:e}):e},function(e){return t?t({status:c,rejected:!0,reason:e}):e})}function l(e,t,r,n){var o=e;try{if((0,s.$XS)(e))(t||r)&&(o=e.then(t,r));else try{t&&(o=t(e))}catch(e){if(!r)throw e;o=r(e)}}finally{n&&function(e,t){var r=e;t&&((0,s.$XS)(e)?r=e.finally?e.finally(t):e.then(function(e){return t(),e},function(e){throw t(),e}):t())}(o,n)}return o}var p,d=!1,f=["pending","resolving","resolved",c],g="dispatchEvent";function m(e){var t;return e&&e.createEvent&&(t=e.createEvent("Event")),!!t&&t.initEvent}var h,v,y,b,E="unhandledRejection",T=E.toLowerCase(),w=[],_=0,P=10;function S(e){return(0,s.Tnt)(e)?e.toString():(0,s.mmD)(e)}function k(e,t,r){var c,u,l=(0,s.KVm)(arguments,3),v=0,y=!1,b=[],k=_++,O=w.length>0?w[w.length-1]:void 0,C=!1,R=null;function A(t,r){try{return w.push(k),C=!0,R&&R.cancel(),R=null,e(function(e,n){b.push(function(){try{var o=2===v?t:r,i=(0,s.b07)(o)?c:(0,s.Tnt)(o)?o(c):o;(0,s.$XS)(i)?i.then(e,n):o?e(i):3===v?n(i):e(i)}catch(e){n(e)}}),y&&D()},l)}finally{w.pop()}}function I(){return f[v]}function D(){if(b.length>0){var e=b.slice();b=[],C=!0,R&&R.cancel(),R=null,t(e)}}function G(e,t){return function(r){if(v===t){if(2===e&&(0,s.$XS)(r))return v=1,void r.then(G(2,1),G(3,1));v=e,y=!0,c=r,D(),C||3!==e||R||(R=(0,s.dRz)(U,P))}}}function U(){if(!C)if(C=!0,(0,s.Lln)())process.emit(E,c,u);else{var e=(0,s.zkX)()||(0,s.mS$)();!h&&(h=(0,s.GuU)((0,s.gBW)(s.zS2,[a+"RejectionEvent"]).v)),function(e,t,r,n){var o=(0,s.YEm)();!p&&(p=(0,s.GuU)(!!(0,s.gBW)(m,[o]).v));var i=p.v?o.createEvent("Event"):n?new Event(t):{};if(r&&r(i),p.v&&i.initEvent(t,!1,!0),i&&e[g])e[g](i);else{var a=e["on"+t];if(a)a(i);else{var c=(0,s.zS2)("console");c&&(c.error||c.log)(t,(0,s.mmD)(i))}}}(e,T,function(e){return(0,s.vF1)(e,"promise",{g:function(){return u}}),e.reason=c,e},!!h.v)}}return u={then:A,catch:function(e){return A(void 0,e)},finally:function(e){var t=e,r=e;return(0,s.Tnt)(e)&&(t=function(t){return e&&e(),t},r=function(t){throw e&&e(),t}),A(t,r)}},(0,s.UxO)(u,"state",{get:I}),d&&function(e,t){o=o||{toString:function(){return"[[PromiseResult]]"}},i=i||{toString:function(){return"[[PromiseIsHandled]]"}};var r={};r[n=n||{toString:function(){return"[[PromiseState]]"}}]={get:t},r[o]={get:function(){return(0,s.SZ2)(c)}},r[i]={get:function(){return C}},(0,s.isD)(e,r)}(u,I),(0,s.Lok)()&&(u[(0,s.Y0g)(11)]="IPromise"),u.toString=function(){return"IPromise"+(d?"["+k+((0,s.b07)(O)?"":":"+O)+"]":"")+" "+I()+(y?" - "+S(c):"")},function(){(0,s.Tnt)(r)||(0,s.zkd)(a+": executor is not a function - "+S(r));var e=G(3,0);try{r.call(u,G(2,0),e)}catch(t){e(t)}}(),u}function O(e){return function(t){var r=(0,s.KVm)(arguments,1);return e(function(e,r){try{var n=[],o=1;(0,s.DA8)(t,function(t,i){t&&(o++,l(t,function(t){n[i]=t,0===--o&&e(n)},r))}),0===--o&&e(n)}catch(e){r(e)}},r)}}function C(e){(0,s.Iuo)(e,function(e){try{e()}catch(e){}})}function R(e,t){return k(R,function(e){var t=(0,s.EtT)(e)?e:0;return function(e){(0,s.dRz)(function(){C(e)},t)}}(t),e,t)}function A(e,t){!v&&(v=(0,s.GuU)((0,s.gBW)(s.zS2,[a]).v||null));var r=v.v;if(!r)return R(e);(0,s.Tnt)(e)||(0,s.zkd)(a+": executor is not a function - "+(0,s.mmD)(e));var n=0,o=new r(function(t,r){e(function(e){n=2,t(e)},function(e){n=3,r(e)})});return(0,s.UxO)(o,"state",{get:function(){return f[n]}}),o}function I(e){return k(I,C,e)}function D(e,t){return!y&&(r=I,y=(0,s.GuU)(function(e){var t=(0,s.KVm)(arguments,1);return r(function(t,r){var n=[],o=1;function i(e,r){o++,u(e,function(e){e.rejected?n[r]={status:c,reason:e.reason}:n[r]={status:"fulfilled",value:e.value},0===--o&&t(n)})}try{(0,s.cyL)(e)?(0,s.Iuo)(e,i):(0,s.xZI)(e)?(0,s.DA8)(e,i):(0,s.zkd)("Input is not an iterable"),0===--o&&t(n)}catch(e){r(e)}},t)})),y.v(e,t);var r}function G(e,t){return!b&&(b=(0,s.GuU)(A)),b.v.call(this,e,t)}var U=O(G);(0,s.Y0g)(11)},8257:(e,t,r)=>{"use strict";r.d(t,{s:()=>h});var n,o=r(8279),i=r(269),s=r(9749),a=r(6182),c=r(3775),u=r(3673),l=r(6492),p=r(2317),d=r(836),f=r(8969),g="getPlugin",m=((n={})[l.Bw]={isVal:u.Gh,v:{}},n),h=function(){function e(){var t,r,n,h,v,y=this;function b(e){void 0===e&&(e=null);var t=e;if(!t){var o=r||(0,p.i8)(null,{},y[l.eT]);t=n&&n[g]?o[a.$o](null,n[g]):o[a.$o](null,n)}return t}function E(e,t,o){(0,s.e)(e,m,(0,c.y0)(t)),!o&&t&&(o=t[a.DI]()[a.uR]());var i=n;n&&n[g]&&(i=n[g]()),y[l.eT]=t,r=(0,p.i8)(o,e,t,i)}function T(){t=!1,y[l.eT]=null,r=null,n=null,v=(0,f.w)(),h=(0,d.P)()}T(),(0,o.A)(e,y,function(e){e[a.mE]=function(e,r,n,o){E(e,r,o),t=!0},e[a.Ik]=function(t,r){var o,i=e[l.eT];if(i&&(!t||i===t[l.eT]())){var s,c=!1,u=t||(0,p.tS)(null,i,n&&n[g]?n[g]():n),d=r||((o={reason:0})[a.tI]=!1,o);return e[a.tn]&&!0===e[a.tn](u,d,f)?s=!0:f(),s}function f(){c||(c=!0,h.run(u,r),v.run(u[a.e4]()),!0===s&&u[a.$5](d),T())}},e[a.HC]=function(t,r){var o=e[l.eT];if(o&&(!t||o===t[l.eT]())){var i,s=!1,c=t||(0,p.nU)(null,o,n&&n[g]?n[g]():n),u=r||{reason:0};return e._doUpdate&&!0===e._doUpdate(c,u,d)?i=!0:d(),i}function d(){s||(s=!0,E(c.getCfg(),c.core(),c[a.uR]()))}},(0,u.RF)(e,"_addUnloadCb",function(){return h},"add"),(0,u.RF)(e,"_addHook",function(){return v},"add"),(0,i.vF1)(e,"_unloadHooks",{g:function(){return v}})}),y[a.e4]=function(e){return b(e)[a.e4]()},y[a.tZ]=function(){return t},y.setInitialized=function(e){t=e},y[a.YH]=function(e){n=e},y[a.$5]=function(e,t){t?t[a.$5](e):n&&(0,i.Tnt)(n[l.qT])&&n[l.qT](e,null)},y._getTelCtx=b}return e.__ieDyn=1,e}()},8262:function(e,t,r){"use strict";var n,o=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(n=function(e){return n=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t},n(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=n(e),s=0;s<r.length;s++)"default"!==r[s]&&o(t,e,r[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.VscodeDevShareProvider=void 0;const a=s(r(1398)),c=r(7937),u=r(2676);t.VscodeDevShareProvider=class{set hasGitHubRepositories(e){a.commands.executeCommand("setContext","github.hasGitHubRepo",e),this._hasGitHubRepositories=e,this.ensureShareProviderRegistration()}constructor(e){this.gitAPI=e,this.id="copyVscodeDevLink",this.label=a.l10n.t("Copy vscode.dev Link"),this.priority=10,this._hasGitHubRepositories=!1,this.disposables=[],this.initializeGitHubRepoContext()}dispose(){this.disposables.forEach(e=>e.dispose())}initializeGitHubRepoContext(){this.gitAPI.repositories.find(e=>(0,c.repositoryHasGitHubRemote)(e))?(this.hasGitHubRepositories=!0,a.commands.executeCommand("setContext","github.hasGitHubRepo",!0)):this.disposables.push(this.gitAPI.onDidOpenRepository(async e=>{await e.status(),(0,c.repositoryHasGitHubRemote)(e)&&(a.commands.executeCommand("setContext","github.hasGitHubRepo",!0),this.hasGitHubRepositories=!0)})),this.disposables.push(this.gitAPI.onDidCloseRepository(()=>{this.gitAPI.repositories.find(e=>(0,c.repositoryHasGitHubRemote)(e))||(this.hasGitHubRepositories=!1)}))}ensureShareProviderRegistration(){if("codespaces"!==a.env.appHost&&!this.shareProviderRegistration&&this._hasGitHubRepositories){const e=a.window.registerShareProvider({scheme:"file"},this);this.shareProviderRegistration=e,this.disposables.push(e)}else this.shareProviderRegistration&&!this._hasGitHubRepositories&&(this.shareProviderRegistration.dispose(),this.shareProviderRegistration=void 0)}async provideShare(e,t){const r=(0,u.getRepositoryForFile)(this.gitAPI,e.resourceUri);if(!r)return;let n;if(await(0,u.ensurePublished)(r,e.resourceUri),r.state.remotes.find(e=>{if(e.fetchUrl){const t=(0,c.getRepositoryFromUrl)(e.fetchUrl);if(t&&e.name===r.state.HEAD?.upstream?.remote)return void(n=t);t&&!n&&(n=t)}}),!n)return;const o=r?.state.HEAD?.name?(0,u.encodeURIComponentExceptSlashes)(r.state.HEAD?.name):r?.state.HEAD?.commit,i=(0,u.encodeURIComponentExceptSlashes)(e.resourceUri.path.substring(r?.rootUri.path.length)),s=function(e){if("vscode-notebook-cell"===e.resourceUri.scheme){const t=a.window.visibleNotebookEditors.find(t=>t.notebook.uri.fsPath===e.resourceUri.fsPath),r=t?.notebook.getCells().find(t=>t.document.uri.fragment===e.resourceUri?.fragment),n=r?.index??t?.selection.start;return(0,u.notebookCellRangeString)(n,e.selection)}return(0,u.rangeString)(e.selection)}(e);return a.Uri.parse(`${this.getVscodeDevHost()}/${n.owner}/${n.repo}/blob/${o}${i}${s}`)}getVscodeDevHost(){return`https://${a.env.appName.toLowerCase().includes("insiders")?"insiders.":""}vscode.dev/github`}}},8279:(e,t,r)=>{"use strict";r.d(t,{A:()=>L});var n,o=r(269),i="constructor",s="prototype",a="function",c="_dynInstFuncs",u="_isDynProxy",l="_dynClass",p="_dynCls$",d="_dynInstChk",f=d,g="_dfOpts",m="_unknown_",h="__proto__",v="_dyn"+h,y="__dynProto$Gbl",b="_dynInstProto",E="useBaseInst",T="setInstFuncs",w=Object,_=w.getPrototypeOf,P=w.getOwnPropertyNames,S=(0,o.mS$)(),k=S[y]||(S[y]={o:(n={},n[T]=!0,n[E]=!0,n),n:1e3});function O(e){return e&&(e===w[s]||e===Array[s])}function C(e){return O(e)||e===Function[s]}function R(e){var t;if(e){if(_)return _(e);var r=e[h]||e[s]||(e[i]?e[i][s]:null);t=e[v]||r,(0,o.v0u)(e,v)||(delete e[b],t=e[v]=e[b]||e[v],e[b]=r)}return t}function A(e,t){var r=[];if(P)r=P(e);else for(var n in e)"string"==typeof n&&(0,o.v0u)(e,n)&&r.push(n);if(r&&r.length>0)for(var i=0;i<r.length;i++)t(r[i])}function I(e,t,r){return t!==i&&typeof e[t]===a&&(r||(0,o.v0u)(e,t))&&t!==h&&t!==s}function D(e){(0,o.zkd)("DynamicProto: "+e)}function G(e,t){for(var r=e.length-1;r>=0;r--)if(e[r]===t)return!0;return!1}function U(e,t,r,n,i){if(!O(e)){var s=r[c]=r[c]||(0,o.sSX)(null);if(!O(s)){var p=s[t]=s[t]||(0,o.sSX)(null);!1!==s[f]&&(s[f]=!!i),O(p)||A(r,function(t){I(r,t,!1)&&r[t]!==n[t]&&(p[t]=r[t],delete r[t],(!(0,o.v0u)(e,t)||e[t]&&!e[t][u])&&(e[t]=function(e,t){var r=function(){var n=function(e,t,r,n){var i=null;if(e&&(0,o.v0u)(r,l)){var s=e[c]||(0,o.sSX)(null);if((i=(s[r[l]]||(0,o.sSX)(null))[t])||D("Missing ["+t+"] "+a),!i[d]&&!1!==s[f]){for(var u=!(0,o.v0u)(e,t),p=R(e),g=[];u&&p&&!C(p)&&!G(g,p);){var m=p[t];if(m){u=m===n;break}g.push(p),p=R(p)}try{u&&(e[t]=i),i[d]=1}catch(e){s[f]=!1}}}return i}(this,t,e,r)||function(e,t,r){var n=t[e];return n===r&&(n=R(t)[e]),typeof n!==a&&D("["+e+"] is not a "+a),n}(t,e,r);return n.apply(this,arguments)};return r[u]=1,r}(e,t)))})}}}function F(e,t){return(0,o.v0u)(e,s)?e.name||t||m:((e||{})[i]||{}).name||t||m}function L(e,t,r,n){(0,o.v0u)(e,s)||D("theClass is an invalid class definition.");var i=e[s];(function(e,t){if(_){for(var r=[],n=R(t);n&&!C(n)&&!G(r,n);){if(n===e)return!0;r.push(n),n=R(n)}return!1}return!0})(i,t)||D("["+F(e)+"] not in hierarchy of ["+F(t)+"]");var a=null;(0,o.v0u)(i,l)?a=i[l]:(a=p+F(e,"_")+"$"+k.n,k.n++,i[l]=a);var d=L[g],m=!!d[E];m&&n&&void 0!==n[E]&&(m=!!n[E]);var h=function(e){var t=(0,o.sSX)(null);return A(e,function(r){!t[r]&&I(e,r,!1)&&(t[r]=e[r])}),t}(t),v=function(e,t,r,n){function i(e,t,r){var o=t[r];if(o[u]&&n){var i=e[c]||{};!1!==i[f]&&(o=(i[t[l]]||{})[r]||o)}return function(){return o.apply(e,arguments)}}var s=(0,o.sSX)(null);A(r,function(e){s[e]=i(t,r,e)});for(var a=R(e),p=[];a&&!C(a)&&!G(p,a);)A(a,function(e){!s[e]&&I(a,e,!_)&&(s[e]=i(t,a,e))}),p.push(a),a=R(a);return s}(i,t,h,m);r(t,v);var y=!!_&&!!d[T];y&&n&&(y=!!n[T]),U(i,a,t,h,!1!==y)}L[g]=k.o},8330:e=>{"use strict";e.exports=JSON.parse('{"name":"github","displayName":"%displayName%","description":"%description%","publisher":"vscode","license":"MIT","version":"0.0.1","engines":{"vscode":"^1.41.0"},"aiKey":"0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255","icon":"images/icon.png","categories":["Other"],"activationEvents":["*"],"extensionDependencies":["vscode.git-base"],"main":"./out/extension.js","capabilities":{"virtualWorkspaces":false,"untrustedWorkspaces":{"supported":true}},"enabledApiProposals":["canonicalUriProvider","contribEditSessions","contribShareMenu","contribSourceControlHistoryItemMenu","scmHistoryProvider","shareProvider","timeline"],"contributes":{"commands":[{"command":"github.publish","title":"%command.publish%"},{"command":"github.copyVscodeDevLink","title":"%command.copyVscodeDevLink%"},{"command":"github.copyVscodeDevLinkFile","title":"%command.copyVscodeDevLink%"},{"command":"github.copyVscodeDevLinkWithoutRange","title":"%command.copyVscodeDevLink%"},{"command":"github.openOnVscodeDev","title":"%command.openOnVscodeDev%","icon":"$(globe)"},{"command":"github.graph.openOnGitHub","title":"%command.openOnGitHub%","icon":"$(github)"},{"command":"github.timeline.openOnGitHub","title":"%command.openOnGitHub%","icon":"$(github)"}],"continueEditSession":[{"command":"github.openOnVscodeDev","when":"github.hasGitHubRepo","qualifiedName":"Continue Working in vscode.dev","category":"Remote Repositories","remoteGroup":"virtualfs_44_vscode-vfs_2_web@2"}],"menus":{"commandPalette":[{"command":"github.publish","when":"git-base.gitEnabled && workspaceFolderCount != 0 && remoteName != \'codespaces\'"},{"command":"github.graph.openOnGitHub","when":"false"},{"command":"github.copyVscodeDevLink","when":"false"},{"command":"github.copyVscodeDevLinkFile","when":"false"},{"command":"github.copyVscodeDevLinkWithoutRange","when":"false"},{"command":"github.openOnVscodeDev","when":"false"},{"command":"github.timeline.openOnGitHub","when":"false"}],"file/share":[{"command":"github.copyVscodeDevLinkFile","when":"github.hasGitHubRepo && remoteName != \'codespaces\'","group":"0_vscode@0"}],"editor/context/share":[{"command":"github.copyVscodeDevLink","when":"github.hasGitHubRepo && resourceScheme != untitled && !isInEmbeddedEditor && remoteName != \'codespaces\'","group":"0_vscode@0"}],"explorer/context/share":[{"command":"github.copyVscodeDevLinkWithoutRange","when":"github.hasGitHubRepo && resourceScheme != untitled && !isInEmbeddedEditor && remoteName != \'codespaces\'","group":"0_vscode@0"}],"editor/lineNumber/context":[{"command":"github.copyVscodeDevLink","when":"github.hasGitHubRepo && resourceScheme != untitled && activeEditor == workbench.editors.files.textFileEditor && config.editor.lineNumbers == on && remoteName != \'codespaces\'","group":"1_cutcopypaste@2"},{"command":"github.copyVscodeDevLink","when":"github.hasGitHubRepo && resourceScheme != untitled && activeEditor == workbench.editor.notebook && remoteName != \'codespaces\'","group":"1_cutcopypaste@2"}],"editor/title/context/share":[{"command":"github.copyVscodeDevLinkWithoutRange","when":"github.hasGitHubRepo && resourceScheme != untitled && remoteName != \'codespaces\'","group":"0_vscode@0"}],"scm/historyItem/context":[{"command":"github.graph.openOnGitHub","when":"github.hasGitHubRepo","group":"0_view@2"}],"scm/historyItem/hover":[{"command":"github.graph.openOnGitHub","when":"github.hasGitHubRepo","group":"1_open@1"}],"timeline/item/context":[{"command":"github.timeline.openOnGitHub","group":"1_actions@3","when":"github.hasGitHubRepo && timelineItem =~ /git:file:commit\\\\b/"}]},"configuration":[{"title":"GitHub","properties":{"github.branchProtection":{"type":"boolean","scope":"resource","default":true,"description":"%config.branchProtection%"},"github.gitAuthentication":{"type":"boolean","scope":"resource","default":true,"description":"%config.gitAuthentication%"},"github.gitProtocol":{"type":"string","enum":["https","ssh"],"default":"https","description":"%config.gitProtocol%"},"github.showAvatar":{"type":"boolean","scope":"resource","default":true,"description":"%config.showAvatar%"}}}],"viewsWelcome":[{"view":"scm","contents":"%welcome.publishFolder%","when":"config.git.enabled && git.state == initialized && workbenchState == folder && git.parentRepositoryCount == 0 && git.unsafeRepositoryCount == 0 && git.closedRepositoryCount == 0"},{"view":"scm","contents":"%welcome.publishWorkspaceFolder%","when":"config.git.enabled && git.state == initialized && workbenchState == workspace && workspaceFolderCount != 0 && git.parentRepositoryCount == 0 && git.unsafeRepositoryCount == 0 && git.closedRepositoryCount == 0"}],"markdown.previewStyles":["./markdown.css"]},"scripts":{"vscode:prepublish":"npm run compile","compile":"gulp compile-extension:github","watch":"gulp watch-extension:github"},"dependencies":{"@octokit/graphql":"8.2.0","@octokit/graphql-schema":"14.4.0","@octokit/rest":"21.1.0","tunnel":"^0.0.6","@vscode/extension-telemetry":"^0.9.8"},"devDependencies":{"@types/node":"20.x"},"repository":{"type":"git","url":"https://github.com/microsoft/vscode.git"}}')},8393:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TelemetryUtil=void 0;class r{static applyReplacements(e,t){for(const r of Object.keys(e))for(const n of t)n.lookup.test(r)&&(void 0!==n.replacementString?e[r]=n.replacementString:delete e[r])}static shouldUseOneDataSystemSDK(e){return 74===e.length&&"-"===e[32]&&"-"===e[41]&&"-"===e[46]&&"-"===e[51]&&"-"===e[56]&&"-"===e[69]}static getAdditionalCommonProperties(e){return{"common.os":e.platform,"common.nodeArch":e.architecture,"common.platformversion":(e.release||"").replace(/^(\d+)(\.\d+)?(\.\d+)?(.*)/,"$1$2$3"),"common.telemetryclientversion":"0.9.8"}}static getInstance(){return r._instance||(r._instance=new r),r._instance}}t.TelemetryUtil=r},8566:function(e,t,r){"use strict";var n,o=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(n=function(e){return n=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t},n(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=n(e),s=0;s<r.length;s++)"default"!==r[s]&&o(t,e,r[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.publishRepository=async function(e,t){if(!a.workspace.workspaceFolders?.length)return;let r;if(t)r=t.rootUri;else if(1===e.repositories.length)t=e.repositories[0],r=t.rootUri;else if(1===a.workspace.workspaceFolders.length)r=a.workspace.workspaceFolders[0].uri;else{const e=a.workspace.workspaceFolders.map(e=>({label:e.name,folder:e})),t=a.l10n.t("Pick a folder to publish to GitHub"),n=await a.window.showQuickPick(e,{placeHolder:t});if(!n)return;r=n.folder.uri}let n,o,i,s,f=a.window.createQuickPick();f.ignoreFocusOut=!0,f.placeholder="Repository Name",f.value=(0,l.basename)(r.fsPath),f.show(),f.busy=!0;try{o=await(0,c.getOctokit)();const e=await o.users.getAuthenticated({});n=e.data.login}catch(e){return void f.dispose()}f.busy=!1;const g=async()=>{const e=f.value.trim().replace(/[^a-z0-9_.]/gi,"-");f.items=e?[{label:"$(repo) Publish to GitHub private repository",description:`$(github) ${n}/${e}`,alwaysShow:!0,repo:e,isPrivate:!0},{label:"$(repo) Publish to GitHub public repository",description:`$(github) ${n}/${e}`,alwaysShow:!0,repo:e,isPrivate:!1}]:[]};for(g();;){const e=f.onDidChangeValue(g),t=await d(f);if(e.dispose(),i=t?.repo,s=t?.isPrivate??!0,i)try{f.busy=!0,await o.repos.get({owner:n,repo:i}),f.items=[{label:"$(error) GitHub repository already exists",description:`$(github) ${n}/${i}`,alwaysShow:!0}]}catch{break}finally{f.busy=!1}}if(f.dispose(),!i)return;if(!t){const e=a.Uri.joinPath(r,".gitignore");let t=!1;try{await a.workspace.fs.stat(e)}catch(e){t=!0}if(t){f=a.window.createQuickPick(),f.placeholder=a.l10n.t("Select which files should be included in the repository."),f.canSelectMany=!0,f.show();try{f.busy=!0;const t=(await a.workspace.fs.readDirectory(r)).map(([e])=>e).filter(e=>".git"!==e);f.items=t.map(e=>({label:e})),f.selectedItems=f.items,f.busy=!1;const n=await Promise.race([new Promise(e=>f.onDidAccept(()=>e(f.selectedItems))),new Promise(e=>f.onDidHide(()=>e(void 0)))]);if(!n||0===n.length)return;const o=new Set(t);if(n.forEach(e=>o.delete(e.label)),o.size>0){const t=[...o].map(e=>`/${e}`).join("\n"),r=new u.TextEncoder;await a.workspace.fs.writeFile(e,r.encode(t))}}finally{f.dispose()}}}const m=await a.window.withProgress({location:a.ProgressLocation.Notification,cancellable:!1,title:"Publish to GitHub"},async n=>{let c;if(n.report({message:s?a.l10n.t("Publishing to a private GitHub repository"):a.l10n.t("Publishing to a public GitHub repository"),increment:25}),c=(0,p.isInCodespaces)()?await a.commands.executeCommand("github.codespaces.publish",{name:i,isPrivate:s}):(await o.repos.createForAuthenticatedUser({name:i,private:s})).data,c){if(n.report({message:a.l10n.t("Creating first commit"),increment:25}),!t){if(!(t=await e.init(r,{defaultBranch:c.default_branch})||void 0))return;await t.commit("first commit",{all:!0,postCommitCommand:null})}n.report({message:a.l10n.t("Uploading files"),increment:25});const o=await t.getBranch("HEAD"),i="https"===a.workspace.getConfiguration("github").get("gitProtocol")?c.clone_url:c.ssh_url;await t.addRemote("origin",i),await t.push("origin",o.name,!0)}return c});if(!m)return;const h=a.l10n.t("Open on GitHub");a.window.showInformationMessage(a.l10n.t('Successfully published the "{0}" repository to GitHub.',`${n}/${i}`),h).then(e=>{e===h&&a.commands.executeCommand("vscode.open",a.Uri.parse(m.html_url))})};const a=s(r(1398)),c=r(7171),u=r(9023),l=r(6928),p=r(3703);function d(e){return Promise.race([new Promise(t=>e.onDidAccept(()=>e.selectedItems.length>0&&t(e.selectedItems[0]))),new Promise(t=>e.onDidHide(()=>t(void 0)))])}},8596:(e,t,r)=>{"use strict";r.d(t,{o:()=>g});var n=r(659),o=r(3673);function i(e){var t="ai."+e+".";return function(e){return t+e}}var s=i("application"),a=i("device"),c=i("location"),u=i("operation"),l=i("session"),p=i("user"),d=i("cloud"),f=i("internal"),g=function(e){function t(){return e.call(this)||this}return(0,n.qU)(t,e),t}((0,o.SZ)({applicationVersion:s("ver"),applicationBuild:s("build"),applicationTypeId:s("typeId"),applicationId:s("applicationId"),applicationLayer:s("layer"),deviceId:a("id"),deviceIp:a("ip"),deviceLanguage:a("language"),deviceLocale:a("locale"),deviceModel:a("model"),deviceFriendlyName:a("friendlyName"),deviceNetwork:a("network"),deviceNetworkName:a("networkName"),deviceOEMName:a("oemName"),deviceOS:a("os"),deviceOSVersion:a("osVersion"),deviceRoleInstance:a("roleInstance"),deviceRoleName:a("roleName"),deviceScreenResolution:a("screenResolution"),deviceType:a("type"),deviceMachineName:a("machineName"),deviceVMName:a("vmName"),deviceBrowser:a("browser"),deviceBrowserVersion:a("browserVersion"),locationIp:c("ip"),locationCountry:c("country"),locationProvince:c("province"),locationCity:c("city"),operationId:u("id"),operationName:u("name"),operationParentId:u("parentId"),operationRootId:u("rootId"),operationSyntheticSource:u("syntheticSource"),operationCorrelationVector:u("correlationVector"),sessionId:l("id"),sessionIsFirst:l("isFirst"),sessionIsNew:l("isNew"),userAccountAcquisitionDate:p("accountAcquisitionDate"),userAccountId:p("accountId"),userAgent:p("userAgent"),userId:p("id"),userStoreRegion:p("storeRegion"),userAuthUserId:p("authUserId"),userAnonymousUserAcquisitionDate:p("anonUserAcquisitionDate"),userAuthenticatedUserAcquisitionDate:p("authUserAcquisitionDate"),cloudName:d("name"),cloudRole:d("role"),cloudRoleVer:d("roleVer"),cloudRoleInstance:d("roleInstance"),cloudEnvironment:d("environment"),cloudLocation:d("location"),cloudDeploymentUnit:d("deploymentUnit"),internalNodeName:f("nodeName"),internalSdkVersion:f("sdkVersion"),internalAgentVersion:f("agentVersion"),internalSnippet:f("snippet"),internalSdkSrc:f("sdkSrc")}))},8611:e=>{"use strict";e.exports=require("http")},8916:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BE_PROFILE:()=>i,NRT_PROFILE:()=>o,PostChannel:()=>Tt,RT_PROFILE:()=>n});var n="REAL_TIME",o="NEAR_REAL_TIME",i="BEST_EFFORT",s=r(659),a=r(8279),c=r(4822),u=r(269),l=r(8156),p=r(6149),d=r(4276),f=r(9749),g=r(2317),m=r(3673),h=r(3662),v=r(3775),y=r(8257),b=r(8205),E="",T="drop",w="requeue",_="no-cache, no-store",P="application/x-json-stream",S="cache-control",k="content-type",O="client-version",C="client-id",R="time-delta-to-apply-millis",A="upload-time",I="apikey",D="AuthMsaDeviceTicket",G="WebAuthToken",U="AuthXToken",F="msfpc",L="trace",x="user",H="allowRequestSending",j="firstRequestSent",M="shouldAddClockSkewHeaders",N="getClockSkewHeaderValue",$="setClockSkew",q="length",z="concat",B="iKey",V="count",X="events",W="push",K="split",Z="splice",J="toLowerCase",Q="hdrs",Y="useHdrs",ee="initialize",te="setTimeoutOverride",re="clearTimeoutOverride",ne="overrideEndpointUrl",oe="avoidOptions",ie="enableCompoundKey",se="disableXhrSync",ae="disableFetchKeepAlive",ce="useSendBeacon",ue="fetchCredentials",le="alwaysUseXhrOverride",pe="serializeOfflineEvt",de="getOfflineRequestDetails",fe="createPayload",ge="createOneDSPayload",me="payloadBlob",he="headers",ve="_thePayload",ye="urlString",be="batches",Ee="sendType",Te="addHeader",we="canSendRequest",_e="sendQueuedRequests",Pe="isCompletelyIdle",Se="setUnloading",ke="resume",Oe="sendSynchronousBatch",Ce="_transport",Re="getWParam",Ae="isBeacon",Ie="timings",De="isTeardown",Ge="isSync",Ue="data",Fe="_sendReason",Le="setKillSwitchTenants",xe="_backOffTransmission",He="identifier",je="eventsLimitInMem",Me="autoFlushEventsLimit",Ne="baseData",$e="sendAttempt",qe="latency",ze="sync";function Be(e){var t=(e.ext||{}).intweb;return t&&(0,c.yD)(t[F])?t[F]:null}function Ve(e){for(var t=null,r=0;null===t&&r<e[q];r++)t=Be(e[r]);return t}var Xe=function(){function e(t,r){var n=r?[][z](r):[],o=this,i=Ve(n);o[B]=function(){return t},o.Msfpc=function(){return i||E},o[V]=function(){return n[q]},o[X]=function(){return n},o.addEvent=function(e){return!!e&&(n[W](e),i||(i=Be(e)),!0)},o[K]=function(r,o){var s;if(r<n[q]){var a=n[q]-r;(0,u.hXl)(o)||(a=o<a?o:a),s=n[Z](r,a),i=Ve(n)}return new e(t,s)}}return e.create=function(t,r){return new e(t,r)},e}(),We=r(7292),Ke=r(856),Ze=r(5664),Je=function(){function e(){var t=!0,r=!0,n=!0,o="use-collector-delta",i=!1;(0,a.A)(e,this,function(e){e[H]=function(){return t},e[j]=function(){n&&(n=!1,i||(t=!1))},e[M]=function(){return r},e[N]=function(){return o},e[$]=function(e){i||(e?(o=e,r=!0,i=!0):r=!1,t=!0)}})}return e.__ieDyn=1,e}(),Qe=function(){function e(){var t={};(0,a.A)(e,this,function(e){e[Le]=function(e,r){if(e&&r)try{var n=(s=e[K](","),a=[],s&&(0,u.Iuo)(s,function(e){a[W]((0,u.EHq)(e))}),a);if("this-request-only"===r)return n;for(var o=1e3*parseInt(r,10),i=0;i<n[q];++i)t[n[i]]=(0,u.f0d)()+o}catch(e){return[]}var s,a;return[]},e.isTenantKilled=function(e){var r=t,n=(0,u.EHq)(e);return void 0!==r[n]&&r[n]>(0,u.f0d)()||(delete r[n],!1)}})}return e.__ieDyn=1,e}();function Ye(e){var t,r=Math.floor(1200*Math.random())+2400;return t=Math.pow(2,e)*r,Math.min(t,6e5)}var et,tt=2e6,rt=Math.min(tt,65e3),nt="metadata",ot="f",it=/\./,st=function(){function e(t,r,n,o,i,s){var p="data",d="baseData",f=!!o,g=!0,m=r,h={},v=!!s,y=i||c.Go;(0,a.A)(e,this,function(e){function r(e,t,o,i,s,a,l){(0,u.zav)(e,function(e,p){var d=null;if(p||(0,c.yD)(p)){var g=o,v=e,y=s,b=t;if(f&&!i&&it.test(e)){var E=e.split("."),T=E.length;if(T>1){y&&(y=y.slice());for(var w=0;w<T-1;w++){var _=E[w];b=b[_]=b[_]||{},g+="."+_,y&&y.push(_)}v=E[T-1]}}var P=i&&function(e){var t=h[e];return void 0===t&&(e.length>=7&&(t=(0,u.tGl)(e,"ext.metadata")||(0,u.tGl)(e,"ext.web")),h[e]=t),t}(g);if(d=!P&&m&&m.handleField(g,v)?m.value(g,v,p,n):(0,c.TC)(v,p,n)){var S=d.value;if(b[v]=S,a&&a(y,v,d),l&&"object"==typeof S&&!(0,u.cyL)(S)){var k=y;k&&(k=k.slice()).push(v),r(p,S,g+"."+v,i,k,a,l)}}}})}e.createPayload=function(e,t,r,n,o,i){return{apiKeys:[],payloadBlob:E,overflow:null,sizeExceed:[],failedEvts:[],batches:[],numEvents:0,retryCnt:e,isTeardown:t,isSync:r,isBeacon:n,sendType:i,sendReason:o}},e.appendPayload=function(r,n,o){var i=r&&n&&!r.overflow;return i&&(0,l.r2)(t,function(){return"Serializer:appendPayload"},function(){for(var t=n.events(),i=r.payloadBlob,s=r.numEvents,a=!1,c=[],l=[],p=r.isBeacon,d=p?65e3:3984588,f=p?rt:tt,g=0,m=0;g<t.length;){var h=t[g];if(h){if(s>=o){r.overflow=n.split(g);break}var v=e.getEventBlob(h);if(v&&v.length<=f){var y=v.length;if(i.length+y>d){r.overflow=n.split(g);break}i&&(i+="\n"),i+=v,++m>20&&((0,u.hKY)(i,0,1),m=0),a=!0,s++}else v?c.push(h):l.push(h),t.splice(g,1),g--}g++}if(c.length>0&&r.sizeExceed.push(Xe.create(n.iKey(),c)),l.length>0&&r.failedEvts.push(Xe.create(n.iKey(),l)),a){r.batches.push(n),r.payloadBlob=i,r.numEvents=s;var b=n.iKey();-1===(0,u.rDm)(r.apiKeys,b)&&r.apiKeys.push(b)}},function(){return{payload:r,theBatch:{iKey:n.iKey(),evts:n.events()},max:o}}),i},e.getEventBlob=function(e){try{return(0,l.r2)(t,function(){return"Serializer.getEventBlob"},function(){var t={};t.name=e.name,t.time=e.time,t.ver=e.ver,t.iKey="o:"+(0,c.EO)(e.iKey);var n,o={};v||(n=function(e,t,r){!function(e,t,r,n,o){if(o&&t){var i=e(o.value,o.kind,o.propertyType);if(i>-1){var s=t[nt];s||(s=t[nt]={f:{}});var a=s[ot];if(a||(a=s[ot]={}),r)for(var c=0;c<r.length;c++){var l=r[c];a[l]||(a[l]={f:{}});var p=a[l][ot];p||(p=a[l][ot]={}),a=p}a=a[n]={},(0,u.cyL)(o.value)?a.a={t:i}:a.t=i}}}(y,o,e,t,r)});var i=e.ext;i&&(t.ext=o,(0,u.zav)(i,function(e,t){r(t,o[e]={},"ext."+e,!0,null,null,!0)}));var s=t[p]={};s.baseType=e.baseType;var a=s[d]={};return r(e.baseData,a,d,!1,[d],n,g),r(e.data,s,p,!1,[],n,g),JSON.stringify(t)},function(){return{item:e}})}catch(e){return null}}})}return e.__ieDyn=1,e}();function at(e,t){return{set:function(r,n){for(var o=[],i=2;i<arguments.length;i++)o[i-2]=arguments[i];return(0,u.vKV)([e,t],r,n,o)}}}var ct="sendAttempt",ut="?cors=true&"+k[J]()+"="+P,lt=((et={})[1]=w,et[100]=w,et[200]="sent",et[8004]=T,et[8003]=T,et),pt={},dt={};function ft(e,t,r){pt[e]=t,!1!==r&&(dt[t]=e)}function gt(e,t){var r=!1;if(e&&t){var n=(0,u.cGk)(e);if(n&&n[q]>0)for(var o=t[J](),i=0;i<n[q];i++){var s=n[i];if(s&&(0,u.v0u)(t,s)&&s[J]()===o){r=!0;break}}}return r}function mt(e,t,r,n){t&&r&&r[q]>0&&(n&&pt[t]?(e[Q][pt[t]]=r,e[Y]=!0):e.url+="&"+t+"="+r)}ft(D,D,!1),ft(O,O),ft(C,"Client-Id"),ft(I,I),ft(R,R),ft(A,A),ft(U,U);var ht=function(){function e(t,r,n,o){var i,s,p,d,g,h,y,b,w,D,G,U,L,x,J,He,je,Me,Ne,$e,qe,ze,Be,Ve,et,tt,rt,nt,ot,it,pt,ft,ht=!1;(0,a.A)(e,this,function(e){function a(e,t){try{return pt&&pt.getSenderInst(e,t)}catch(e){}return null}function vt(){try{return{enableSendPromise:!1,isOneDs:!0,disableCredentials:!1,fetchCredentials:ft,disableXhr:!1,disableBeacon:!ht,disableBeaconSync:!ht,disableFetchKeepAlive:qe,timeWrapper:ot,addNoResponse:Be,senderOnCompleteCallBack:{xdrOnComplete:yt,fetchOnComplete:bt,xhrOnComplete:Et,beaconOnRetry:wt}}}catch(e){}return null}function yt(e,t,r){var n=(0,m.Lo)(e);Tt(t,200,{},n),Ut(n)}function bt(e,t,r,n){var o={},i=e[he];i&&i.forEach(function(e,t){o[t]=e}),function(e,r,n){Tt(t,e,r,n),Ut(n)}(e.status,o,r||E)}function Et(e,t,r){var n=(0,m.Lo)(e);Tt(t,e.status,(0,m.w3)(e,!0),n),Ut(n)}function Tt(e,t,r,n){try{e(t,r,n)}catch(e){(0,v.ZP)(y,2,518,(0,u.mmD)(e))}}function wt(e,t,r){var n=200,o=e[ve],i=e[ye]+(Be?"&NoResponseBody=true":E);try{var s=(0,u.w3n)();if(o){var a=!!w.getPlugin("LocalStorage"),c=[],l=[];(0,u.Iuo)(o[be],function(e){if(c&&e&&e[V]()>0)for(var t=e[X](),r=0;r<t[q];r++){if(!s.sendBeacon(i,x.getEventBlob(t[r]))){c[W](e[K](r));break}l[W](e[r])}else c[W](e[K](0))}),l[q]>0&&(o.sentEvts=l),a||Ft(c,8003,o[Ee],!0)}else n=0}catch(e){(0,v.OG)(y,"Failed to send telemetry using sendBeacon API. Ex:"+(0,u.mmD)(e)),n=0}finally{Tt(t,n,{},E)}}function _t(e){return 2===e||3===e}function Pt(e){return je&&_t(e)&&(e=2),e}function St(){return!p&&g<r}function kt(){var e=L;return L=[],e}function Ot(e,t,r){var n=!1;return e&&e[q]>0&&!p&&b[t]&&x&&(n=0!==t||St()&&(r>0||d[H]())),n}function Ct(e){var t={};return e&&(0,u.Iuo)(e,function(e,r){t[r]={iKey:e[B](),evts:e[X]()}}),t}function Rt(e,r,n,o,i){if(e&&0!==e[q])if(p)Ft(e,1,o);else{o=Pt(o);try{var a=e,d=0!==o;(0,l.r2)(w,function(){return"HttpManager:_sendBatches"},function(a){a&&(e=e.slice(0));for(var u=[],l=null,p=(0,c.WB)(),f=b[o]||(d?b[1]:b[0]),g=f&&f[Ce],m=ze&&(je||_t(o)||3===g||f._isSync&&2===g);Ot(e,o,r);){var h=e.shift();h&&h[V]()>0&&(s.isTenantKilled(h[B]())?u[W](h):(l=l||x[fe](r,n,d,m,i,o),x.appendPayload(l,h,t)?null!==l.overflow&&(e=[l.overflow][z](e),l.overflow=null,Dt(l,p,(0,c.WB)(),i),p=(0,c.WB)(),l=null):(Dt(l,p,(0,c.WB)(),i),p=(0,c.WB)(),e=[h][z](e),l=null)))}l&&Dt(l,p,(0,c.WB)(),i),e[q]>0&&(L=e[z](L)),Ft(u,8004,o)},function(){return{batches:Ct(a),retryCount:r,isTeardown:n,isSynchronous:d,sendReason:i,useSendBeacon:_t(o),sendType:o}},!d)}catch(e){(0,v.ZP)(y,2,48,"Unexpected Exception sending batch: "+(0,u.mmD)(e))}}}function At(e,t){var r={url:i,hdrs:{},useHdrs:!1};t?(r[Q]=(0,c.X$)(r[Q],U),r.useHdrs=(0,u.cGk)(r.hdrs)[q]>0):(0,u.zav)(U,function(e,t){dt[e]?mt(r,dt[e],t,!1):(r[Q][e]=t,r[Y]=!0)}),mt(r,C,"NO_AUTH",t),mt(r,O,c.xE,t);var n=E;(0,u.Iuo)(e.apiKeys,function(e){n[q]>0&&(n+=","),n+=e}),mt(r,I,n,t),mt(r,A,(0,u.f0d)().toString(),t);var o=function(e){for(var t=0;t<e.batches[q];t++){var r=e[be][t].Msfpc();if(r)return encodeURIComponent(r)}return E}(e);if((0,c.yD)(o)&&(r.url+="&ext.intweb.msfpc="+o),d[M]()&&mt(r,R,d[N](),t),w[Re]){var s=w[Re]();s>=0&&(r.url+="&w="+s)}for(var a=0;a<G[q];a++)r.url+="&"+G[a].name+"="+G[a].value;return r}function It(e,t,r){e[t]=e[t]||{},e[t][h.identifier]=r}function Dt(t,r,o,i){if(t&&t.payloadBlob&&t.payloadBlob[q]>0){var a=!!et,p=b[t.sendType];!_t(t[Ee])&&t[Ae]&&2===t.sendReason&&(p=b[2]||b[3]||p);var f=Me;(t.isBeacon||3===p[Ce])&&(f=!1);var m=At(t,f);f=f||m[Y];var E=(0,c.WB)();(0,l.r2)(w,function(){return"HttpManager:_doPayloadSend"},function(){for(var b=0;b<t.batches[q];b++)for(var T=t[be][b][X](),O=0;O<T[q];O++){var C=T[O];if(J){var R=C[Ie]=C[Ie]||{};It(R,"sendEventStart",E),It(R,"serializationStart",r),It(R,"serializationCompleted",o)}C[ct]>0?C[ct]++:C[ct]=1}Ft(t[be],1e3+(i||0),t[Ee],!0);var A={data:t[me],urlString:m.url,headers:m[Q],_thePayload:t,_sendReason:i,timeout:Ne,disableXhrSync:$e,disableFetchKeepAlive:qe};f&&(gt(A[he],S)||(A[he][S]=_),gt(A[he],k)||(A[he][k]=P));var I=null;p&&(I=function(r){d[j]();var o=function(r,o){!function(t,r,o,i){var a,l=9e3,p=null,f=!1,m=!1;try{var v=!0;if(typeof t!==Ze.bA){if(r){d[$](r["time-delta-millis"]);var y=r["kill-duration"]||r["kill-duration-seconds"];(0,u.Iuo)(s[Le](r["kill-tokens"],y),function(e){(0,u.Iuo)(o[be],function(t){if(t[B]()===e){p=p||[];var r=t[K](0);o.numEvents-=r[V](),p[W](r)}})})}if(200==t||204==t)return void(l=200);((a=t)>=300&&a<500&&429!=a||501==a||505==a||o.numEvents<=0)&&(v=!1),l=9e3+t%1e3}if(v){l=100;var b=o.retryCnt;0===o[Ee]&&(b<n?(f=!0,Gt(function(){0===o[Ee]&&g--,Rt(o[be],b+1,o[De],je?2:o[Ee],5)},je,Ye(b))):(m=!0,je&&(l=8001)))}}finally{f||(d[$](),function(t,r,n,o){try{o&&h[xe]();var i=t[be];200===r&&(i=t.sentEvts||t[be],o||t[Ge]||h._clearBackOff(),function(e){if(J){var t=(0,c.WB)();(0,u.Iuo)(e,function(e){e&&e[V]()>0&&function(e,t){J&&(0,u.Iuo)(e,function(e){It(e[Ie]=e[Ie]||{},"sendEventCompleted",t)})}(e[X](),t)})}}(i)),Ft(i,r,t[Ee],!0)}finally{0===t[Ee]&&(g--,5!==n&&e.sendQueuedRequests(t[Ee],n))}}(o,l,i,m)),Ft(p,8004,o[Ee])}}(r,o,t,i)},a=t[De]||t[Ge];try{p.sendPOST(r,o,a),tt&&tt(A,r,a,t[Ae])}catch(e){(0,v.OG)(y,"Unexpected exception sending payload. Ex:"+(0,u.mmD)(e)),Tt(o,0,{})}}),(0,l.r2)(w,function(){return"HttpManager:_doPayloadSend.sender"},function(){if(I)if(0===t[Ee]&&g++,a&&!t.isBeacon&&3!==p[Ce]){var e={data:A[Ue],urlString:A[ye],headers:(0,c.X$)({},A[he]),timeout:A.timeout,disableXhrSync:A[se],disableFetchKeepAlive:A[ae]},r=!1;(0,l.r2)(w,function(){return"HttpManager:_doPayloadSend.sendHook"},function(){try{et(e,function(e){r=!0,D||e[ve]||(e[ve]=e[ve]||A[ve],e[Fe]=e[Fe]||A[Fe]),I(e)},t.isSync||t[De])}catch(e){r||I(A)}})}else I(A)})},function(){return{thePayload:t,serializationStart:r,serializationCompleted:o,sendReason:i}},t[Ge])}t.sizeExceed&&t.sizeExceed[q]>0&&Ft(t.sizeExceed,8003,t[Ee]),t.failedEvts&&t.failedEvts[q]>0&&Ft(t.failedEvts,8002,t[Ee])}function Gt(e,t,r){t?e():ot.set(e,r)}function Ut(e){var t=rt;try{for(var r=0;r<t[q];r++)try{t[r](e)}catch(e){(0,v.ZP)(y,1,519,"Response handler failed: "+e)}if(e){var n=JSON.parse(e);(0,c.yD)(n.webResult)&&(0,c.yD)(n.webResult[F])&&He.set("MSFPC",n.webResult[F],31536e3)}}catch(e){}}function Ft(e,t,r,n){if(e&&e[q]>0&&o){var i=o[(a=t,u=lt[a],(0,c.yD)(u)||(u="oth",a>=9e3&&a<=9999?u="rspFail":a>=8e3&&a<=8999?u=T:a>=1e3&&a<=1999&&(u="send")),u)];if(i){var s=0!==r;(0,l.r2)(w,function(){return"HttpManager:_sendBatchesNotification"},function(){Gt(function(){try{i.call(o,e,t,s,r)}catch(e){(0,v.ZP)(y,1,74,"send request notification failed: "+e)}},n||s,0)},function(){return{batches:Ct(e),reason:t,isSync:s,sendSync:n,sendType:r}},!s)}}var a,u}!function(){var e;i=null,s=new Qe,p=!1,d=new Je,ht=!1,g=0,h=null,y=null,b=null,w=null,D=!0,G=[],U={},L=[],x=null,J=!1,He=null,je=!1,Me=!1,Ne=e,$e=e,qe=e,ze=e,Be=e,Ve=[],et=e,tt=e,rt=[],nt=!1,ot=at(),it=!1,pt=null}(),e[ee]=function(e,t,r){nt||(w=t,He=t.getCookieMgr(),y=(h=r).diagLog(),(0,u.Yny)(Ve,(0,f.a)(e,function(e){var n,o=e.cfg,s=e.cfg.extensionConfig[r.identifier];ot=at(s[te],s[re]),(0,c.yD)(o.anonCookieName)?function(e,t,r){for(var n=0;n<e[q];n++)if(e[n].name===t)return void(e[n].value=r);e[W]({name:t,value:r})}(G,"anoncknm",o.anonCookieName):function(e){for(var t=0;t<e[q];t++)if("anoncknm"===e[t].name)return void e[Z](t,1)}(G),et=s.payloadPreprocessor,tt=s.payloadListener;var l=s.httpXHROverride,p=s[ne]?s[ne]:o.endpointUrl;i=p+ut,Me=!!(0,u.b07)(s[oe])||!s[oe],J=!s.disableEventTimings;var d=s.valueSanitizer,f=s.stringifyObjects,g=!!o[ie];(0,u.b07)(s[ie])||(g=!!s[ie]),Ne=s.xhrTimeout,$e=!!s[se],qe=!!s[ae],Be=!1!==s.addNoResponse,it=!!s.excludeCsMetaData,t.getPlugin("LocalStorage")&&(qe=!0),ht=!(0,We.lV)(),x=new st(w,d,f,g,c.Go,it),(0,u.hXl)(s[ce])||(ht=!!s[ce]),s[ue]&&(ft=s[ue]);var h=vt();pt?pt.SetConfig(h):(pt=new Ke.v)[ee](h,y);var E=l,T=s[le]?l:null,_=s[le]?l:null,P=[3,2];if(!l){D=!1;var S=[];(0,We.lV)()?(S=[2,1],P=[2,1,3]):S=[1,2,3],(l=a(S=(0,m.jL)(S,s.transports),!1))||(0,v.OG)(y,"No available transport to send events"),E=a(S,!0)}T||(T=a(P=(0,m.jL)(P,s.unloadTransports),!0)),ze=!D&&(ht&&(0,We.Uf)()||!qe&&(0,We.R7)(!0)),(n={})[0]=l,n[1]=E||a([1,2,3],!0),n[2]=T||E||a([1],!0),n[3]=_||a([2,3],!0)||E||a([1],!0),b=n})),nt=!0)},e.addResponseHandler=function(e){return rt[W](e),{rm:function(){var t=rt.indexOf(e);t>=0&&rt[Z](t,1)}}},e[pe]=function(e){try{if(x)return x.getEventBlob(e)}catch(e){}return E},e[de]=function(){try{return At(x&&x[fe](0,!1,!1,!1,1,0),Me)}catch(e){}return null},e[ge]=function(e,r){try{var n=[];(0,u.Iuo)(e,function(e){r&&(e=(0,m.hW)(e));var t=Xe.create(e[B],[e]);n[W](t)});for(var o=null;n[q]>0&&x;){var i=n.shift();i&&i[V]()>0&&(o=o||x[fe](0,!1,!1,!1,1,0),x.appendPayload(o,i,t))}var s=At(o,Me),a={data:o[me],urlString:s.url,headers:s[Q],timeout:Ne,disableXhrSync:$e,disableFetchKeepAlive:qe};return Me&&(gt(a[he],S)||(a[he][S]=_),gt(a[he],k)||(a[he][k]=P)),a}catch(e){}return null},e._getDbgPlgTargets=function(){return[b[0],s,x,b,vt(),i]},e[Te]=function(e,t){U[e]=t},e.removeHeader=function(e){delete U[e]},e[we]=function(){return St()&&d[H]()},e[_e]=function(e,t){(0,u.b07)(e)&&(e=0),je&&(e=Pt(e),t=2),Ot(L,e,0)&&Rt(kt(),0,!1,e,t||0)},e[Pe]=function(){return!p&&0===g&&0===L[q]},e[Se]=function(e){je=e},e.addBatch=function(e){if(e&&e[V]()>0){if(s.isTenantKilled(e[B]()))return!1;L[W](e)}return!0},e.teardown=function(){L[q]>0&&Rt(kt(),0,!0,2,2),(0,u.Iuo)(Ve,function(e){e&&e.rm&&e.rm()}),Ve=[]},e.pause=function(){p=!0},e[ke]=function(){p=!1,e[_e](0,4)},e[Oe]=function(e,t,r){e&&e[V]()>0&&((0,u.hXl)(t)&&(t=1),je&&(t=Pt(t),r=2),Rt([e],0,!1,t,r||0))}})}return e.__ieDyn=1,e}(),vt=1e4,yt="eventsDiscarded",bt=void 0,Et=(0,u.ZHX)({eventsLimitInMem:{isVal:c.ei,v:vt},immediateEventLimit:{isVal:c.ei,v:500},autoFlushEventsLimit:{isVal:c.ei,v:0},disableAutoBatchFlushLimit:!1,httpXHROverride:{isVal:function(e){return e&&e.sendPOST},v:bt},overrideInstrumentationKey:bt,overrideEndpointUrl:bt,disableTelemetry:!1,ignoreMc1Ms0CookieProcessing:!1,setTimeoutOverride:bt,clearTimeoutOverride:bt,payloadPreprocessor:bt,payloadListener:bt,disableEventTimings:bt,valueSanitizer:bt,stringifyObjects:bt,enableCompoundKey:bt,disableOptimizeObj:!1,fetchCredentials:bt,transports:bt,unloadTransports:bt,useSendBeacon:bt,disableFetchKeepAlive:bt,avoidOptions:!1,xhrTimeout:bt,disableXhrSync:bt,alwaysUseXhrOverride:!1,maxEventRetryAttempts:{isVal:u.EtT,v:6},maxUnloadEventRetryAttempts:{isVal:u.EtT,v:2},addNoResponse:bt,excludeCsMetaData:bt}),Tt=function(e){function t(){var r,s=e.call(this)||this;s.identifier="PostChannel",s.priority=1011,s.version="4.3.4";var y,E,T,w,_,P,S,k,O,C,R,A,I,U,F,H,j,M,N,$,J,Q,Y,ne,oe,ie=!1,se=[],ae=!1,ce=0,ue=0,le={},fe=n;return(0,a.A)(t,s,function(e,t){function s(){(0,p.Ds)(null,M),(0,p.sq)(null,M),(0,p.vF)(null,M)}function a(e){var t="";return e&&e[q]&&(0,u.Iuo)(e,function(e){t&&(t+="\n"),t+=e}),t}function me(e){var t="";try{ye(e),t=k[pe](e)}catch(e){}return t}function he(e){"beforeunload"!==(e||(0,u.zkX)().event).type&&(F=!0,k[Se](F)),Le(2,2)}function ve(e){F=!1,k[Se](F)}function ye(e){e.ext&&e.ext[L]&&delete e.ext[L],e.ext&&e.ext[x]&&e.ext[x].id&&delete e.ext[x].id,U&&(e.ext=(0,m.hW)(e.ext),e[Ne]&&(e[Ne]=(0,m.hW)(e[Ne])),e[Ue]&&(e[Ue]=(0,m.hW)(e[Ue])))}function Ee(e,t){if(e[$e]||(e[$e]=0),e[qe]||(e[qe]=1),ye(e),e[ze])if(P||ae)e[qe]=3,e[ze]=!1;else if(k)return U&&(e=(0,m.hW)(e)),void k[Oe](Xe.create(e[B],[e]),!0===e[ze]?1:e[ze],3);var r=e[qe],n=ue,o=T;4===r&&(n=ce,o=E);var i=!1;if(n<o)i=!We(e,t);else{var s=1,a=20;4===r&&(s=4,a=1),i=!0,function(e,t,r,n){for(;r<=t;){var o=Be(e,t,!0);if(o&&o[V]()>0){var i=o[K](0,n),s=i[V]();if(s>0)return 4===r?ce-=s:ue-=s,ot(yt,[i],h.x.QueueFull),!0}r++}return Ke(),!1}(e[B],e[qe],s,a)&&(i=!We(e,t))}i&&nt(yt,[e],h.x.QueueFull)}function Ce(e,t,r){var n=Ze(e,t,r);return k[_e](t,r),n}function Ae(){return ue>0}function Ie(){if(A>=0&&Ze(A,0,I)&&k[_e](0,I),ce>0&&!_&&!ae){var e=le[fe][2];e>=0&&(_=Ge(function(){_=null,Ce(4,0,1),Ie()},e))}var t=le[fe][1];!w&&!y&&t>=0&&!ae&&(Ae()?w=Ge(function(){w=null,Ce(0===S?3:1,0,1),S++,S%=2,Ie()},t):S=0)}function De(){r=null,ie=!1,se=[],y=null,ae=!1,ce=0,E=500,ue=0,T=vt,le={},fe=n,w=null,_=null,P=0,S=0,O={},C=0,Y=!1,R=0,A=-1,I=null,U=!0,F=!1,H=6,j=2,M=null,ne=null,oe=!1,N=at(),k=new ht(500,2,1,{requeue:tt,send:it,sent:st,drop:ct,rspFail:ut,oth:lt}),et(),O[4]={batches:[],iKeyMap:{}},O[3]={batches:[],iKeyMap:{}},O[2]={batches:[],iKeyMap:{}},O[1]={batches:[],iKeyMap:{}},pt()}function Ge(e,t){0===t&&P&&(t=1);var r=1e3;return P&&(r=Ye(P-1)),N.set(e,t*r)}function Fe(){return null!==w&&(w.cancel(),w=null,S=0,!0)}function Le(e,t){Fe(),y&&(y.cancel(),y=null),ae||Ce(1,e,t)}function Be(e,t,r){var n=O[t];n||(n=O[t=1]);var o=n.iKeyMap[e];return!o&&r&&(o=Xe.create(e),n.batches[W](o),n.iKeyMap[e]=o),o}function Ve(t,r){k[we]()&&!P&&(C>0&&ue>C&&(r=!0),r&&null==y&&e.flush(t,function(){},20))}function We(e,t){U&&(e=(0,m.hW)(e));var r=e[qe],n=Be(e[B],r,!0);return!!n.addEvent(e)&&(4!==r?(ue++,t&&0===e[$e]&&Ve(!e.sync,R>0&&n[V]()>=R)):ce++,!0)}function Ke(){for(var e=0,t=0,r=function(r){var n=O[r];n&&n[be]&&(0,u.Iuo)(n[be],function(n){4===r?e+=n[V]():t+=n[V]()})},n=1;n<=4;n++)r(n);ue=t,ce=e}function Ze(t,r,n){var o=!1,i=0===r;return!i||k[we]()?(0,l.r2)(e.core,function(){return"PostChannel._queueBatches"},function(){for(var e=[],r=4;r>=t;){var n=O[r];n&&n.batches&&n.batches[q]>0&&((0,u.Iuo)(n[be],function(t){k.addBatch(t)?o=o||t&&t[V]()>0:e=e[z](t[X]()),4===r?ce-=t[V]():ue-=t[V]()}),n[be]=[],n.iKeyMap={}),r--}e[q]>0&&nt(yt,e,h.x.KillSwitch),o&&A>=t&&(A=-1,I=0)},function(){return{latency:t,sendType:r,sendReason:n}},!i):(A=A>=0?Math.min(A,t):t,I=Math.max(I,n)),o}function Je(e,t){Ce(1,0,t),Ke(),Qe(function(){e&&e(),se[q]>0?y=Ge(function(){y=null,Je(se.shift(),t)},0):(y=null,Ie())})}function Qe(e){k[Pe]()?e():y=Ge(function(){y=null,Qe(e)},.25)}function et(){(le={})[n]=[2,1,0],le[o]=[6,3,0],le[i]=[18,9,0]}function tt(t,r){var n=[],o=H;F&&(o=j),(0,u.Iuo)(t,function(t){t&&t[V]()>0&&(0,u.Iuo)(t[X](),function(t){t&&(t[ze]&&(t[qe]=4,t[ze]=!1),t[$e]<o?((0,c.u9)(t,e[He]),Ee(t,!1)):n[W](t))})}),n[q]>0&&nt(yt,n,h.x.NonRetryableStatus),F&&Le(2,2)}function rt(t,r){var n=Q||{},o=n[t];if(o)try{o.apply(n,r)}catch(r){(0,v.ZP)(e.diagLog(),1,74,t+" notification failed: "+r)}}function nt(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];t&&t[q]>0&&rt(e,[t][z](r))}function ot(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];t&&t[q]>0&&(0,u.Iuo)(t,function(t){t&&t[V]()>0&&rt(e,[t.events()][z](r))})}function it(e,t,r){e&&e[q]>0&&rt("eventsSendRequest",[t>=1e3&&t<=1999?t-1e3:0,!0!==r])}function st(e,t){ot("eventsSent",e,t),Ie()}function ct(e,t){ot(yt,e,t>=8e3&&t<=8999?t-8e3:h.x.Unknown)}function ut(e){ot(yt,e,h.x.NonRetryableStatus),Ie()}function lt(e,t){ot(yt,e,h.x.Unknown),Ie()}function pt(){R=J?0:Math.max(1500,T/6)}De(),e._getDbgPlgTargets=function(){return[k,r]},e[ee]=function(n,o,i){(0,l.r2)(o,function(){return"PostChannel:initialize"},function(){t[ee](n,o,i),Q=o.getNotifyMgr();try{M=(0,p.Hm)((0,d.Z)(e[He]),o.evtNamespace&&o.evtNamespace()),e._addHook((0,f.a)(n,function(t){var n=t.cfg,i=(0,g.i8)(null,n,o);r=i.getExtCfg(e[He],Et),N=at(r[te],r[re]),U=!r.disableOptimizeObj&&(0,c.F2)(),$=r.ignoreMc1Ms0CookieProcessing,function(e){var t=e[Re];e[Re]=function(){var r=0;return $&&(r|=2),r|t.call(e)}}(o),T=r[je],E=r.immediateEventLimit,C=r[Me],H=r.maxEventRetryAttempts,j=r.maxUnloadEventRetryAttempts,J=r.disableAutoBatchFlushLimit,(0,u.$XS)(n.endpointUrl)?e.pause():ae&&e[ke](),pt(),ne=r.overrideInstrumentationKey,oe=!!r.disableTelemetry,Y&&s();var a=n.disablePageUnloadEvents||[];Y=(0,p.ee)(he,a,M),Y=(0,p.Fc)(he,a,M)||Y,Y=(0,p.oS)(ve,n.disablePageShowEvents,M)||Y})),k[ee](n,e.core,e)}catch(t){throw e.setInitialized(!1),t}},function(){return{theConfig:n,core:o,extensions:i}})},e.processTelemetry=function(t,r){(0,c.u9)(t,e[He]),r=r||e._getTelCtx(r);var n=t;oe||ie||(ne&&(n[B]=ne),Ee(n,!0),F?Le(2,2):Ie()),e.processNext(n,r)},e.getOfflineSupport=function(){try{var e=k&&k[de]();if(k)return{getUrl:function(){return e?e.url:null},serialize:me,batch:a,shouldProcess:function(e){return!oe},createPayload:function(e){return null},createOneDSPayload:function(e){if(k[ge])return k[ge](e,U)}}}catch(e){}return null},e._doTeardown=function(e,t){Le(2,2),ie=!0,k.teardown(),s(),De()},e.setEventQueueLimits=function(e,t){r[je]=T=(0,c.ei)(e)?e:vt,r[Me]=C=(0,c.ei)(t)?t:0,pt();var n=ue>e;if(!n&&R>0)for(var o=1;!n&&o<=3;o++){var i=O[o];i&&i[be]&&(0,u.Iuo)(i[be],function(e){e&&e[V]()>=R&&(n=!0)})}Ve(!0,n)},e.pause=function(){Fe(),ae=!0,k&&k.pause()},e[ke]=function(){ae=!1,k&&k[ke](),Ie()},e._loadTransmitProfiles=function(e){Fe(),et(),fe=n,Ie(),(0,u.zav)(e,function(e,t){var r=t[q];if(r>=2){var n=r>2?t[2]:0;if(t[Z](0,r-2),t[1]<0&&(t[0]=-1),t[1]>0&&t[0]>0){var o=t[0]/t[1];t[0]=Math.ceil(o)*t[1]}n>=0&&t[1]>=0&&n>t[1]&&(n=t[1]),t[W](n),le[e]=t}})},e.flush=function(e,t,r){var n;if(void 0===e&&(e=!0),!ae)if(r=r||1,e)t||(n=(0,b.Qo)(function(e){t=e})),null==y?(Fe(),Ze(1,0,r),y=Ge(function(){y=null,Je(t,r)},0)):se[W](t);else{var o=Fe();Ce(1,1,r),t&&t(),o&&Ie()}return n},e.setMsaAuthTicket=function(e){k[Te](D,e)},e.setAuthPluginHeader=function(e){k[Te](G,e)},e.removeAuthPluginHeader=function(){k.removeHeader(G)},e.hasEvents=Ae,e._setTransmitProfile=function(e){fe!==e&&void 0!==le[e]&&(Fe(),fe=e,Ie())},(0,m.o$)(e,function(){return k},["addResponseHandler"]),e[xe]=function(){P<4&&(P++,Fe(),Ie())},e._clearBackOff=function(){P&&(P=0,Fe(),Ie())}}),s}return(0,s.qU)(t,e),t.__ieDyn=1,t}(y.s)},8969:(e,t,r)=>{"use strict";r.d(t,{d:()=>c,w:()=>u});var n,o,i=r(269),s=r(6182),a=r(3775);function c(e,t){n=e,o=t}function u(){var e=[];return{run:function(t){var r=e;e=[],(0,i.Iuo)(r,function(e){try{(e.rm||e.remove).call(e)}catch(e){(0,a.ZP)(t,2,73,"Unloading:"+(0,i.mmD)(e))}}),n&&r[s.oI]>n&&(o?o("doUnload",r):(0,a.ZP)(null,1,48,"Max unload hooks exceeded. An excessive number of unload hooks has been detected."))},add:function(t){t&&((0,i.Yny)(e,t),n&&e[s.oI]>n&&(o?o("Add",e):(0,a.ZP)(null,1,48,"Max unload hooks exceeded. An excessive number of unload hooks has been detected.")))}}}},9023:e=>{"use strict";e.exports=require("util")},9147:(e,t,r)=>{"use strict";r.d(t,{Dy:()=>c,Hf:()=>p,If:()=>f,QA:()=>u,V9:()=>l,hF:()=>d,nM:()=>i});var n=r(269),o=r(6182),i=(0,n.eCG)("[[ai_dynCfg_1]]"),s=(0,n.eCG)("[[ai_blkDynCfg_1]]"),a=(0,n.eCG)("[[ai_frcDynCfg_1]]");function c(e){var t;return e&&((0,n.cyL)(e)?(t=[])[o.oI]=e[o.oI]:(0,n.QdQ)(e)&&(t={}),t)?((0,n.zav)(e,function(e,r){t[e]=c(r)}),t):e}function u(e){if(e){var t=e[i]||e;if(t.cfg&&(t.cfg===e||t.cfg[i]===t))return t}return null}function l(e){if(e&&((0,n.QdQ)(e)||(0,n.cyL)(e)))try{e[s]=!0}catch(e){}return e}function p(e){if(e)try{e[a]=!0}catch(e){}return e}function d(e,t,r){var o=!1;return r&&!e[t.blkVal]&&((o=r[a])||r[s]||(o=(0,n.QdQ)(r)||(0,n.cyL)(r))),o}function f(e){(0,n.zkd)("InvalidAccess:"+e)}},9278:e=>{"use strict";e.exports=require("net")},9354:(e,t,r)=>{"use strict";r.d(t,{Gz:()=>l,M0:()=>f,PS:()=>d,cM:()=>c,k6:()=>p,wX:()=>u});var n=r(269),o=r(5130),i=(0,n.YEm)()||{},s=0,a=[null,null,null,null,null];function c(e){var t=s,r=a,n=r[t];return i.createElement?r[t]||(n=r[t]=i.createElement("a")):n={host:d(e,!0)},n.href=e,++t>=r[o.oI]&&(t=0),s=t,n}function u(e){var t,r=c(e);return r&&(t=r.href),t}function l(e){var t,r=c(e);return r&&(t=r[o.Ue]),t}function p(e,t){return e?e.toUpperCase()+" "+t:t}function d(e,t){var r=f(e,t)||"";if(r){var i=r.match(/(www\d{0,5}\.)?([^\/:]{1,256})(:\d{1,20})?/i);if(null!=i&&i[o.oI]>3&&(0,n.KgX)(i[2])&&i[2][o.oI]>0)return i[2]+(i[3]||"")}return r}function f(e,t){var r=null;if(e){var i=e.match(/(\w{1,150}):\/\/([^\/:]{1,256})(:\d{1,20})?/i);if(null!=i&&i[o.oI]>2&&(0,n.KgX)(i[2])&&i[2][o.oI]>0&&(r=i[2]||"",t&&i[o.oI]>2)){var s=(i[1]||"")[o.OL](),a=i[3]||"";("http"===s&&":80"===a||"https"===s&&":443"===a)&&(a=""),r+=a}}return r}},9749:(e,t,r)=>{"use strict";r.d(t,{e:()=>b,a:()=>E});var n,o=r(269),i=r(4276),s=r(6492),a=r(6182),c=r(991),u=r(9147),l=["push","pop","shift","unshift","splice"],p=function(e,t,r,n){e&&e[a.ih](3,108,"".concat(r," [").concat(t,"] failed - ")+(0,o.mmD)(n))};function d(e,t){var r=(0,o.kgX)(e,t);return r&&r.get}function f(e,t,r,n){if(t){var i=d(t,r);i&&i[e.prop]?t[r]=n:function(e,t,r,n){var i={n:r,h:[],trk:function(t){t&&t.fn&&(-1===(0,o.rDm)(i.h,t)&&i.h[a.y5](t),e.trk(t,i))},clr:function(e){var t=(0,o.rDm)(i.h,e);-1!==t&&i.h[a.Ic](t,1)}},c=!0,l=!1;function g(){c&&(l=l||(0,u.hF)(g,e,n),n&&!n[u.nM]&&l&&(n=m(e,n,r,"Converting")),c=!1);var t=e.act;return t&&i.trk(t),n}g[e.prop]={chng:function(){e.add(i)}},(0,o.vF1)(t,i.n,{g,s:function(h){if(n!==h){g[e.ro]&&!e.upd&&(0,u.If)("["+r+"] is read-only:"+(0,o.mmD)(t)),c&&(l=l||(0,u.hF)(g,e,n),c=!1);var v=l&&g[e.rf];if(l)if(v){(0,o.zav)(n,function(e){n[e]=h?h[e]:s.HP});try{(0,o.zav)(h,function(t,r){f(e,n,t,r)}),h=n}catch(t){p((e.hdlr||{})[a.Uw],r,"Assigning",t),l=!1}}else n&&n[u.nM]&&(0,o.zav)(n,function(t){var r=d(n,t);if(r){var o=r[e.prop];o&&o.chng()}});if(h!==n){var y=h&&(0,u.hF)(g,e,h);!v&&y&&(h=m(e,h,r,"Converting")),n=h,l=y}e.add(i)}}})}(e,t,r,n)}return t}function g(e,t,r,n){if(t){var o=d(t,r),i=o&&!!o[e.prop],s=n&&n[0],c=n&&n[1],l=n&&n[2];if(!i){if(l)try{(0,u.V9)(t)}catch(t){p((e.hdlr||{})[a.Uw],r,"Blocking",t)}try{f(e,t,r,t[r]),o=d(t,r)}catch(t){p((e.hdlr||{})[a.Uw],r,"State",t)}}s&&(o[e.rf]=s),c&&(o[e.ro]=c),l&&(o[e.blkVal]=!0)}return t}function m(e,t,r,n){try{(0,o.zav)(t,function(r,n){f(e,t,r,n)}),t[u.nM]||((0,o.UxO)(t,u.nM,{get:function(){return e[a.K0]}}),function(e,t,r){(0,o.cyL)(t)&&(0,o.Iuo)(l,function(n){var o=t[n];t[n]=function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var s=o[a.y9](this,n);return m(e,t,r,"Patching"),s}})}(e,t,r))}catch(t){p((e.hdlr||{})[a.Uw],r,n,t)}return t}var h="[[ai_",v="]]";function y(e,t,r){var s,l=(0,u.QA)(t);if(l)return l;var d,y=(0,i.Z)("dyncfg",!0),b=t&&!1!==r?t:(0,u.Dy)(t),E=((s={uid:null,cfg:b})[a.Uw]=e,s[a.zs]=function(){d[a.zs]()},s.set=function(t,r,n){try{t=f(d,t,r,n)}catch(t){p(e,r,"Setting value",t)}return t[r]},s[a.h0]=function(e,t){return t&&(0,o.zav)(t,function(t,r){(0,c.q)(E,e,t,r)}),e},s[a.x6]=function(e){return function(e,t){var r={fn:t,rm:function(){r.fn=null,e=null,t=null}};return(0,o.vF1)(r,"toJSON",{v:function(){return"WatcherHandler"+(r.fn?"":"[X]")}}),e.use(r,t),r}(d,e)},s.ref=function(e,t){var r;return g(d,e,t,(r={},r[0]=!0,r))[t]},s[a.XW]=function(e,t){var r;return g(d,e,t,(r={},r[1]=!0,r))[t]},s[a.JQ]=function(e,t){var r;return g(d,e,t,(r={},r[2]=!0,r))[t]},s._block=function(e,t){d.use(null,function(r){var n=d.upd;try{(0,o.b07)(t)||(d.upd=t),e(r)}finally{d.upd=n}})},s);return(0,o.vF1)(E,"uid",{c:!1,e:!1,w:!1,v:y}),m(d=function(e){var t,r,i=(0,o.jjc)(h+"get"+e.uid+v),s=(0,o.jjc)(h+"ro"+e.uid+v),c=(0,o.jjc)(h+"rf"+e.uid+v),u=(0,o.jjc)(h+"blkVal"+e.uid+v),l=(0,o.jjc)(h+"dtl"+e.uid+v),p=null,d=null;function f(t,n){var i=r.act;try{r.act=t,t&&t[l]&&((0,o.Iuo)(t[l],function(e){e.clr(t)}),t[l]=[]),n({cfg:e.cfg,set:e.set.bind(e),setDf:e[a.h0].bind(e),ref:e.ref.bind(e),rdOnly:e[a.XW].bind(e)})}catch(t){var s=e[a.Uw];throw s&&s[a.ih](1,107,(0,o.mmD)(t)),t}finally{r.act=i||null}}function g(){if(p){var e=p;p=null,d&&d[a._w](),d=null;var t=[];if((0,o.Iuo)(e,function(e){if(e&&(e[l]&&((0,o.Iuo)(e[l],function(t){t.clr(e)}),e[l]=null),e.fn))try{f(e,e.fn)}catch(e){t[a.y5](e)}}),p)try{g()}catch(e){t[a.y5](e)}t[a.oI]>0&&function(e,t){n||(n=(0,o.aqQ)("AggregationError",function(e,t){t[a.oI]>1&&(e.errors=t[1])}));var r="Watcher error(s): ";throw(0,o.Iuo)(t,function(e,t){r+="\n".concat(t," > ").concat((0,o.mmD)(e))}),new n(r,t||[])}(0,t)}}return(t={prop:i,ro:s,rf:c})[a.JQ]=u,t[a.K0]=e,t.add=function(e){if(e&&e.h[a.oI]>0){p||(p=[]),d||(d=(0,o.dRz)(function(){d=null,g()},0));for(var t=0;t<e.h[a.oI];t++){var r=e.h[t];r&&-1===(0,o.rDm)(p,r)&&p[a.y5](r)}}},t[a.zs]=g,t.use=f,t.trk=function(e,t){if(e){var r=e[l]=e[l]||[];-1===(0,o.rDm)(r,t)&&r[a.y5](t)}},r=t}(E),b,"config","Creating"),E}function b(e,t,r,n){var o=y(r,e||{},n);return t&&o[a.h0](o.cfg,t),o}function E(e,t,r){var n=e[u.nM]||e;return!n.cfg||n.cfg!==e&&n.cfg[u.nM]!==n?(function(e,t){e?(e[a.on](t),e[a.ih](2,108,t)):(0,u.If)(t)}(r,s.xW+(0,o.mmD)(e)),b(e,null,r)[a.x6](t)):n[a.x6](t)}},9762:(e,t,r)=>{"use strict";r.d(t,{O:()=>n});var n=(0,r(4282).H)({Verbose:0,Information:1,Warning:2,Error:3,Critical:4})},9882:(e,t,r)=>{"use strict";r.d(t,{aq:()=>s,cL:()=>a});var n=r(269),o=r(6492),i=r(6535);function s(){var e=a();return(0,n.P0f)(e,0,8)+"-"+(0,n.P0f)(e,8,12)+"-"+(0,n.P0f)(e,12,16)+"-"+(0,n.P0f)(e,16,20)+"-"+(0,n.P0f)(e,20)}function a(){for(var e,t=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],r=o.m5,s=0;s<4;s++)r+=t[15&(e=(0,i.VN)())]+t[e>>4&15]+t[e>>8&15]+t[e>>12&15]+t[e>>16&15]+t[e>>20&15]+t[e>>24&15]+t[e>>28&15];var a=t[8+(3&(0,i.VN)())|0];return(0,n.hKY)(r,0,8)+(0,n.hKY)(r,9,4)+"4"+(0,n.hKY)(r,13,3)+a+(0,n.hKY)(r,16,3)+(0,n.hKY)(r,19,12)}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n=r(5256),o=exports;for(var i in n)o[i]=n[i];n.__esModule&&Object.defineProperty(o,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/4cefd8e163dc8255316b7bc1c43103ac9eaeba8e/extensions/github/dist/extension.js.map