#!/usr/bin/env python3
"""
控件识别超时监控器
通过UDP socket接收auto_recording_manager.py的控件识别开始/结束信号
当超过3秒没有收到结束信号时，杀死并重启auto_recording_manager进程
"""

import sys
import os
import time
import json
import socket
import threading
import signal
import subprocess
from typing import Dict, Any, Optional
import traceback

class WidgetRecognitionMonitor:
    """
    控件识别超时监控器
    监听控件识别开始/结束信号，超时时杀死并重启进程
    """

    def __init__(self, timeout_threshold: float = 3.0, debug: bool = False, listen_port: int = 12345):
        self.timeout_threshold = timeout_threshold  # 超时阈值（秒）
        self.debug = debug
        self.running = False

        # 网络通信
        self.socket = None
        self.listen_port = listen_port  # 监听端口

        # 监控状态
        self.active_recognitions = {}  # 活跃的控件识别任务 {session_id: {start_time, x, y, type}}
        self.timeout_timers = {}  # 超时计时器 {session_id: timer}
        self.lock = threading.Lock()

        # 统计信息
        self.total_recognitions = 0
        self.timeout_count = 0
        self.restart_count = 0

        # 进程管理
        self.target_process = None  # 被监控的auto_recording_manager进程
        self.target_process_args = []  # 进程启动参数

    def start(self, target_process_args: list):
        """启动监控器"""
        self.target_process_args = target_process_args

        try:
            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind(('127.0.0.1', self.listen_port))
            self.socket.settimeout(1.0)  # 1秒超时，用于检查running状态

            self.running = True

            # 启动目标进程
            self._start_target_process()

            # 启动监听线程
            self.listen_thread = threading.Thread(target=self._listen_loop, daemon=True)
            self.listen_thread.start()

            print(f"[MONITOR] 🎯 控件识别监控器已启动", file=sys.stderr)
            print(f"[MONITOR] 📊 监听端口: {self.listen_port}, 超时阈值: {self.timeout_threshold}秒", file=sys.stderr)

            return True

        except Exception as e:
            print(f"[ERROR] 启动监控器失败: {e}", file=sys.stderr)
            self.running = False
            return False

    def stop(self):
        """停止监控器"""
        self.running = False

        # 清理所有超时计时器
        with self.lock:
            for timer in self.timeout_timers.values():
                timer.cancel()
            self.timeout_timers.clear()
            self.active_recognitions.clear()

        # 关闭socket
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None

        # 停止目标进程
        self._stop_target_process()

        print(f"[MONITOR] 🛑 控件识别监控器已停止", file=sys.stderr)

    def _start_target_process(self):
        """启动目标进程"""
        try:
            print(f"[MONITOR] 🚀 启动目标进程: {' '.join(self.target_process_args)}", file=sys.stderr)
            self.target_process = subprocess.Popen(
                self.target_process_args,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            print(f"[MONITOR] ✅ 目标进程已启动，PID: {self.target_process.pid}", file=sys.stderr)

        except Exception as e:
            print(f"[ERROR] 启动目标进程失败: {e}", file=sys.stderr)
            self.target_process = None

    def _stop_target_process(self):
        """停止目标进程"""
        if self.target_process:
            try:
                print(f"[MONITOR] 🛑 停止目标进程，PID: {self.target_process.pid}", file=sys.stderr)
                self.target_process.terminate()

                # 等待进程结束
                try:
                    self.target_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    print(f"[MONITOR] ⚡ 强制杀死目标进程", file=sys.stderr)
                    self.target_process.kill()
                    self.target_process.wait()

                print(f"[MONITOR] ✅ 目标进程已停止", file=sys.stderr)

            except Exception as e:
                print(f"[ERROR] 停止目标进程失败: {e}", file=sys.stderr)
            finally:
                self.target_process = None

    def _restart_target_process(self):
        """重启目标进程"""
        print(f"[MONITOR] 🔄 重启目标进程...", file=sys.stderr)

        # 停止当前进程
        self._stop_target_process()

        # 等待一下
        time.sleep(1)

        # 启动新进程
        self._start_target_process()

        self.restart_count += 1
        print(f"[MONITOR] ✅ 目标进程重启完成 (第{self.restart_count}次)", file=sys.stderr)

    def _listen_loop(self):
        """监听循环"""
        print(f"[MONITOR] 🎧 开始监听循环，端口: {self.listen_port}", file=sys.stderr)

        while self.running:
            try:
                # 接收数据
                data, addr = self.socket.recvfrom(4096)
                message_json = data.decode('utf-8')
                message = json.loads(message_json)

                print(f"[MONITOR] 📨 收到消息: {message.get('signal_type')} 来自 {addr}", file=sys.stderr)

                # 处理消息
                self._handle_message(message)

            except socket.timeout:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                if self.running:
                    print(f"[ERROR] 监听循环异常: {e}", file=sys.stderr)
                    if self.debug:
                        traceback.print_exc(file=sys.stderr)

    def _handle_message(self, message: Dict[str, Any]):
        """处理接收到的消息"""
        try:
            signal_type = message.get('signal_type')
            session_id = message.get('session_id')
            timestamp = message.get('timestamp', time.time())
            data = message.get('data', {})

            if self.debug:
                print(f"[DEBUG] 收到信号: {signal_type}, 会话: {session_id}", file=sys.stderr)

            if signal_type == 'recognition_start':
                self._handle_recognition_start(session_id, timestamp, data)
            elif signal_type == 'recognition_end':
                self._handle_recognition_end(session_id, timestamp, data)
            elif signal_type == 'heartbeat':
                self._handle_heartbeat(session_id, timestamp)

        except Exception as e:
            print(f"[ERROR] 处理消息失败: {e}", file=sys.stderr)
            if self.debug:
                traceback.print_exc(file=sys.stderr)

    def _handle_recognition_start(self, session_id: str, timestamp: float, data: Dict[str, Any]):
        """处理控件识别开始信号"""
        with self.lock:
            # 记录识别任务
            self.active_recognitions[session_id] = {
                'start_time': timestamp,
                'x': data.get('x', 0),
                'y': data.get('y', 0),
                'recognition_type': data.get('recognition_type', 'unknown')
            }

            # 启动超时计时器
            timer = threading.Timer(
                self.timeout_threshold,
                self._handle_recognition_timeout,
                args=(session_id,)
            )
            timer.start()
            self.timeout_timers[session_id] = timer

            self.total_recognitions += 1

        if self.debug:
            print(f"[DEBUG] 🎯 控件识别开始: 会话={session_id}, 位置=({data.get('x')}, {data.get('y')})", file=sys.stderr)

    def _handle_recognition_end(self, session_id: str, timestamp: float, data: Dict[str, Any]):
        """处理控件识别结束信号"""
        with self.lock:
            # 取消超时计时器
            if session_id in self.timeout_timers:
                self.timeout_timers[session_id].cancel()
                del self.timeout_timers[session_id]

            # 移除识别任务
            if session_id in self.active_recognitions:
                recognition = self.active_recognitions[session_id]
                duration = data.get('duration', timestamp - recognition['start_time'])
                success = data.get('success', False)

                del self.active_recognitions[session_id]

                if self.debug:
                    print(f"[DEBUG] ✅ 控件识别结束: 会话={session_id}, 耗时={duration:.3f}秒, 成功={success}", file=sys.stderr)

    def _handle_recognition_timeout(self, session_id: str):
        """处理控件识别超时"""
        with self.lock:
            if session_id in self.active_recognitions:
                recognition = self.active_recognitions[session_id]
                elapsed = time.time() - recognition['start_time']

                print(f"[MONITOR] 🚨 控件识别超时!", file=sys.stderr)
                print(f"[MONITOR] 🚨 会话: {session_id}", file=sys.stderr)
                print(f"[MONITOR] 🚨 位置: ({recognition['x']}, {recognition['y']})", file=sys.stderr)
                print(f"[MONITOR] 🚨 类型: {recognition['recognition_type']}", file=sys.stderr)
                print(f"[MONITOR] 🚨 耗时: {elapsed:.3f}秒 (阈值: {self.timeout_threshold}秒)", file=sys.stderr)

                # 清理超时任务
                del self.active_recognitions[session_id]
                if session_id in self.timeout_timers:
                    del self.timeout_timers[session_id]

                self.timeout_count += 1

        # 重启目标进程
        print(f"[MONITOR] 🔄 触发进程重启...", file=sys.stderr)
        self._restart_target_process()

    def _handle_heartbeat(self, session_id: str, timestamp: float):
        """处理心跳信号"""
        if self.debug:
            print(f"[DEBUG] 💓 收到心跳: {session_id}", file=sys.stderr)

    def get_statistics(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        with self.lock:
            return {
                'running': self.running,
                'total_recognitions': self.total_recognitions,
                'timeout_count': self.timeout_count,
                'restart_count': self.restart_count,
                'active_recognitions': len(self.active_recognitions),
                'timeout_threshold': self.timeout_threshold,
                'target_process_pid': self.target_process.pid if self.target_process else None
            }

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='控件识别超时监控器')
    parser.add_argument('--timeout', type=float, default=3.0, help='超时阈值（秒），默认3.0')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('target_script', help='要监控的目标脚本路径')
    parser.add_argument('target_args', nargs='*', help='目标脚本的参数')

    args = parser.parse_args()

    # 构建目标进程参数
    target_process_args = ['python3', args.target_script] + args.target_args

    # 创建监控器
    monitor = WidgetRecognitionMonitor(
        timeout_threshold=args.timeout,
        debug=args.debug
    )

    # 信号处理
    def signal_handler(signum, frame):
        print(f"\n[MAIN] 🛑 收到信号 {signum}，正在退出...", file=sys.stderr)
        monitor.stop()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 启动监控器
        if monitor.start(target_process_args):
            print(f"[MAIN] 🎯 监控器已启动，监控目标: {args.target_script}", file=sys.stderr)
            print(f"[MAIN] 📊 超时阈值: {args.timeout}秒", file=sys.stderr)
            print(f"[MAIN] 📊 按Ctrl+C退出", file=sys.stderr)

            # 主循环 - 定期报告状态
            last_status_time = 0
            status_interval = 30  # 30秒报告一次

            while monitor.running:
                current_time = time.time()

                # 定期输出状态
                if current_time - last_status_time >= status_interval:
                    stats = monitor.get_statistics()
                    print(f"[MAIN] 📈 状态报告:", file=sys.stderr)
                    print(f"   总识别次数: {stats['total_recognitions']}", file=sys.stderr)
                    print(f"   超时次数: {stats['timeout_count']}", file=sys.stderr)
                    print(f"   重启次数: {stats['restart_count']}", file=sys.stderr)
                    print(f"   活跃识别任务: {stats['active_recognitions']}", file=sys.stderr)
                    print(f"   目标进程PID: {stats['target_process_pid']}", file=sys.stderr)
                    print(f"[MAIN] " + "─" * 60, file=sys.stderr)
                    last_status_time = current_time

                time.sleep(1)
        else:
            print(f"[ERROR] 监控器启动失败", file=sys.stderr)
            sys.exit(1)

    except KeyboardInterrupt:
        print(f"\n[MAIN] 🛑 用户中断，正在退出...", file=sys.stderr)
    except Exception as e:
        print(f"[ERROR] 主程序异常: {e}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
    finally:
        monitor.stop()
        print(f"[MAIN] 👋 程序已退出", file=sys.stderr)

if __name__ == "__main__":
    main()
