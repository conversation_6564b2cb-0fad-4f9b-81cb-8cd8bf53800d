#!/usr/bin/env python3
"""
测试鼠标悬停监控功能
"""

import sys
import os
import time
import threading

# 添加scripts目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入悬停监控器
try:
    from start_auto_recording_with_lifecycle import HoverTimeoutMonitor, PYNPUT_AVAILABLE
except ImportError as e:
    print(f"[ERROR] 无法导入悬停监控器: {e}", file=sys.stderr)
    sys.exit(1)


class MockLifecycleManager:
    """模拟生命周期管理器"""
    
    def __init__(self):
        self.restart_count = 0
        
    def _schedule_restart(self):
        """模拟重启调度"""
        self.restart_count += 1
        print(f"[MOCK] 🔄 模拟重启被触发 (第{self.restart_count}次)", file=sys.stderr)


def main():
    """主函数"""
    if not PYNPUT_AVAILABLE:
        print("[ERROR] pynput不可用，无法测试悬停监控", file=sys.stderr)
        sys.exit(1)
        
    print("=" * 60, file=sys.stderr)
    print("🧪 鼠标悬停监控测试", file=sys.stderr)
    print("=" * 60, file=sys.stderr)
    
    # 创建模拟生命周期管理器
    mock_manager = MockLifecycleManager()
    
    # 创建悬停监控器
    hover_monitor = HoverTimeoutMonitor(
        lifecycle_manager=mock_manager,
        hover_threshold=0.5,  # 0.5秒悬停阈值
        timeout_threshold=2.0,  # 2秒超时阈值
        debug=True
    )
    
    try:
        # 启动悬停监控
        if hover_monitor.start():
            print("[TEST] ✅ 悬停监控启动成功", file=sys.stderr)
            print("[TEST] 📝 请移动鼠标并悬停测试功能", file=sys.stderr)
            print("[TEST] 📝 悬停0.5秒后，如果2秒内没有其他操作，将触发模拟重启", file=sys.stderr)
            print("[TEST] 📝 按Ctrl+C退出测试", file=sys.stderr)
            print("=" * 60, file=sys.stderr)
            
            # 主循环 - 定期输出统计信息
            last_stats_time = 0
            stats_interval = 5  # 每5秒输出一次统计
            
            while True:
                current_time = time.time()
                
                # 定期输出统计信息
                if current_time - last_stats_time >= stats_interval:
                    stats = hover_monitor.get_statistics()
                    print(f"[STATS] 📊 统计信息:", file=sys.stderr)
                    print(f"   运行状态: {'运行中' if stats['running'] else '已停止'}", file=sys.stderr)
                    print(f"   悬停检测次数: {stats['hover_count']}", file=sys.stderr)
                    print(f"   悬停超时次数: {stats['timeout_count']}", file=sys.stderr)
                    print(f"   触发重启次数: {stats['restart_triggered_count']}", file=sys.stderr)
                    print(f"   模拟管理器重启次数: {mock_manager.restart_count}", file=sys.stderr)
                    print("─" * 40, file=sys.stderr)
                    last_stats_time = current_time
                    
                time.sleep(1)
                
        else:
            print("[ERROR] 悬停监控启动失败", file=sys.stderr)
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n[TEST] 🛑 用户中断，正在退出...", file=sys.stderr)
    except Exception as e:
        print(f"[ERROR] 测试过程中发生错误: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
    finally:
        # 停止悬停监控
        hover_monitor.stop()
        
        # 输出最终统计
        final_stats = hover_monitor.get_statistics()
        print(f"[TEST] 📊 最终统计:", file=sys.stderr)
        print(f"   悬停检测次数: {final_stats['hover_count']}", file=sys.stderr)
        print(f"   悬停超时次数: {final_stats['timeout_count']}", file=sys.stderr)
        print(f"   触发重启次数: {final_stats['restart_triggered_count']}", file=sys.stderr)
        print(f"   模拟管理器重启次数: {mock_manager.restart_count}", file=sys.stderr)
        print(f"[TEST] 👋 测试结束", file=sys.stderr)


if __name__ == "__main__":
    main()
