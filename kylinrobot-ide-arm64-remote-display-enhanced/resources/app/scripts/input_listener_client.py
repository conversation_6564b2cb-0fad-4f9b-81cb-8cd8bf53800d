#!/usr/bin/env python3

import socket, os,time,threading,subprocess,signal
from libinput.constant import ButtonState,KeyState
from libinput.evcodes import Button,Key
import errno,json
import enum

debug = False


def print_debug(msg):
    import datetime
    if debug:
        now = datetime.datetime.now()
        print(f"[{now.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}]:{msg}")

class InputListener:
    _instance = None
    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self,sudo_password="",on_click=None,\
        on_move=None,on_scroll=None,on_key_press=None,on_key_release=None,on_float_stop=None):
        self._password = sudo_password
        self._on_click = on_click
        self._on_move = on_move
        self._on_scroll = on_scroll
        self._on_key_press = on_key_press
        self._on_key_release = on_key_release

        self.listerner_thread = None
        self.rec_thread = None
        self.client = None
        os.setpgrp()
        pgid=os.getpgid(0)
        signal.signal(signal.SIGINT, self.kill_process)
        signal.signal(signal.SIGTERM, self.kill_process)

    def kill_process(self,signum, frame):
        self.stop_listen()
        os._exit(0)

    # via cmd xdotool getmouselocation get mouse position
    def get_mouse_position(self):
        x,y=-1,-1
        output = subprocess.check_output(["xdotool", "getmouselocation"]).decode()
        parts = output.strip().split(" ")
        x = int(parts[0].replace("x:", ""))
        y = int(parts[1].replace("y:", ""))
        return x, y

    def listen_events(self):
        buffer=b""
        while True:
            try:
                data = self.client.recv(1024)
                if not data:
                    time.sleep(0.05)
                    pass
                else:
                    buffer += data
                    print_debug(f"收到数据: {data}")
                    while b'\n' in buffer:
                        line,buffer = buffer.split(b'\n',1)
                        event_dict = json.loads(line.decode())
                        self.handle_events(event_dict)
            except BlockingIOError as e:
                if e.errno != errno.EAGAIN and e.errno != errno.EWOULDBLOCK:
                    raise
                else:
                    time.sleep(0.05)
                # 套接字没有数据可读，可以执行其他操作或稍后重试
                # print("套接字当前没有数据可读，稍后再试。")
                # 可以选择在这里添加一个短暂的延迟，然后重试
                # time.sleep(0.1)
            #if socket disconnected, then exit
            if self.client.fileno() == -1:
                print("套接字已断开连接")
                exit(0)

    def start_listen(self):
        if self.listerner_thread is None:
            # 检查server是否已在运行
            check_server = subprocess.run(["pgrep", "-f", "input_listener_server.py"], capture_output=True)
            if check_server.returncode == 0:
                print("Server已在运行")
            else:
                print("启动Server...")
                import sys
                # 获取当前Python解释器路径，并将其作为参数传递给python3命令
                venv_python = sys.executable
                print_debug(f"venv_python: {venv_python}")

                # 获取input_listener_server.py的正确路径
                server_script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "input_listener_server.py")
                print_debug(f"server_script_path: {server_script_path}")

                # print(f"password: {self._password}")
                if os.getuid() == 0:
                    # root user did not need sudo
                    self.listerner_thread = subprocess.Popen(
                        [venv_python, server_script_path],
                        stdin=subprocess.PIPE,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )
                else:
                    # start input_listener_server.py with sudo
                    self.listerner_thread = subprocess.Popen(
                        ["sudo", "-S", venv_python, server_script_path],
                        stdin=subprocess.PIPE,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )
                    self.listerner_thread.stdin.write(self._password+"\n")
                    self.listerner_thread.stdin.flush()

            print("尝试连接到socket...")
            # ss -x -a | grep input-listener.sock
            try_count = 0
            while try_count < 5:
                try_count += 1
                check_server = subprocess.run(["ss", "-x", "-a"], capture_output=True)
                if b"/tmp/input-listener.sock" in check_server.stdout:
                    break
                else:
                    time.sleep(0.5)
            if try_count >= 5:
                print("无法找到输入监听服务器进程")
                return False
            self.client = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            try:
                self.client.connect("/tmp/input-listener.sock")
                print("成功连接到socket")
                self.client.setblocking(False)
            except Exception as e:
                print(f"连接socket失败: {str(e)}")
                raise

            #create a new thread to listen the input events
            self.rec_thread = threading.Thread(target=self.listen_events,args=())
            self.rec_thread.start()
            return True



    def stop_listen(self):
        if self.rec_thread is not None:
            self.rec_thread.join(timeout=1.0)
            self.rec_thread = None
        #whether the client socket is connected or not
        if self.client is not None and self.client.fileno() != -1:
            # self.client.send(b"exit")
            self.client.shutdown(socket.SHUT_RDWR)
            self.client.close()
            self.client = None
        if self.listerner_thread is not None:
            # self.listerner_thread.kill()
            self.listerner_thread = None

    #handle the input events
    def handle_events(self,event):
        print_debug(f"监听到事件类型: {event['event_type']}")
        if event["event_type"] == "POINTER_BUTTON":
            if self._on_click:
                x,y=self.get_mouse_position()
                self._on_click(x,y,Button(event["button"]), \
                True if event["button_state"] == ButtonState.PRESSED.value else False)
        elif event["event_type"] == "KEYBOARD_KEY":
            if event["key_state"] == KeyState.PRESSED.value:
                if self._on_key_press:
                    self._on_key_press(Key(event["key"]))
            else:
                if self._on_key_release:
                    self._on_key_release(Key(event["key"]))
        elif event["event_type"] == "POINTER_AXIS":
            if self._on_scroll:
                x,y=self.get_mouse_position()
                self._on_scroll(x,y,0,int(event["axis_value"]))
        elif event["event_type"] == "POINTER_MOTION":
            if self._on_move:
                x,y=self.get_mouse_position()
                self._on_move(x,y)





def print_event(event):
    print(f"接收到事件: {event}")

if __name__ == "__main__":
    #获取命令行参数
    import sys
    password = ""

    if len(sys.argv) == 2 and sys.argv[1] != "--debug":
        password = sys.argv[1]
    elif len(sys.argv) == 3 and sys.argv[2] == "--debug":
        debug = True
    else:
        print("请输入密码")
        exit()
    listener = InputListener(
        sudo_password=password,
    )
    listener.start_listen()
    try:
        while True:
            # time.sleep(1)
            #if socket disconnected, then exit
            if listener.client is None \
                or listener.client.fileno() == -1:
                print("Socket断开连接")
                break
    except KeyboardInterrupt:
        listener.stop_listen()
        exit()

