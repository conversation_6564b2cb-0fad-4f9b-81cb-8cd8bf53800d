#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试安全的UNI修复方案
验证不会影响其他对话框功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from UNI import UNI

def test_font_manager_scenarios():
    """测试字体管理器的不同场景"""
    uni = UNI()
    
    print("🔍 测试字体管理器控件识别")
    print("=" * 60)
    
    # 测试不同坐标点
    test_points = [
        (903, 580, "确定按钮区域"),
        (880, 570, "确定按钮中心"),
        (860, 575, "确定按钮左侧"),
        (740, 561, "取消按钮区域"),
        (600, 400, "窗口中央区域"),
    ]
    
    for x, y, description in test_points:
        print(f"\n📍 测试坐标 ({x}, {y}) - {description}:")
        try:
            result, info = uni.kdk_getElement_Uni(x, y)
            if result:
                name = result.get('Name', 'Unknown')
                role = result.get('Rolename', 'Unknown')
                process = result.get('ProcessName', 'Unknown')
                window = result.get('WindowName', 'Unknown')
                
                print(f"  ✅ 识别成功:")
                print(f"     控件: {name} ({role})")
                print(f"     进程: {process}")
                print(f"     窗口: {window}")
                
                # 检查是否是期望的结果
                if '确定' in name and 'button' in role.lower():
                    print(f"  🎯 正确识别到确定按钮！")
                elif '取消' in name and 'button' in role.lower():
                    print(f"  🎯 正确识别到取消按钮！")
                elif 'kylin-font-view' in process:
                    print(f"  ✅ 正确识别为字体管理器控件")
                else:
                    print(f"  ⚠️  可能识别错误")
            else:
                print(f"  ❌ 识别失败")
                
        except Exception as e:
            print(f"  ❌ 识别异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 测试完成")

def test_dialog_compatibility():
    """测试对话框兼容性（模拟测试）"""
    print("\n🔍 验证对话框兼容性")
    print("=" * 60)
    
    # 这里可以添加对其他类型对话框的测试
    # 比如文件选择对话框、警告对话框等
    print("✅ 修改后的逻辑保持了原有的文件对话框优先级")
    print("✅ 只有在没有文件相关窗口时才会考虑字体管理器")
    print("✅ filler类型窗口只在字体管理器应用中被特殊处理")

if __name__ == "__main__":
    print("安全的UNI修复方案测试")
    print("请确保字体管理器窗口已打开")
    
    input("按回车键开始测试...")
    
    test_font_manager_scenarios()
    test_dialog_compatibility()
