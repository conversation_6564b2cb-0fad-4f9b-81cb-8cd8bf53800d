{"nameShort": "kylinrobot-ide", "nameLong": "KylinRobot-IDE", "applicationName": "kylinrobot-ide", "linuxIconName": "kylinrobot-ide", "urlProtocol": "kylinrobot-ide", "dataFolderName": ".kylinrobot-ide", "version": "1.102.1", "licenseFileName": "LICENSE.txt", "tunnelApplicationName": "code-tunnel-oss", "builtInExtensions": [], "webBuiltInExtensions": [], "extensionEnabledApiProposals": {}, "target": {"arch": "arm64", "platform": "linux"}, "extensionsGallery": {"serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery", "cacheUrl": "https://vscode.blob.core.windows.net/gallery/index", "itemUrl": "https://marketplace.visualstudio.com/items"}, "commit": "4cefd8e163dc8255316b7bc1c43103ac9eaeba8e", "date": "2025-07-30T09:41:42.456Z", "checksums": {"vs/base/parts/sandbox/electron-sandbox/preload.js": "CoCiDHizcpGq+e6c/ODN5jmnP+7kP8eKSES2KAVZ3q8", "vs/workbench/workbench.desktop.main.js": "RQzKNyy6RNM6HX9fRllh6baHHH8kGRk5F6S04bp9SzI", "vs/workbench/workbench.desktop.main.css": "bP36gTMZPONVySHa9VwPt1gOrLFXfcQxsDz2cMiJBCQ", "vs/workbench/api/node/extensionHostProcess.js": "YORahLAUhRJ0tOKETSZvZ7n3T1cueTrA6gjky4jc0MU", "vs/code/electron-sandbox/workbench/workbench.html": "jyZC3vdSpB6QNN6FM7WlZ0VlH7eLSiUtA5UedJy3GA0", "vs/code/electron-sandbox/workbench/workbench.js": "BojF2aDvxaetE2piX9mfwAMfwTab9OocFmHPU9iyID8"}}